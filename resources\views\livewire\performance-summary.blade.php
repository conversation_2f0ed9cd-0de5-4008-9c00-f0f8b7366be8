@push('styles')
<link rel="stylesheet" href="{{ asset('css/task-summary.css') }}">
@endpush

<div>
    <!-- Performance Summary Header replacing the empty purple area -->
    <div class="page-header min-height-300 border-radius-xl mt-4" 
         style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row h-100 align-items-center">
                <div class="col-12">
                    <div class="p-4">
                        <!-- Header Section -->
                        <div class="row align-items-center mb-4">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <div class="icon icon-lg icon-shape bg-white shadow text-center border-radius-xl me-3">
                                        <i class="material-icons opacity-10 text-primary">assessment</i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0 text-white">Performance Management</h5>
                                        <p class="text-white-50 mb-0">Monitor and manage team performance reports</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <!-- View Type Toggle -->
                                <div class="btn-group" role="group">
                                    <button wire:click="$set('viewType', 'overall')" 
                                            class="btn btn-sm {{ $viewType === 'overall' ? 'btn-light' : 'btn-outline-light' }}">
                                        <i class="fas fa-chart-bar me-1"></i> OVERALL
                                    </button>
                                    <button wire:click="$set('viewType', 'department')" 
                                            class="btn btn-sm {{ $viewType === 'department' ? 'btn-light' : 'btn-outline-light' }}">
                                        <i class="fas fa-building me-1"></i> DEPARTMENT
                                    </button>
                                    <button wire:click="$set('viewType', 'superior')" 
                                            class="btn btn-sm {{ $viewType === 'superior' ? 'btn-light' : 'btn-outline-light' }}">
                                        <i class="fas fa-users me-1"></i> SUPERIOR
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Filters Row -->
                        <div class="row mb-4">
                            <div class="col-md-2">
                                <label class="form-label text-white text-xs">Time Period</label>
                                <select wire:model.live="monthFilter" class="form-select form-select-sm">
                                    <option value="current">Current Month</option>
                                    <option value="previous">Previous Month</option>
                                    <option value="custom">Custom Range</option>
                                </select>
                            </div>
                            
                            @if($monthFilter === 'custom')
                            <div class="col-md-2">
                                <label class="form-label text-white text-xs">Start Date</label>
                                <input type="date" wire:model.live="startDate" class="form-control form-control-sm">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-white text-xs">End Date</label>
                                <input type="date" wire:model.live="endDate" class="form-control form-control-sm">
                            </div>
                            @endif        
                    
                            <div class="col-md-2">
                                <label class="form-label text-white text-xs">Status Filter</label>
                                <select wire:model.live="statusFilter" class="form-select form-select-sm">
                                    <option value="all">All Status</option>
                                    <option value="submitted">Submitted</option>
                                    <option value="pending">Pending</option>
                                    <option value="overdue">Overdue</option>
                                </select>
                            </div>
                            
                            @if($viewType !== 'superior')
                            <div class="col-md-2">
                                <label class="form-label text-white text-xs">Department</label>
                                <select wire:model.live="departmentFilter" class="form-select form-select-sm">
                                    <option value="all">All Departments</option>
                                    @foreach($departments as $department)
                                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @endif
                            
                            <div class="col-md-2">
                                <label class="form-label text-white text-xs">&nbsp;</label>
                                <button wire:click="loadSummary" class="btn btn-sm btn-light d-block">
                                    <i class="fas fa-sync-alt"></i> REFRESH
                                </button>
                            </div>
                        </div>
                        
                        <!-- Summary Content -->
                        @if($loading)
                            <!-- Loading State -->
                            <div class="text-center py-3">
                                <div class="spinner-border text-white" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-white-50 mb-0">Loading performance data...</p>
                            </div>
                        @elseif(!empty($summaryData))
                            <!-- Period Badge -->
                            <div class="mb-3">
                                <span class="badge bg-white text-primary px-3 py-2">
                                    <i class="fas fa-calendar me-2"></i> 
                                    {{ $summaryData['period']['label'] ?? 'Summary' }}
                                </span>
                            </div>        
                    
                            <!-- Overall View - Performance Summary Cards -->
                            @if($viewType === 'overall' && isset($summaryData['summary']))
                            <div class="row g-3">
                                <div class="col-xl-2 col-md-4 col-sm-6">
                                    <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                        <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                            <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">people</i>
                                        </div>
                                        <h4 class="mb-0 text-primary">{{ $summaryData['summary']['total_users'] }}</h4>
                                        <span class="text-xs text-secondary">
                                            @if(($summaryData['summary']['user_role'] ?? '') === 'nazim')
                                                Total Users
                                            @elseif(($summaryData['summary']['user_role'] ?? '') === 'superior')
                                                My Team
                                            @else
                                                My Reports
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="col-xl-2 col-md-4 col-sm-6">
                                    <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                        <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                            <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">check_circle</i>
                                        </div>
                                        <h4 class="mb-0 text-success">{{ $summaryData['summary']['submitted'] }}</h4>
                                        <span class="text-xs text-secondary">
                                            @if(($summaryData['summary']['user_role'] ?? '') === 'nazim')
                                                Total Submitted
                                            @elseif(($summaryData['summary']['user_role'] ?? '') === 'superior')
                                                Team Submitted
                                            @else
                                                My Submitted
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="col-xl-2 col-md-4 col-sm-6">
                                    <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                        <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                            <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">pending</i>
                                        </div>
                                        <h4 class="mb-0 text-warning">{{ $summaryData['summary']['pending'] }}</h4>
                                        <span class="text-xs text-secondary">
                                            @if(($summaryData['summary']['user_role'] ?? '') === 'nazim')
                                                Total Pending
                                            @elseif(($summaryData['summary']['user_role'] ?? '') === 'superior')
                                                Team Pending
                                            @else
                                                My Pending
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="col-xl-2 col-md-4 col-sm-6">
                                    <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                        <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                            <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">schedule</i>
                                        </div>
                                        <h4 class="mb-0 text-danger">{{ $summaryData['summary']['overdue'] }}</h4>
                                        <span class="text-xs text-secondary">
                                            @if(($summaryData['summary']['user_role'] ?? '') === 'nazim')
                                                Total Overdue
                                            @elseif(($summaryData['summary']['user_role'] ?? '') === 'superior')
                                                Team Overdue
                                            @else
                                                My Overdue
                                            @endif
                                        </span>
                                    </div>
                                </div>
                               
                                <div class="col-xl-2 col-md-4 col-sm-6">
                                    <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                        <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                            <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">trending_up</i>
                                        </div>
                                        <h4 class="mb-0 text-info">{{ $summaryData['summary']['submission_rate'] }}%</h4>
                                        <span class="text-xs text-secondary">
                                            @if(($summaryData['summary']['user_role'] ?? '') === 'nazim')
                                                Overall Rate
                                            @elseif(($summaryData['summary']['user_role'] ?? '') === 'superior')
                                                Team Rate
                                            @else
                                                My Rate
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="col-xl-2 col-md-4 col-sm-6">
                                    <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                        <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                            <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">access_time</i>
                                        </div>
                                        <h4 class="mb-0 text-secondary">{{ $summaryData['summary']['avg_hours'] }}h</h4>
                                        <span class="text-xs text-secondary">
                                            @if(($summaryData['summary']['user_role'] ?? '') === 'nazim')
                                                Avg Hours
                                            @elseif(($summaryData['summary']['user_role'] ?? '') === 'superior')
                                                Team Avg Hours
                                            @else
                                                My Avg Hours
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                            @endif
                            
                            <!-- Department View -->
                            @if($viewType === 'department' && isset($summaryData['departments']))
                            <div class="text-center py-3">
                                <h6 class="text-white mb-2">
                                    <i class="fas fa-building me-2"></i> 
                                    Department-wise Performance Summary
                                </h6>
                                <p class="text-white-50 mb-0">{{ count($summaryData['departments']) }} departments found</p>
                            </div>
                            @endif
                            
                            <!-- Superior View -->
                            @if($viewType === 'superior' && isset($summaryData['superiors']))
                            <div class="text-center py-3">
                                <h6 class="text-white mb-2">
                                    <i class="fas fa-users me-2"></i> 
                                    Superior-wise Performance Summary
                                </h6>
                                <p class="text-white-50 mb-0">{{ count($summaryData['superiors']) }} superiors found</p>
                            </div>
                            @endif
                        @else
                            <!-- Empty State -->
                            <div class="text-center py-3">
                                <i class="fas fa-chart-bar fa-2x text-white-50 mb-2"></i>
                                <h6 class="text-white">No Performance Data Available</h6>
                                <p class="text-white-50 mb-0">No performance reports found for the selected filters.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
 
   <!-- Department and Superior Details (Below Header) -->
    @if(!$loading && !empty($summaryData))
        <div class="container-fluid">
            <!-- Department View Details -->
            @if($viewType === 'department' && isset($summaryData['departments']))
            <div class="row mt-4 department-details">
                @foreach($summaryData['departments'] as $deptData)
                <div class="col-12 mb-4">
                    <div class="card department-card">
                        <div class="card-header pb-0">
                            <div class="d-flex align-items-center">
                                <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md me-3">
                                    <i class="material-icons opacity-10 text-white">business</i>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ $deptData['department']['name'] }}</h6>
                                    <p class="text-sm text-secondary mb-0">
                                        {{ $deptData['summary']['total_users'] }} users • {{ $deptData['summary']['submission_rate'] }}% submission rate
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Department Summary Cards -->
                            <div class="row mb-3">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-primary mb-0">{{ $deptData['summary']['total_users'] }}</h5>
                                        <span class="text-xs text-secondary">Total Users</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-success mb-0">{{ $deptData['summary']['submitted'] }}</h5>
                                        <span class="text-xs text-secondary">Submitted</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-warning mb-0">{{ $deptData['summary']['pending'] }}</h5>
                                        <span class="text-xs text-secondary">Pending</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-danger mb-0">{{ $deptData['summary']['overdue'] }}</h5>
                                        <span class="text-xs text-secondary">Overdue</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-info mb-0">{{ $deptData['summary']['avg_hours'] }}h</h5>
                                        <span class="text-xs text-secondary">Avg Hours</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-success mb-0">{{ $deptData['summary']['excellent_ratings'] }}</h5>
                                        <span class="text-xs text-secondary">Excellent</span>
                                    </div>
                                </div>
                            </div>      
                      
                            <!-- Department Users -->
                            @if(!empty($deptData['users']))
                            <div class="table-responsive">
                                <table class="table table-sm align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Reports</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Submitted</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Pending</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Overdue</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Avg Hours</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Rating</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($deptData['users'] as $userData)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm bg-gradient-{{ $userData['user']['role'] === 'Superior' ? 'success' : 'info' }} me-2">
                                                        <span class="text-white text-xs font-weight-bold">
                                                            {{ strtoupper(substr($userData['user']['name'], 0, 2)) }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm font-weight-bold mb-0">{{ $userData['user']['name'] }}</p>
                                                        <span class="badge badge-sm bg-gradient-{{ $userData['user']['role'] === 'Superior' ? 'success' : 'info' }}">
                                                            {{ $userData['user']['role'] }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center"><span class="text-sm font-weight-bold">{{ $userData['summary']['total_reports'] }}</span></td>
                                            <td class="text-center"><span class="text-sm text-success">{{ $userData['summary']['submitted'] }}</span></td>
                                            <td class="text-center"><span class="text-sm text-warning">{{ $userData['summary']['pending'] }}</span></td>
                                            <td class="text-center">
                                                @if($userData['summary']['overdue'] > 0)
                                                    <span class="badge badge-sm bg-gradient-danger">{{ $userData['summary']['overdue'] }}</span>
                                                @else
                                                    <span class="text-sm">0</span>
                                                @endif
                                            </td>
                                            <td class="text-center"><span class="text-sm">{{ $userData['summary']['avg_hours'] }}h</span></td>
                                            <td class="text-center">
                                                <span class="badge badge-sm bg-gradient-{{ $this->getRatingColor($userData['summary']['avg_rating']) }}">
                                                    {{ ucfirst($userData['summary']['avg_rating']) }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @endif    
        <!-- Superior View Details -->
            @if($viewType === 'superior' && isset($summaryData['superiors']))
            <div class="row mt-4 superior-details">
                @foreach($summaryData['superiors'] as $superiorData)
                <div class="col-12 mb-4">
                    <div class="card superior-card">
                        <div class="card-header pb-0">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-lg bg-gradient-success me-3">
                                    <span class="text-white font-weight-bold">
                                        {{ strtoupper(substr($superiorData['superior']['name'], 0, 2)) }}
                                    </span>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ $superiorData['superior']['name'] }}</h6>
                                    <p class="text-sm text-secondary mb-0">
                                        <span class="badge badge-sm bg-gradient-success">Superior</span>
                                        {{ $superiorData['superior']['email'] }}
                                    </p>
                                </div>
                                <div class="ms-auto">
                                    <div class="text-end">
                                        <h5 class="mb-0">{{ $superiorData['team_summary']['total_reports'] }}</h5>
                                        <span class="text-xs text-secondary">Team Reports</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Team Summary Cards -->
                            <div class="row mb-4">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-primary mb-0">{{ $superiorData['team_summary']['total_reports'] }}</h5>
                                        <span class="text-xs text-secondary">Team Total</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-success mb-0">{{ $superiorData['team_summary']['submitted'] }}</h5>
                                        <span class="text-xs text-secondary">Submitted</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-warning mb-0">{{ $superiorData['team_summary']['pending'] }}</h5>
                                        <span class="text-xs text-secondary">Pending</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-danger mb-0">{{ $superiorData['team_summary']['overdue'] }}</h5>
                                        <span class="text-xs text-secondary">Overdue</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-info mb-0">{{ $superiorData['team_summary']['avg_hours'] }}h</h5>
                                        <span class="text-xs text-secondary">Avg Hours</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h5 class="text-secondary mb-0">{{ count($superiorData['assistants']) }}</h5>
                                        <span class="text-xs text-secondary">Assistants</span>
                                    </div>
                                </div>
                            </div>     
                       
                            <!-- Superior's Own Performance -->
                            @if($superiorData['superior_performance']['total_reports'] > 0)
                            <div class="mb-4">
                                <h6 class="text-sm font-weight-bold mb-2">Superior's Performance</h6>
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="text-center p-2 border rounded">
                                            <h6 class="text-primary mb-0">{{ $superiorData['superior_performance']['total_reports'] }}</h6>
                                            <span class="text-xs">Reports</span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="text-center p-2 border rounded">
                                            <h6 class="text-success mb-0">{{ $superiorData['superior_performance']['submitted'] }}</h6>
                                            <span class="text-xs">Submitted</span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="text-center p-2 border rounded">
                                            <h6 class="text-warning mb-0">{{ $superiorData['superior_performance']['pending'] }}</h6>
                                            <span class="text-xs">Pending</span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="text-center p-2 border rounded">
                                            <h6 class="text-danger mb-0">{{ $superiorData['superior_performance']['overdue'] }}</h6>
                                            <span class="text-xs">Overdue</span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="text-center p-2 border rounded">
                                            <h6 class="text-info mb-0">{{ $superiorData['superior_performance']['avg_hours'] }}h</h6>
                                            <span class="text-xs">Avg Hours</span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="text-center p-2 border rounded">
                                            <h6 class="text-secondary mb-0">{{ ucfirst($superiorData['superior_performance']['avg_rating']) }}</h6>
                                            <span class="text-xs">Rating</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                            
                            <!-- Assistants Performance -->
                            @if(!empty($superiorData['assistants']))
                            <div>
                                <h6 class="text-sm font-weight-bold mb-3">Assistants Performance</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm align-items-center mb-0">
                                        <thead>
                                            <tr>
                                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Assistant</th>
                                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Reports</th>
                                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Submitted</th>
                                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Pending</th>
                                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Overdue</th>
                                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Avg Hours</th>
                                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Rating</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($superiorData['assistants'] as $assistantData)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar avatar-sm bg-gradient-info me-2">
                                                            <span class="text-white text-xs font-weight-bold">
                                                                {{ strtoupper(substr($assistantData['assistant']['name'], 0, 2)) }}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <p class="text-sm font-weight-bold mb-0">{{ $assistantData['assistant']['name'] }}</p>
                                                            <span class="badge badge-sm bg-gradient-info">Assistant</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center"><span class="text-sm font-weight-bold">{{ $assistantData['summary']['total_reports'] }}</span></td>
                                                <td class="text-center"><span class="text-sm text-success">{{ $assistantData['summary']['submitted'] }}</span></td>
                                                <td class="text-center"><span class="text-sm text-warning">{{ $assistantData['summary']['pending'] }}</span></td>
                                                <td class="text-center">
                                                    @if($assistantData['summary']['overdue'] > 0)
                                                        <span class="badge badge-sm bg-gradient-danger">{{ $assistantData['summary']['overdue'] }}</span>
                                                    @else
                                                        <span class="text-sm">0</span>
                                                    @endif
                                                </td>
                                                <td class="text-center"><span class="text-sm">{{ $assistantData['summary']['avg_hours'] }}h</span></td>
                                                <td class="text-center">
                                                    <span class="badge badge-sm bg-gradient-{{ $this->getRatingColor($assistantData['summary']['avg_rating']) }}">
                                                        {{ ucfirst($assistantData['summary']['avg_rating']) }}
                                                    </span>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @endif
        </div>
    @endif    <!-
- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation classes to cards
        const cards = document.querySelectorAll('.compact-summary-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('summary-card-animated');
            }, index * 100);
        });
        
        // Auto-refresh summary every 5 minutes
        setInterval(() => {
            if (typeof Livewire !== 'undefined') {
                Livewire.emit('loadSummary');
            }
        }, 300000); // 5 minutes
    });
    </script>
    @endpush
</div>