<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class TaskManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $showCreateModal = false;
    public $showEditModal = false;
    public $showViewModal = false;
    
    public $taskId;
    public $title = '';
    public $description = '';
    public $type = 'daily';
    public $status = 'pending';
    public $assigned_to = '';
    public $department_id = '';
    public $due_date = '';
    public $priority = 1;
    public $completion_notes = '';
    
    public $search = '';
    public $filterStatus = 'all';
    public $filterType = 'all';
    public $filterAssignedTo = 'all';
    public $filterDepartment = 'all';
    
    public $availableUsers = [];
    public $departments = [];
    public $selectedTask = null;

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'type' => 'required|in:daily,weekly,operational,one-time',
        'assigned_to' => 'required|exists:users,id',
        'department_id' => 'nullable|exists:departments,id',
        'due_date' => 'required|date|after_or_equal:today',
        'priority' => 'required|integer|min:1|max:3',
    ];

    public function mount()
    {
        $this->authorize('assign-tasks');
        $this->loadData();
        $this->due_date = Carbon::today()->format('Y-m-d');
    }

    public function render()
    {
        $tasks = Task::with(['assignedTo', 'assignedBy', 'department'])
            ->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->filterStatus !== 'all', function ($query) {
                $query->where('status', $this->filterStatus);
            })
            ->when($this->filterType !== 'all', function ($query) {
                $query->where('type', $this->filterType);
            })
            ->when($this->filterAssignedTo !== 'all', function ($query) {
                $query->where('assigned_to', $this->filterAssignedTo);
            })
            ->when($this->filterDepartment !== 'all', function ($query) {
                $query->where('department_id', $this->filterDepartment);
            })
            ->when(!auth()->user()->isNazim(), function ($query) {
                // Superior can only see tasks they assigned or in their department
                $user = auth()->user();
                if ($user->isSuperior()) {
                    $departmentIds = $user->departments->pluck('id');
                    $query->where(function ($q) use ($user, $departmentIds) {
                        $q->where('assigned_by', $user->id)
                          ->orWhereIn('department_id', $departmentIds);
                    });
                }
            })
            ->orderBy('due_date', 'asc')
            ->orderBy('priority', 'desc')
            ->paginate(10);

        return view('livewire.task-management', [
            'tasks' => $tasks,
        ]);
    }

    public function openCreateModal()
    {
        $this->authorize('create', Task::class);
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function openEditModal($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('update', $task);
        
        $this->taskId = $task->id;
        $this->title = $task->title;
        $this->description = $task->description;
        $this->type = $task->type;
        $this->status = $task->status;
        $this->assigned_to = $task->assigned_to;
        $this->department_id = $task->department_id;
        $this->due_date = $task->due_date->format('Y-m-d');
        $this->priority = $task->priority;
        $this->completion_notes = $task->completion_notes;
        
        $this->showEditModal = true;
    }

    public function openViewModal($taskId)
    {
        $this->selectedTask = Task::with(['assignedTo', 'assignedBy', 'department'])->findOrFail($taskId);
        $this->authorize('view', $this->selectedTask);
        $this->showViewModal = true;
    }

    public function createTask()
    {
        $this->authorize('create', Task::class);
        $this->validate();

        Task::create([
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'assigned_to' => $this->assigned_to,
            'assigned_by' => auth()->id(),
            'department_id' => $this->department_id ?: null,
            'due_date' => $this->due_date,
            'priority' => $this->priority,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        session()->flash('message', 'Task created successfully.');
    }

    public function updateTask()
    {
        $task = Task::findOrFail($this->taskId);
        $this->authorize('update', $task);
        $this->validate();

        $task->update([
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'status' => $this->status,
            'assigned_to' => $this->assigned_to,
            'department_id' => $this->department_id ?: null,
            'due_date' => $this->due_date,
            'priority' => $this->priority,
            'completion_notes' => $this->completion_notes,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        session()->flash('message', 'Task updated successfully.');
    }

    public function deleteTask($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('delete', $task);

        $task->delete();
        session()->flash('message', 'Task deleted successfully.');
    }

    public function markCompleted($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('complete', $task);

        $task->markCompleted();
        session()->flash('message', 'Task marked as completed.');
    }

    public function changeStatus($taskId, $status)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('update', $task);

        $task->update(['status' => $status]);
        session()->flash('message', 'Task status updated successfully.');
    }

    private function resetForm()
    {
        $this->taskId = null;
        $this->title = '';
        $this->description = '';
        $this->type = 'daily';
        $this->status = 'pending';
        $this->assigned_to = '';
        $this->department_id = '';
        $this->due_date = Carbon::today()->format('Y-m-d');
        $this->priority = 1;
        $this->completion_notes = '';
        $this->resetValidation();
    }

    private function loadData()
    {
        $user = auth()->user();
        
        if ($user->isNazim()) {
            // Nazim can assign tasks to anyone
            $this->availableUsers = User::select('id', 'name', 'email')->orderBy('name')->get();
        } elseif ($user->isSuperior()) {
            // Superior can assign tasks to their assistants and department members
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->departments->flatMap->users->pluck('id');
            $userIds = $assistantIds->merge($departmentUserIds)->unique();
            
            $this->availableUsers = User::whereIn('id', $userIds)
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();
        }
        
        $this->departments = Department::active()->select('id', 'name')->orderBy('name')->get();
    }

    public function closeModals()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showViewModal = false;
        $this->selectedTask = null;
        $this->resetForm();
    }

    public function getStatusBadgeClass($status)
    {
        return match($status) {
            'pending' => 'bg-gradient-warning',
            'in_progress' => 'bg-gradient-info',
            'completed' => 'bg-gradient-success',
            'cancelled' => 'bg-gradient-secondary',
            default => 'bg-gradient-secondary'
        };
    }

    public function getPriorityBadgeClass($priority)
    {
        return match($priority) {
            1 => 'bg-gradient-success',
            2 => 'bg-gradient-warning',
            3 => 'bg-gradient-danger',
            default => 'bg-gradient-secondary'
        };
    }

    public function getPriorityText($priority)
    {
        return match($priority) {
            1 => 'Low',
            2 => 'Medium',
            3 => 'High',
            default => 'Unknown'
        };
    }
}
