<div>


    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                Performance Holiday Management
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Manage holidays when performance submission is not required</p>
                        </div>
                        <button wire:click="openAddForm"
                                class="btn bg-gradient-primary btn-sm"
                                wire:loading.attr="disabled"
                                wire:target="openAddForm">
                            <span wire:loading.remove wire:target="openAddForm">
                                <i class="fas fa-plus me-1"></i> Add Holiday
                            </span>
                            <span wire:loading wire:target="openAddForm">
                                <i class="fas fa-spinner fa-spin me-1"></i> Loading...
                            </span>
                        </button>


                    </div>
                </div>
                <div class="card-body pt-3">
                    <!-- Filters -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <select wire:model.live="filterType" class="form-select">
                                    <option value="">All Types</option>
                                    <option value="religious">Religious</option>
                                    <option value="national">National</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <select wire:model.live="filterYear" class="form-select">
                                    <option value="">All Years</option>
                                    @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                        <option value="{{ $year }}">{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-end">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Holidays exclude performance requirements
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Form Modal -->
    @if($showAddForm)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ $editingId ? 'Edit Holiday' : 'Add New Holiday' }}</h5>
                    <button type="button" class="btn-close" wire:click="hideAddForm"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="save">
                        <div class="mb-3">
                            <label class="form-label">Holiday Date *</label>
                            <input type="date" class="form-control @error('holiday_date') is-invalid @enderror" 
                                   wire:model="holiday_date">
                            @error('holiday_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Holiday Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   wire:model="name" placeholder="e.g., Eid ul-Fitr">
                            @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Type *</label>
                            <select class="form-select @error('type') is-invalid @enderror" wire:model="type">
                                @foreach($types as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                            @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      wire:model="description" rows="3" 
                                      placeholder="Optional description about this holiday"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="hideAddForm">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="save">
                        {{ $editingId ? 'Update Holiday' : 'Add Holiday' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <select class="form-select" wire:model="filterType">
                <option value="">All Types</option>
                @foreach($types as $value => $label)
                    <option value="{{ $value }}">{{ $label }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-6">
            <select class="form-select" wire:model="filterYear">
                @for($year = date('Y') - 1; $year <= date('Y') + 2; $year++)
                    <option value="{{ $year }}">{{ $year }}</option>
                @endfor
            </select>
        </div>
    </div>

    <!-- Holidays Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Holiday</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Type</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Created By</th>
                                    <th class="text-secondary opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($holidays as $holiday)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $holiday->holiday_date->format('M d, Y') }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $holiday->holiday_date->format('l') }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $holiday->name }}</h6>
                                            @if($holiday->description)
                                                <p class="text-xs text-secondary mb-0">{{ Str::limit($holiday->description, 50) }}</p>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-sm 
                                            @if($holiday->type === 'religious') bg-gradient-success
                                            @elseif($holiday->type === 'national') bg-gradient-info
                                            @else bg-gradient-secondary
                                            @endif">
                                            {{ $types[$holiday->type] }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-sm {{ $holiday->is_active ? 'bg-gradient-success' : 'bg-gradient-secondary' }}">
                                            {{ $holiday->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $holiday->createdBy->name }}</h6>
                                            <p class="text-xs text-secondary mb-0">{{ $holiday->created_at->format('M d, Y') }}</p>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="dropdown">
                                            <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                                                <i class="fa fa-ellipsis-v text-xs"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" wire:click="edit({{ $holiday->id }})">Edit</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="toggleStatus({{ $holiday->id }})">
                                                    {{ $holiday->is_active ? 'Deactivate' : 'Activate' }}
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" 
                                                       wire:click="delete({{ $holiday->id }})"
                                                       onclick="return confirm('Are you sure you want to delete this holiday?')">Delete</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-calendar-times fa-3x text-secondary mb-3"></i>
                                            <h6 class="text-secondary">No holidays found</h6>
                                            <p class="text-sm text-secondary">Add holidays when performance submission is not required.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                
                @if($holidays->hasPages())
                <div class="card-footer">
                    {{ $holidays->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif
</div>
