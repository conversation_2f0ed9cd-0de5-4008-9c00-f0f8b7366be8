/* Task Summary Dashboard Styles */
.task-header-card {
    border: none !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
}

.task-summary-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.task-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.compact-summary-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.compact-summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}

.summary-stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.summary-stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

.department-card {
    border-left: 4px solid #17a2b8;
    transition: all 0.3s ease;
}

.department-card:hover {
    border-left-color: #007bff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.superior-card {
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
}

.superior-card:hover {
    border-left-color: #20c997;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.assistant-mini-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.assistant-mini-card:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.view-toggle-btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-toggle-btn.active {
    transform: scale(1.05);
}

.filter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section .form-label {
    color: white;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.filter-section .form-select,
.filter-section .form-control {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-period-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 1rem;
}

.loading-overlay {
    position: relative;
}

.loading-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 10;
    border-radius: 15px;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 11;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .summary-stat-number {
        font-size: 1.5rem;
    }
    
    .view-toggle-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .filter-section {
        padding: 1rem;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.summary-card-animated {
    animation: fadeInUp 0.5s ease-out;
}

/* Status color indicators */
.status-pending { color: #17a2b8; }
.status-in-progress { color: #ffc107; }
.status-completed { color: #28a745; }
.status-cancelled { color: #6c757d; }
.status-overdue { color: #dc3545; }

/* Progress bars for visual representation */
.progress-mini {
    height: 4px;
    border-radius: 2px;
    margin-top: 0.25rem;
}

.team-hierarchy {
    position: relative;
    padding-left: 2rem;
}

.team-hierarchy::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #28a745, #17a2b8);
}

.hierarchy-node {
    position: relative;
    margin-bottom: 1rem;
}

.hierarchy-node::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1rem;
    width: 12px;
    height: 2px;
    background: #dee2e6;
}

.hierarchy-superior::before {
    background: #28a745;
}

.hierarchy-assistant::before {
    background: #17a2b8;
}

/* View More Button Styles */
.view-more-button {
    transition: all 0.3s ease;
    background-color: white;
    color: #5e72e4;
    border: 2px solid rgba(255,255,255,0.8);
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.view-more-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    background-color: #f8f9fa;
    color: #5e72e4;
    border-color: rgba(255,255,255,1);
    text-decoration: none;
}

.view-more-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(94, 114, 228, 0.2);
}

.view-more-button i {
    transition: transform 0.3s ease;
}

.view-more-button:hover i {
    transform: translateX(3px);
}
