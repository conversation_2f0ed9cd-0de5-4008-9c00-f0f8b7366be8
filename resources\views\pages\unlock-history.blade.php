<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="unlock-history"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Account Lock/Unlock History"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <div class="container-fluid py-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header pb-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">
                                        <i class="fas fa-history me-2 text-primary"></i>
                                        Account Lock/Unlock History
                                    </h6>
                                    <p class="text-sm mb-0 text-secondary">View complete history of account locks and unlocks</p>
                                </div>
                                <a href="{{ route('unlock-history.stats') }}" class="btn btn-sm bg-gradient-info">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    System Statistics
                                </a>
                            </div>
                        </div>
                        <div class="card-body px-0 pt-0 pb-2">
                            <div class="table-responsive p-0">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Lock History</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Unlock Requests</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Current Status</th>
                                            <th class="text-secondary opacity-7">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($users as $user)
                                        <tr>
                                            <td>
                                                <div class="d-flex px-2 py-1">
                                                    <div class="d-flex flex-column justify-content-center">
                                                        <h6 class="mb-0 text-sm">{{ $user->name }}</h6>
                                                        <p class="text-xs text-secondary mb-0">{{ $user->email }}</p>
                                                        <div class="mt-1">
                                                            @foreach($user->roles as $role)
                                                                <span class="badge badge-sm bg-gradient-info">{{ $role->name }}</span>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column justify-content-center">
                                                    <h6 class="mb-0 text-sm">{{ $user->restrictions_count }} records</h6>
                                                    @if($user->activeRestrictions->count() > 0)
                                                        <p class="text-xs text-danger mb-0">
                                                            <i class="fas fa-lock me-1"></i>
                                                            Currently locked
                                                        </p>
                                                    @else
                                                        <p class="text-xs text-success mb-0">
                                                            <i class="fas fa-unlock me-1"></i>
                                                            Currently unlocked
                                                        </p>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column justify-content-center">
                                                    <h6 class="mb-0 text-sm">{{ $user->unlock_requests_count }} requests</h6>
                                                    @php
                                                        $pendingRequests = $user->unlockRequests()->where('status', 'pending')->count();
                                                    @endphp
                                                    @if($pendingRequests > 0)
                                                        <p class="text-xs text-warning mb-0">
                                                            <i class="fas fa-clock me-1"></i>
                                                            {{ $pendingRequests }} pending
                                                        </p>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                @if($user->activeRestrictions->count() > 0)
                                                    <span class="badge bg-gradient-danger">Locked</span>
                                                @else
                                                    <span class="badge bg-gradient-success">Unlocked</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('unlock-history.user', $user->id) }}" class="btn btn-sm bg-gradient-primary">
                                                    <i class="fas fa-history me-1"></i>
                                                    View History
                                                </a>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="text-center">
                                                    <i class="fas fa-history fa-3x text-secondary mb-3"></i>
                                                    <h6 class="text-secondary">No lock/unlock history found</h6>
                                                    <p class="text-sm text-secondary">No users have been locked or unlocked yet.</p>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                            
                            @if($users->hasPages())
                            <div class="card-footer">
                                {{ $users->links() }}
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <x-footers.auth></x-footers.auth>
</x-layout>