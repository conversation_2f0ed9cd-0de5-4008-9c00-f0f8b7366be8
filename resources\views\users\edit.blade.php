<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="user-management"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Edit User"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #7c3aed;
                --primary-hover: #6d28d9;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --danger-hover: #dc2626;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 800px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white !important;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
            }

            .page-header * {
                color: white !important;
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                color: white !important;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .page-title svg {
                color: white !important;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            }

            .page-subtitle {
                font-size: 1.1rem;
                color: white !important;
                opacity: 0.95;
                margin-top: 0.5rem;
                margin-bottom: 0;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            }

            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1rem;
                font-size: 0.875rem;
            }

            .breadcrumb a {
                color: white !important;
                text-decoration: none;
                opacity: 1;
                transition: all 0.2s;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.1);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .breadcrumb a:hover {
                opacity: 1;
                text-decoration: none;
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
            }

            .breadcrumb span {
                color: white !important;
                opacity: 1;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.25);
            }

            .breadcrumb svg {
                color: white !important;
                opacity: 0.8;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            }

            .form-card {
                background: white;
                border-radius: 1rem;
                padding: 2.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
                margin-bottom: 2rem;
            }

            .form-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-icon {
                width: 24px;
                height: 24px;
                color: var(--primary-color);
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                font-weight: 600;
                color: var(--gray-700);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .form-input, .form-select {
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.75rem;
                font-size: 1rem;
                transition: all 0.2s ease;
                background: var(--gray-50);
            }

            .form-input:focus, .form-select:focus {
                outline: none;
                border-color: var(--primary-color);
                background: white;
                box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
            }

            .current-value {
                background: #f3e8ff;
                border: 2px solid var(--primary-color);
                color: var(--gray-800);
            }

            .password-input-group {
                position: relative;
            }

            .password-toggle {
                position: absolute;
                right: 1rem;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
                color: var(--gray-600);
                transition: color 0.2s ease;
            }

            .password-toggle:hover {
                color: var(--primary-color);
            }

            .role-checkboxes {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                padding: 1rem;
                background: var(--gray-50);
                border-radius: 0.75rem;
                border: 2px solid var(--gray-200);
            }

            .role-checkbox {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 0.75rem;
                background: white;
                border-radius: 0.5rem;
                border: 1px solid var(--gray-200);
                transition: all 0.2s ease;
                cursor: pointer;
            }

            .role-checkbox:hover {
                border-color: var(--primary-color);
                box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
            }

            .role-checkbox.selected {
                border-color: var(--primary-color);
                background: #f3e8ff;
            }

            .role-checkbox input[type="checkbox"] {
                width: 1.25rem;
                height: 1.25rem;
                accent-color: var(--primary-color);
            }

            .role-checkbox label {
                font-weight: 500;
                color: var(--gray-700);
                cursor: pointer;
                margin: 0;
                text-transform: none;
                letter-spacing: normal;
            }

            .btn-primary {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.3);
            }

            .danger-zone {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 2px solid #fecaca;
            }

            .danger-title {
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--danger-color);
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .danger-description {
                color: var(--gray-600);
                margin-bottom: 1.5rem;
                line-height: 1.6;
            }

            .btn-danger {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .btn-danger:hover {
                background: linear-gradient(135deg, var(--danger-hover) 0%, var(--danger-color) 100%);
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
            }

            .back-link {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                color: white !important;
                text-decoration: none;
                font-weight: 600;
                margin-bottom: 2rem;
                padding: 0.875rem 1.75rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                border-radius: 0.75rem;
                box-shadow: 0 4px 12px rgba(124, 58, 237, 0.25);
                transition: all 0.2s ease;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }

            .back-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
                color: white !important;
                text-decoration: none;
                background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .back-link svg {
                color: white !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
            }

            .current-info {
                background: #f3e8ff;
                border: 1px solid var(--primary-color);
                border-radius: 0.75rem;
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .current-info-title {
                font-weight: 600;
                color: var(--primary-color);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .current-info-content {
                color: var(--gray-700);
                font-size: 0.875rem;
            }

            .error-message {
                color: var(--danger-color);
                font-size: 0.875rem;
                margin-top: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .alert {
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                margin-bottom: 1.5rem;
                border: 1px solid;
            }

            .alert-success {
                background: #ecfdf5;
                border-color: #10b981;
                color: #065f46;
            }

            .alert-error {
                background: #fef2f2;
                border-color: #ef4444;
                color: #991b1b;
            }

            @media (max-width: 768px) {
                .modern-container {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }
                
                .form-card, .danger-zone {
                    padding: 1.5rem;
                }

                .role-checkboxes {
                    grid-template-columns: 1fr;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Back Link -->
            <a href="{{ route('users.index') }}" class="back-link">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Users List
            </a>

            <!-- Page Header -->
            <div class="page-header">
                <div class="breadcrumb">
                    <a href="{{ route('users.index') }}">User Management</a>
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                    <span>Edit User</span>
                </div>
                <h1 class="page-title">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Edit User
                </h1>
                <p class="page-subtitle">Update information for "{{ $user->name }}"</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success">
                    <strong>Success!</strong> {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    <strong>Error!</strong> {{ session('error') }}
                </div>
            @endif

            <!-- Current Information Display -->
            <div class="current-info">
                <div class="current-info-title">Current Information</div>
                <div class="current-info-content">
                    <strong>Name:</strong> {{ $user->name }} | 
                    <strong>Email:</strong> {{ $user->email }} | 
                    <strong>Roles:</strong> 
                    @forelse($user->roles as $role)
                        {{ $role->name }}{{ !$loop->last ? ', ' : '' }}
                    @empty
                        No roles assigned
                    @endforelse
                </div>
            </div>

            <!-- Edit Form -->
            <div class="form-card">
                <h2 class="form-title">
                    <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Update User Information
                </h2>
                
                <form action="{{ route('users.update', $user->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="form-group">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" 
                               class="form-input current-value" 
                               id="name" 
                               name="name" 
                               value="{{ old('name', $user->name) }}"
                               placeholder="Enter user's full name"
                               required>
                        @error('name')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" 
                               class="form-input current-value" 
                               id="email" 
                               name="email" 
                               value="{{ old('email', $user->email) }}"
                               placeholder="Enter user's email address"
                               required>
                        @error('email')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">Password (leave blank to keep current)</label>
                        <div class="password-input-group">
                            <input type="password" 
                                   class="form-input" 
                                   id="password" 
                                   name="password" 
                                   placeholder="Enter new password to change"
                                   autocomplete="new-password">
                            <span class="password-toggle" onclick="togglePassword()">
                                <svg id="eye-icon" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </span>
                        </div>
                        @error('password')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">User Roles</label>
                        <div class="role-checkboxes">
                            @foreach($roles as $role)
                                <div class="role-checkbox {{ in_array($role->id, $user->roles->pluck('id')->toArray()) ? 'selected' : '' }}">
                                    <input type="checkbox" 
                                           id="role_{{ $role->id }}" 
                                           name="roles[]" 
                                           value="{{ $role->id }}"
                                           {{ in_array($role->id, old('roles', $user->roles->pluck('id')->toArray())) ? 'checked' : '' }}>
                                    <label for="role_{{ $role->id }}">{{ $role->name }}</label>
                                </div>
                            @endforeach
                        </div>
                        @error('roles')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Update User
                    </button>
                </form>
            </div>

            <!-- Danger Zone -->
            <div class="danger-zone">
                <h3 class="danger-title">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    Danger Zone
                </h3>
                <p class="danger-description">
                    Once you delete this user, there is no going back. This action will permanently remove 
                    "{{ $user->name }}" and all associated data. Please be certain before proceeding.
                </p>
                
                <form action="{{ route('users.destroy', $user->id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="btn-danger" 
                            onclick="return confirm('⚠️ FINAL WARNING ⚠️\n\nYou are about to permanently delete:\n\nName: {{ $user->name }}\nEmail: {{ $user->email }}\n\nThis action CANNOT be undone!\n\nAre you absolutely sure?')">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                        Delete User Permanently
                    </button>
                </form>
            </div>
        </div>
        
        <script>
            function togglePassword() {
                const passwordInput = document.getElementById('password');
                const eyeIcon = document.getElementById('eye-icon');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    eyeIcon.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                    `;
                } else {
                    passwordInput.type = 'password';
                    eyeIcon.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    `;
                }
            }

            // Update role checkbox styling when checked/unchecked
            document.querySelectorAll('.role-checkbox input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const roleCheckbox = this.closest('.role-checkbox');
                    if (this.checked) {
                        roleCheckbox.classList.add('selected');
                    } else {
                        roleCheckbox.classList.remove('selected');
                    }
                });
            });
        </script>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>