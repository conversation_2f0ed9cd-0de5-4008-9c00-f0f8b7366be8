<div>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Daily Performance Report</h5>
                            <p class="text-sm mb-0">
                                Submit your daily performance for {{ \Carbon\Carbon::parse($performance_date)->format('F d, Y') }}
                                @if($isSubmitted)
                                    <span class="badge bg-gradient-success ms-2">Submitted</span>
                                @else
                                    <span class="badge bg-gradient-warning ms-2">Pending</span>
                                @endif
                            </p>

                            <!-- User Department Info -->
                            <div class="mt-2">
                                @if($userDepartments->count() > 0)
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>
                                        <strong>Department(s):</strong>
                                        @foreach($userDepartments as $department)
                                            <span class="badge bg-light text-dark me-1">{{ $department->name }}</span>
                                        @endforeach
                                    </small>
                                @endif

                                @if(auth()->user()->name !== 'Abid Madani' && (auth()->user()->isMujeeb() || auth()->user()->isSuperior()))
                                    <small class="text-muted ms-3">
                                        <i class="fas fa-user-tag me-1"></i>
                                        <strong>Role:</strong>
                                        <span class="badge bg-primary">
                                            @if(auth()->user()->isSuperior())
                                                Superior
                                            @elseif(auth()->user()->isMujeeb())
                                                Mujeeb
                                            @endif
                                        </span>
                                    </small>
                                @endif
                            </div>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                @if($todaysTasks->count() > 0)
                                    <button wire:click="autoFillTasksCompleted" class="btn btn-outline-info btn-sm mb-0 me-2">
                                        <i class="fas fa-magic"></i>&nbsp;&nbsp;Auto-fill Tasks
                                    </button>
                                @endif
                                @if(!$isSubmitted)
                                    <button wire:click="saveAsDraft" class="btn btn-outline-secondary btn-sm mb-0 me-2">
                                        <i class="fas fa-save"></i>&nbsp;&nbsp;Save Draft
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if(!$hasAssignedTasks)
                        <!-- No Tasks Message -->
                        <div class="alert alert-info text-center" role="alert">
                            <div class="icon-container mb-3">
                                <i class="fas fa-tasks text-info" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="alert-heading">No Tasks Assigned</h4>
                            <p class="mb-3">
                                You currently have no active tasks assigned to you. Daily performance submission is only required for users with assigned tasks.
                            </p>
                            <hr>
                            <p class="mb-0">
                                Please contact your Superior or Admin if you believe this is an error, or if you need tasks to be assigned to you.
                            </p>
                        </div>
                    @else
                        <form wire:submit.prevent="submitReport">
                            <div class="row">
                                <!-- Performance Date -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Performance Date *</label>
                                        <input type="date" wire:model.live="performance_date"
                                               class="form-control @error('performance_date') is-invalid @enderror"
                                               {{ !$this->canEdit() ? 'disabled' : '' }}>
                                        @error('performance_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Task Selection -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Select Task *</label>
                                        <select wire:model.live="selected_task_id"
                                                class="form-select @error('selected_task_id') is-invalid @enderror"
                                                {{ !$this->canEdit() ? 'disabled' : '' }}>
                                            <option value="">Choose a task...</option>
                                            @foreach($assignedTasks as $task)
                                                <option value="{{ $task->id }}">
                                                    {{ $task->title }}
                                                    @if($task->department)
                                                        ({{ $task->department->name }})
                                                    @endif
                                                    - Due: {{ $task->due_date ? $task->due_date->format('M d') : 'No due date' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('selected_task_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Department Display -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Department</label>
                                        <div class="form-control bg-light">
                                            @if($selectedTaskDepartment)
                                                <span class="badge bg-primary">{{ $selectedTaskDepartment->name }}</span>
                                            @else
                                                <span class="text-muted">Select a task first</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Second Row: Hours and Summary -->
                            <div class="row">
                                <!-- Hours Worked -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Hours Worked *</label>
                                        <input type="number" step="0.5" min="0" max="24" wire:model="hours_worked"
                                               class="form-control @error('hours_worked') is-invalid @enderror"
                                               placeholder="8.0"
                                               {{ !$this->canEdit() ? 'disabled' : '' }}>
                                        @error('hours_worked') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Performance Summary -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Performance Summary</label>
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-gradient-info">
                                                {{ $hours_worked ?: '0' }} hrs
                                            </span>
                                            @if($selectedTaskDepartment)
                                                <span class="badge bg-gradient-primary">
                                                    {{ $selectedTaskDepartment->name }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>



                        <!-- Tasks Completed -->
                        <div class="form-group">
                            <label class="form-label">Tasks Completed *</label>
                            <textarea wire:model="tasks_completed"
                                      class="form-control @error('tasks_completed') is-invalid @enderror"
                                      rows="4"
                                      placeholder="Describe the tasks you completed today..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('tasks_completed') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Challenges Faced -->
                        <div class="form-group">
                            <label class="form-label">Challenges Faced</label>
                            <textarea wire:model="challenges_faced"
                                      class="form-control @error('challenges_faced') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Describe any challenges or obstacles you faced..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('challenges_faced') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Next Day Plan -->
                        <div class="form-group">
                            <label class="form-label">Next Day Plan</label>
                            <textarea wire:model="next_day_plan"
                                      class="form-control @error('next_day_plan') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Outline your plan for tomorrow..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('next_day_plan') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea wire:model="additional_notes"
                                      class="form-control @error('additional_notes') is-invalid @enderror"
                                      rows="2"
                                      placeholder="Any additional notes or comments..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('additional_notes') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- File Attachments -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-paperclip me-1"></i>
                                Work Evidence Files
                                <small class="text-muted">(Images, PDFs, Word docs, etc.)</small>
                            </label>
                            
                            @if($this->canEdit())
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="upload-area border-2 border-dashed border-primary rounded p-4 text-center"
                                             style="background-color: #f8f9ff;">
                                            <input type="file" wire:model="attachments" multiple
                                                   class="form-control @error('attachments.*') is-invalid @enderror"
                                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx"
                                                   id="file-upload"
                                                   style="display: none;">

                                            <div class="upload-content" onclick="document.getElementById('file-upload').click();" style="cursor: pointer;">
                                                <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 3rem;"></i>
                                                <h6 class="mt-3 mb-2">Click to select files or drag & drop</h6>
                                                <p class="text-muted mb-2">Files will upload automatically when selected</p>
                                                <small class="text-muted">
                                                    Max size: {{ $this->getMaxFileSize() }} per file.
                                                    Allowed: {{ $this->getAllowedFileTypes() }}
                                                </small>
                                            </div>

                                            <div wire:loading wire:target="attachments" class="mt-3">
                                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                                    <span class="visually-hidden">Uploading...</span>
                                                </div>
                                                <p class="text-primary mt-2 mb-0">Uploading files...</p>
                                            </div>
                                        </div>
                                        @error('attachments.*') <div class="invalid-feedback d-block">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Legacy Upload Button (hidden by default) -->
                                <div class="row mt-2" style="display: none;">
                                    <div class="col-md-4">
                                        <button type="button" wire:click="uploadFiles"
                                                class="btn btn-outline-primary btn-sm w-100"
                                                wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="uploadFiles">
                                                <i class="fas fa-upload"></i> Manual Upload
                                            </span>
                                            <span wire:loading wire:target="uploadFiles">
                                                <i class="fas fa-spinner fa-spin"></i> Uploading...
                                            </span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Upload Progress -->
                                <div wire:loading wire:target="attachments" class="mt-2">
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                             role="progressbar" style="width: 100%">
                                            Processing files...
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Enhanced Uploaded Files Display -->
                            @if(!empty($uploadedFiles))
                                <div class="mt-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="text-sm font-weight-bold mb-0">
                                            <i class="fas fa-paperclip me-2 text-primary"></i>
                                            Uploaded Files ({{ count($uploadedFiles) }})
                                        </h6>
                                        <span class="badge bg-gradient-info">
                                            {{ $this->getTotalFileSize($uploadedFiles) }}
                                        </span>
                                    </div>

                                    <div class="row">
                                        @foreach($uploadedFiles as $file)
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="card border shadow-sm h-100">
                                                    <div class="card-body p-3">
                                                        <div class="d-flex align-items-center mb-2">
                                                            <div class="me-3">
                                                                <i class="{{ $this->getFileIcon($file) }} text-primary" style="font-size: 2.5rem;"></i>
                                                            </div>
                                                            <div class="flex-grow-1 min-w-0">
                                                                <h6 class="mb-1 text-truncate" title="{{ $file['original_name'] }}">
                                                                    {{ $file['original_name'] }}
                                                                </h6>
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <small class="text-muted">
                                                                        {{ $this->formatFileSize($file['file_size']) }}
                                                                    </small>
                                                                    <span class="badge bg-light text-dark">
                                                                        {{ strtoupper(pathinfo($file['original_name'], PATHINFO_EXTENSION)) }}
                                                                    </span>
                                                                </div>
                                                                <small class="text-muted">
                                                                    <i class="fas fa-clock me-1"></i>
                                                                    {{ \Carbon\Carbon::parse($file['created_at'])->format('M d, Y H:i') }}
                                                                </small>
                                                            </div>
                                                        </div>

                                                        <div class="btn-group w-100" role="group">
                                                            @if($this->canViewInBrowser($file))
                                                                <a href="{{ route('daily-performance.attachment.view', $file['id']) }}"
                                                                   target="_blank"
                                                                   class="btn btn-sm btn-outline-info">
                                                                    <i class="fas fa-eye me-1"></i>
                                                                    View
                                                                </a>
                                                            @endif
                                                            <a href="{{ route('daily-performance.attachment.download', $file['id']) }}"
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-download me-1"></i>
                                                                Download
                                                            </a>
                                                            @if($this->canEdit())
                                                                <button wire:click="removeAttachment({{ $file['id'] }})"
                                                                        class="btn btn-sm btn-outline-danger"
                                                                        onclick="return confirm('Are you sure you want to delete this file?')"
                                                                        title="Delete File">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <div class="mt-4 text-center py-3">
                                    <i class="fas fa-file-upload text-muted" style="font-size: 2rem;"></i>
                                    <p class="text-muted mt-2 mb-0">No files uploaded yet. Use the upload area above to add files.</p>
                                </div>
                            @endif
                        </div>

                        <!-- Submit Button -->
                        @if($this->canEdit() && !$isSubmitted)
                        <div class="form-group text-end">
                            <button type="submit" class="btn bg-gradient-primary">
                                <i class="fas fa-paper-plane"></i>&nbsp;&nbsp;Submit Performance Report
                            </button>
                        </div>
                        @endif
                    </form>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Tasks Card -->
    @if($todaysTasks->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Today's Tasks ({{ $completedTasksCount }}/{{ $todaysTasks->count() }} completed)</h6>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($todaysTasks as $task)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $this->getTaskStatusColor($task->status) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="btn-group" role="group">
                                            @php
                                                $performanceStatus = $this->getTaskPerformanceStatus($task->id);
                                                $attachmentCount = $this->getTaskAttachmentCount($task->id);
                                            @endphp
                                            
                                            <!-- Performance Status Badge -->
                                            <span class="badge bg-{{ $performanceStatus['color'] }} me-2">
                                                {{ $performanceStatus['label'] }}
                                            </span>
                                            
                                            <!-- Edit Performance Button -->
                                            <button wire:click="editTaskPerformance({{ $task->id }})" 
                                                    class="btn btn-sm btn-outline-primary me-1" 
                                                    title="Edit Performance">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            
                                            <!-- View Attachments Button -->
                                            @if($attachmentCount > 0)
                                                <button wire:click="viewTaskAttachments({{ $task->id }})" 
                                                        class="btn btn-sm btn-outline-info me-1" 
                                                        title="View Attachments ({{ $attachmentCount }})">
                                                    <i class="fas fa-paperclip"></i>
                                                    <span class="badge bg-info">{{ $attachmentCount }}</span>
                                                </button>
                                            @endif
                                            
                                            <!-- Delete Performance Button -->
                                            @if($performanceStatus['status'] !== 'none')
                                                <button wire:click="deleteTaskPerformance({{ $task->id }})" 
                                                        class="btn btn-sm btn-outline-danger me-1" 
                                                        title="Delete Performance"
                                                        onclick="return confirm('Are you sure you want to delete the performance record for this task?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                            
                                            <!-- Complete Task Button -->
                                            @if($task->status !== 'completed' && $this->canEdit())
                                                <button wire:click="markTaskCompleted({{ $task->id }})"
                                                        class="btn btn-sm btn-outline-success" 
                                                        title="Mark as Completed">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- All Assigned Tasks Card -->
    @if($allUserTasks->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">All Performance Reports by Task</h6>
                    <p class="text-sm mb-0">All submitted performance reports for your assigned tasks with attachments</p>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Department</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Performance Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hours Worked</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Attachments</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($allUserTasks as $task)
                                    @if($task->dailyPerformances && $task->dailyPerformances->count() > 0)
                                        @foreach($task->dailyPerformances as $index => $performance)
                                        <tr class="{{ $task->isOverdue() ? 'table-warning' : '' }}">
                                            <td>
                                                <div class="d-flex px-2 py-1">
                                                    <div class="d-flex flex-column justify-content-center">
                                                        @if($index === 0)
                                                            <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                            @if($task->description)
                                                                <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                            @endif
                                                            @if($task->assignedBy)
                                                                <p class="text-xs text-info mb-0">Assigned by: {{ $task->assignedBy->name }}</p>
                                                            @endif
                                                        @else
                                                            <p class="text-xs text-muted mb-0">↳ Same task</p>
                                                        @endif
                                                        <div class="mt-1">
                                                            <small class="text-primary">{{ Str::limit($performance->tasks_completed, 80) }}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="align-middle text-center">
                                                @if($index === 0)
                                                    @if($task->department)
                                                        <span class="badge badge-sm bg-gradient-info">{{ $task->department->name }}</span>
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td class="align-middle text-center">
                                                <span class="text-xs text-primary fw-bold">
                                                    {{ \Carbon\Carbon::parse($performance->performance_date)->format('M d, Y') }}
                                                </span>
                                                <br>
                                                <small class="text-muted">
                                                    {{ \Carbon\Carbon::parse($performance->created_at)->format('H:i') }}
                                                </small>
                                            </td>
                                            <td class="align-middle text-center">
                                                <span class="badge badge-sm bg-gradient-primary">
                                                    {{ $performance->hours_worked ?? 0 }}h
                                                </span>
                                            </td>
                                            <td class="align-middle text-center">
                                                <span class="badge badge-sm bg-gradient-success">
                                                    Submitted
                                                </span>
                                            </td>
                                            <td class="align-middle text-center">
                                                @php
                                                    $attachmentCount = $performance->attachments ? $performance->attachments->count() : 0;
                                                @endphp

                                                @if($attachmentCount > 0)
                                                    <div class="text-center">
                                                        <span class="badge bg-info mb-1">
                                                            <i class="fas fa-paperclip"></i> {{ $attachmentCount }}
                                                        </span>
                                                        <br>
                                                        <div class="d-flex flex-wrap justify-content-center gap-1">
                                                            @foreach($performance->attachments->take(3) as $attachment)
                                                                <small class="badge bg-light text-dark" title="{{ $attachment->original_name }}">
                                                                    {{ strtoupper(pathinfo($attachment->original_name, PATHINFO_EXTENSION)) }}
                                                                </small>
                                                            @endforeach
                                                            @if($performance->attachments->count() > 3)
                                                                <small class="badge bg-secondary">+{{ $performance->attachments->count() - 3 }}</small>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @else
                                                    <span class="text-muted">None</span>
                                                @endif
                                            </td>
                                            <td class="align-middle text-center">
                                                <div class="btn-group" role="group">

                                                    <!-- Edit Performance Button -->
                                                    <button wire:click="editSpecificPerformance({{ $performance->id }})"
                                                            class="btn btn-sm btn-outline-primary me-1"
                                                            title="Edit This Performance">
                                                        <i class="fas fa-edit"></i>
                                                    </button>

                                                    <!-- View Attachments Button -->
                                                    @if($attachmentCount > 0)
                                                        <button wire:click="viewPerformanceAttachments({{ $performance->id }})"
                                                                class="btn btn-sm btn-outline-info me-1"
                                                                title="View Attachments ({{ $attachmentCount }})">
                                                            <i class="fas fa-paperclip"></i>
                                                            <span class="badge bg-info">{{ $attachmentCount }}</span>
                                                        </button>
                                                    @endif

                                                    <!-- Delete Performance Button -->
                                                    <button wire:click="deleteSpecificPerformance({{ $performance->id }})"
                                                            class="btn btn-sm btn-outline-danger me-1"
                                                            title="Delete This Performance"
                                                            onclick="return confirm('Are you sure you want to delete this performance record?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>

                                                    <!-- View Details Button -->
                                                    <button wire:click="viewPerformanceDetails({{ $performance->id }})"
                                                            class="btn btn-sm btn-outline-secondary"
                                                            title="View Full Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center text-muted py-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                No performance reports submitted for "{{ $task->title }}"
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Attachments Modal -->
    @if($selectedTaskForAttachments)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-paperclip me-2"></i>
                            Task Attachments
                        </h5>
                        <button type="button" class="btn-close" wire:click="closeAttachmentsModal"></button>
                    </div>
                    <div class="modal-body">
                        @if(!empty($taskAttachments))
                            <div class="row">
                                @foreach($taskAttachments as $attachment)
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <i class="{{ $this->getFileIcon($attachment) }}" style="font-size: 2rem;"></i>
                                                    </div>
                                                    <div class="flex-grow-1 min-w-0">
                                                        <h6 class="mb-1 text-truncate" title="{{ $attachment['original_name'] }}">
                                                            {{ $attachment['original_name'] }}
                                                        </h6>
                                                        <small class="text-muted">
                                                            {{ $this->formatFileSize($attachment['file_size']) }}
                                                        </small>
                                                        <br>
                                                        <small class="text-muted">
                                                            {{ \Carbon\Carbon::parse($attachment['created_at'])->format('M d, Y H:i') }}
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <div class="btn-group w-100" role="group">
                                                        @if($this->canViewInBrowser($attachment))
                                                            <a href="{{ route('daily-performance.attachment.view', $attachment['id']) }}" 
                                                               target="_blank"
                                                               class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye me-1"></i>
                                                                View
                                                            </a>
                                                        @endif
                                                        <a href="{{ route('daily-performance.attachment.download', $attachment['id']) }}" 
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fas fa-download me-1"></i>
                                                            Download
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-paperclip text-muted" style="font-size: 3rem;"></i>
                                <p class="mt-3 text-muted">No attachments found for this task.</p>
                            </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeAttachmentsModal">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Performance History Modal -->
    @if($selectedTaskForHistory)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-history me-2"></i>
                            Performance History
                        </h5>
                        <button type="button" class="btn-close" wire:click="closeHistoryModal"></button>
                    </div>
                    <div class="modal-body">
                        @if(!empty($taskPerformanceHistory))
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Hours</th>
                                            <th>Tasks Completed</th>
                                            <th>Attachments</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($taskPerformanceHistory as $perf)
                                            <tr>
                                                <td>
                                                    <strong>{{ \Carbon\Carbon::parse($perf['performance_date'])->format('M d, Y') }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ \Carbon\Carbon::parse($perf['created_at'])->format('H:i') }}</small>
                                                </td>
                                                <td>
                                                    @if($perf['is_submitted'])
                                                        <span class="badge bg-success">Submitted</span>
                                                    @else
                                                        <span class="badge bg-warning">Draft</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <strong>{{ $perf['hours_worked'] ?? 0 }}</strong> hrs
                                                </td>
                                                <td>
                                                    <div style="max-width: 300px;">
                                                        {{ \Str::limit($perf['tasks_completed'] ?? 'No description', 100) }}
                                                    </div>
                                                </td>
                                                <td>
                                                    @php
                                                        $attachmentCount = count($perf['attachments'] ?? []);
                                                    @endphp
                                                    @if($attachmentCount > 0)
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-paperclip"></i> {{ $attachmentCount }}
                                                        </span>
                                                    @else
                                                        <span class="text-muted">None</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button wire:click="editSpecificPerformance({{ $perf['id'] }})" 
                                                                class="btn btn-sm btn-outline-primary" 
                                                                title="Edit This Record">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button wire:click="deleteSpecificPerformance({{ $perf['id'] }})" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Delete This Record"
                                                                onclick="return confirm('Are you sure you want to delete this performance record?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-history text-muted" style="font-size: 3rem;"></i>
                                <p class="mt-3 text-muted">No performance history found for this task.</p>
                            </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeHistoryModal">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif
</div>

<style>
/* File upload styling */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

.file-upload-area.dragover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* File attachment cards */
.attachment-card {
    transition: all 0.3s ease;
}

.attachment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* File type icons */
.file-icon {
    font-size: 1.2rem;
}

/* Upload progress */
.upload-progress {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.upload-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

/* Responsive file grid */
@media (max-width: 768px) {
    .attachment-grid .col-md-6 {
        margin-bottom: 0.5rem;
    }
}

/* Task action buttons */
.btn-group .btn {
    border-radius: 0.25rem !important;
    margin-right: 0.25rem;
}

.btn-group .badge {
    font-size: 0.7rem;
    margin-left: 0.25rem;
}

/* Modal styling */
.modal.show {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Performance status badges */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Task table responsive */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}

/* Enhanced upload area styles */
.upload-area {
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    background-color: #e3f2fd !important;
    border-color: #1976d2 !important;
    transform: scale(1.01);
}

.upload-area.drag-over {
    background-color: #e8f5e8 !important;
    border-color: #28a745 !important;
    transform: scale(1.02);
}

.upload-content {
    pointer-events: none;
}

/* Performance report table enhancements */
.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75em;
}

/* File extension badges */
.badge.bg-light {
    color: #495057 !important;
    border: 1px solid #dee2e6;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced file upload drag and drop functionality
    const fileInput = document.getElementById('file-upload');
    const uploadArea = document.querySelector('.upload-area');

    if (fileInput && uploadArea) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            uploadArea.style.backgroundColor = '#e3f2fd';
            uploadArea.style.borderColor = '#1976d2';
            uploadArea.style.transform = 'scale(1.02)';
        }

        function unhighlight(e) {
            uploadArea.style.backgroundColor = '#f8f9ff';
            uploadArea.style.borderColor = '#6c757d';
            uploadArea.style.transform = 'scale(1)';
        }

        uploadArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                // Trigger Livewire update for auto-upload
                fileInput.dispatchEvent(new Event('change', { bubbles: true }));

                // Show upload feedback
                const uploadContent = uploadArea.querySelector('.upload-content');
                if (uploadContent) {
                    uploadContent.innerHTML = `
                        <i class="fas fa-spinner fa-spin text-primary" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 mb-2">Uploading ${files.length} file(s)...</h6>
                        <p class="text-muted mb-0">Files will be uploaded automatically</p>
                    `;
                }
            }
        }
    }
    
    // File size validation
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const files = e.target.files;
            const maxSize = 10 * 1024 * 1024; // 10MB
            
            for (let i = 0; i < files.length; i++) {
                if (files[i].size > maxSize) {
                    alert(`File "${files[i].name}" is too large. Maximum size is 10MB.`);
                    e.target.value = '';
                    return;
                }
            }
        });
    }
});
</script>
</div>
