<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="mujeeb"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Mujeeb Management"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #059669;
                --primary-hover: #047857;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 1200px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #10b981 100%);
                color: white;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(5, 150, 105, 0.2);
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .page-subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
                margin-top: 0.5rem;
                margin-bottom: 0;
            }

            .content-grid {
                display: grid;
                grid-template-columns: 1fr 2fr;
                gap: 2rem;
                align-items: start;
            }

            .form-card {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
                height: fit-content;
                position: sticky;
                top: 2rem;
            }

            .form-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-icon {
                width: 24px;
                height: 24px;
                color: var(--primary-color);
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                font-weight: 600;
                color: var(--gray-700);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .form-input, .form-select {
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.75rem;
                font-size: 1rem;
                transition: all 0.2s ease;
                background: var(--gray-50);
            }

            .form-input:focus, .form-select:focus {
                outline: none;
                border-color: var(--primary-color);
                background: white;
                box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
            }

            .form-select {
                cursor: pointer;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 0.5rem center;
                background-repeat: no-repeat;
                background-size: 1.5em 1.5em;
                padding-right: 2.5rem;
            }

            .btn-primary {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #10b981 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
            }

            .table-card {
                background: white;
                border-radius: 1rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
            }

            .table-header {
                background: var(--gray-50);
                padding: 1.5rem 2rem;
                border-bottom: 1px solid var(--gray-200);
            }

            .table-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .table-count {
                background: var(--primary-color);
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 9999px;
                font-size: 0.875rem;
                font-weight: 600;
            }

            .modern-table {
                width: 100%;
                border-collapse: collapse;
            }

            .modern-table th {
                background: var(--gray-50);
                padding: 1rem 1.5rem;
                text-align: left;
                font-weight: 600;
                color: var(--gray-700);
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                border-bottom: 1px solid var(--gray-200);
            }

            .modern-table td {
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--gray-100);
                vertical-align: middle;
            }

            .modern-table tbody tr:hover {
                background: var(--gray-50);
            }

            .name-cell {
                font-weight: 600;
                color: var(--gray-800);
            }

            .darulifta-badge {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                display: inline-block;
            }

            .munsab-badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                display: inline-block;
            }

            .munsab-mufti {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                color: white;
            }

            .munsab-mutakhassis {
                background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
                color: white;
            }

            .munsab-muawin {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                color: white;
            }

            .action-buttons {
                display: flex;
                gap: 0.5rem;
                align-items: center;
            }

            .btn-edit {
                padding: 0.5rem 1rem;
                background: var(--warning-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.25rem;
            }

            .btn-edit:hover {
                background: #d97706;
                transform: translateY(-1px);
                color: white;
                text-decoration: none;
            }

            .btn-delete {
                padding: 0.5rem 1rem;
                background: var(--danger-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.2s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.25rem;
            }

            .btn-delete:hover {
                background: #dc2626;
                transform: translateY(-1px);
            }

            .delete-form {
                display: inline;
            }

            .empty-state {
                text-align: center;
                padding: 3rem 2rem;
                color: var(--gray-600);
            }

            .empty-icon {
                width: 64px;
                height: 64px;
                margin: 0 auto 1rem;
                color: var(--gray-300);
            }

            .alert {
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                margin-bottom: 1.5rem;
                border: 1px solid;
            }

            .alert-success {
                background: #ecfdf5;
                border-color: #10b981;
                color: #065f46;
            }

            .alert-error {
                background: #fef2f2;
                border-color: #ef4444;
                color: #991b1b;
            }

            @media (max-width: 768px) {
                .content-grid {
                    grid-template-columns: 1fr;
                    gap: 1.5rem;
                }
                
                .modern-container {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                }
                
                .form-card {
                    position: static;
                }
                
                .action-buttons {
                    flex-direction: column;
                    align-items: stretch;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Mujeeb Management</h1>
                <p class="page-subtitle">Manage scholars and their positions across different Darulifta institutions</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success">
                    <strong>Success!</strong> {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    <strong>Error!</strong> {{ session('error') }}
                </div>
            @endif

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Add Form -->
                <div class="form-card">
                    <h2 class="form-title">
                        <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                        </svg>
                        Add New Mujeeb
                    </h2>
                    
                    <form action="{{ route('mujeeb.store') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="mujeeb_name" class="form-label">Mujeeb Name</label>
                            <input type="text" 
                                   class="form-input" 
                                   id="mujeeb_name" 
                                   name="mujeeb_name" 
                                   placeholder="Enter scholar's full name"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label for="darul_name" class="form-label">Darulifta Institution</label>
                            <select class="form-select" id="darul_name" name="darul_name" required>
                                <option value="">Select Institution</option>
                                @foreach($daruliftas as $darulifta)
                                    <option value="{{ $darulifta->darul_name }}">{{ $darulifta->darul_name }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="munsab" class="form-label">Position (Munsab)</label>
                            <select class="form-select" id="munsab" name="munsab" required>
                                <option value="">Select Position</option>
                                <option value="Mufti">Mufti</option>
                                <option value="Mutakhassis">Mutakhassis</option>
                                <option value="Muawin">Muawin</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn-primary">
                            Add Mujeeb
                        </button>
                    </form>
                </div>

                <!-- Data Table -->
                <div class="table-card">
                    <div class="table-header">
                        <h2 class="table-title">
                            <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                            Registered Scholars
                            <span class="table-count">{{ count($mujeebs) }}</span>
                        </h2>
                    </div>

                    @if(count($mujeebs) > 0)
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Scholar Name</th>
                                    <th>Institution</th>
                                    <th>Position</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($mujeebs as $mujeeb)
                                <tr>
                                    <td class="name-cell">{{ $mujeeb->mujeeb_name }}</td>
                                    <td>
                                        <span class="darulifta-badge">{{ $mujeeb->darul_name }}</span>
                                    </td>
                                    <td>
                                        <span class="munsab-badge munsab-{{ strtolower($mujeeb->munsab) }}">
                                            {{ $mujeeb->munsab }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('mujeeb.edit', ['id' => $mujeeb->id]) }}" class="btn-edit">
                                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                                </svg>
                                                Edit
                                            </a>
                                            <form action="{{ route('mujeeb.delete', ['id' => $mujeeb->id]) }}" method="POST" class="delete-form">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn-delete" 
                                                        onclick="return confirm('Are you sure you want to delete {{ $mujeeb->mujeeb_name }}? This action cannot be undone.')">
                                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                    </svg>
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div class="empty-state">
                            <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                            <h3>No Scholars Found</h3>
                            <p>Start by adding your first Mujeeb using the form on the left.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
