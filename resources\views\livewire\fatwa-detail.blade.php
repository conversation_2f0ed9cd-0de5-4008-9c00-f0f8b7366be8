
<div>
    <!-- Layout Components with wire:ignore -->
    <div wire:ignore>
        <x-layout bodyClass="g-sidenav-show bg-gray-200">
            <x-navbars.sidebar activePage="fatwa-detail"></x-navbars.sidebar>
        </x-layout>
    </div>
    <!-- Modern UI Styles -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <style>
        /* Modern CSS Variables */
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #6366f1;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --light-bg: #f8fafc;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --card-shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        /* Layout Fix for Livewire Component */
        body.g-sidenav-show .main-content {
            margin-left: 270px !important;
            transition: none !important;
        }

        /* Ensure main content stays in place */
        .main-content {
            margin-left: 270px !important;
            padding: 20px;
            min-height: 100vh;
            position: relative;
            z-index: 1;
            transition: none !important;
        }

        /* Container adjustments */
        .container-fluid {
            padding-left: 15px;
            padding-right: 15px;
            max-width: 100%;
        }

        /* Ensure proper body class is applied */
        body {
            background-color: #f8f9fa !important;
        }

        /* Sidebar positioning */
        .sidenav {
            position: fixed !important;
            top: 12px !important;
            left: 12px !important;
            width: 270px !important;
            z-index: 1000 !important;
        }

        /* Modern Typography */
        .urdu-font {
            font-family: 'Jameel Noori Nastaleeq', 'Noto Nastaliq Urdu', serif;
            font-size: 18px;
            line-height: 1.8;
        }

        /* Modern Loading Animation */
        .modern-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(8px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modern Card Design */
        .modern-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid #e2e8f0;
            transition: var(--transition);
            overflow: hidden;
        }

        .modern-card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .modern-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 1px solid #e2e8f0;
            padding: 24px;
            position: relative;
        }

        .modern-card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .modern-card-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .modern-card-body {
            padding: 24px;
        }

        /* Modern Question Section */
        .question-section {
            background: linear-gradient(135deg, #fef7cd 0%, #fef3c7 100%);
            border: 1px solid #fbbf24;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }

        .question-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--warning-color);
        }

        .question-label {
            background: var(--warning-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            display: inline-block;
            margin-bottom: 16px;
        }

        .question-text {
            font-size: 25px;
            line-height: 1.8;
            color: #374151;
            direction: rtl;
            text-align: right;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            max-width: 100%;
        }

        /* Clean Table Design - Like Old Version */
        .modern-table {
            background: white;
            border: 1px solid #dee2e6;
            border-collapse: collapse;
            width: 100%;
            table-layout: auto;
            font-family: Arial, sans-serif;
            font-size: 13px;
            margin: 0;
            padding: 0;
            border-spacing: 0;
        }

        .modern-table thead {
            background: #343a40;
        }

        .modern-table thead th {
            color: white;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            padding: 12px 8px;
            border: 1px solid #495057;
            text-align: center;
            vertical-align: middle;
        }

        .modern-table tbody tr {
            border-bottom: 1px solid #dee2e6;
        }

        .modern-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .modern-table tbody tr:hover {
            background-color: #e9ecef;
        }

        .modern-table tbody td {
            padding: 6px 4px;
            border: 1px solid #dee2e6;
            vertical-align: top;
            font-size: 12px;
            text-align: center;
            line-height: 1.3;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            overflow: hidden;
        }
        
        /* Specific styling for text-heavy columns */
        .modern-table tbody td.col-sayel,
        .modern-table tbody td.col-category {
            font-size: 11px;
            line-height: 1.2;
            text-align: left;
            padding: 4px 6px;
        }
        
        /* Prevent text overflow in all cells */
        .modern-table tbody td div {
            word-break: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            max-width: 100%;
            margin-bottom: 2px;
        }
        
        /* Table container - consolidated to prevent extra columns */
        .table-container {
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            margin: 0;
            padding: 0;
            display: block;
            box-sizing: border-box;
        }
        
        /* Remove any default Bootstrap table styling that might cause issues */
        .modern-table.table {
            margin-bottom: 0 !important;
        }
        
        /* Ensure no extra spacing that could cause empty columns */
        .modern-table th:last-child,
        .modern-table td:last-child {
            border-right: 1px solid #dee2e6;
        }
        
        /* Prevent any flex or grid issues */
        .modern-card-body {
            display: block;
        }
        
        /* Column width control using CSS classes - Total: 96% */
        .modern-table .col-sno { width: 4%; max-width: 4%; }
        .modern-table .col-fatwa-no { width: 9%; max-width: 9%; }
        .modern-table .col-sayel { width: 14%; max-width: 14%; }
        .modern-table .col-category { width: 12%; max-width: 12%; }
        .modern-table .col-rec-date { width: 7%; max-width: 7%; }
        .modern-table .col-mujeeb { width: 6%; max-width: 6%; }
        .modern-table .col-mail-folder { width: 9%; max-width: 9%; }
        .modern-table .col-checked-date { width: 6%; max-width: 6%; }
        .modern-table .col-checked-folder { width: 7%; max-width: 7%; }
        .modern-table .col-final-status { width: 7%; max-width: 7%; }
        .modern-table .col-expected-date { width: 7%; max-width: 7%; }
        .modern-table .col-actions { width: 8%; max-width: 8%; }
        
        /* Ensure table fits exactly within container */
        .modern-table {
            width: 100%;
            max-width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }
        
        /* Ensure all cells have proper box-sizing */
        .modern-table th,
        .modern-table td {
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* Allow text wrapping in specific columns */
        .modern-table .col-sayel,
        .modern-table .col-category {
            white-space: normal;
        }
        
        /* Prevent any extra space or phantom columns */
        .modern-table tr {
            width: 100%;
            display: table-row;
        }
        
        /* Force table to use exact column count */
        .modern-table thead tr,
        .modern-table tbody tr {
            display: table-row;
        }
        
        .modern-table thead tr th,
        .modern-table tbody tr td {
            display: table-cell;
        }
        
        /* Absolutely prevent extra columns */
        .modern-table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .modern-table th,
        .modern-table td {
            border-collapse: collapse;
        }
        
        /* Fix for text overlapping on larger screens */
        @media (min-width: 1200px) {
            .modern-table tbody td {
                padding: 8px 6px;
                font-size: 13px;
            }
            
            .modern-table tbody td.col-sayel,
            .modern-table tbody td.col-category {
                font-size: 12px;
            }
        }

        /* Column widths are now controlled by inline styles for better precision */
        .modern-table th,
        .modern-table td {
            box-sizing: border-box;
        }
        
        /* Ensure inline width styles take precedence */
        .modern-table th[style*="width"],
        .modern-table td[style*="width"] {
            width: inherit !important;
        }

        /* Mail folder entries styling */
        .mail-folder-entry {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2px;
            font-size: 11px;
        }

        .mail-folder-number {
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            margin-right: 4px;
        }

        .mail-folder-date {
            color: #007bff;
            font-weight: bold;
            margin-right: 4px;
        }

        .mail-folder-icon {
            color: #28a745;
            font-size: 12px;
        }

        /* Modern Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
        }

        .status-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .status-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .status-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
        }

        /* Modern Action Buttons */
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            margin: 0 3px;
            text-decoration: none;
            border: 2px solid transparent;
            font-size: 13px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .action-btn-view {
            background: var(--gradient-success);
            color: white;
        }

        .action-btn-download {
            background: var(--gradient-warning);
            color: white;
        }

        .action-btn-delete {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .action-btn-disabled {
            background: #e2e8f0;
            color: #94a3b8;
            cursor: not-allowed;
        }

        /* Modern Info Cards */
        .info-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary-color);
            margin-bottom: 12px;
        }

        .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
        }

        /* Modern Analysis Section */
        .analysis-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
            border-radius: var(--border-radius);
            padding: 24px;
            margin-top: 24px;
            width: 100%;
            overflow-x: auto;
        }

        .analysis-title {
            color: #0c4a6e;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Ensure analysis table is visible */
        .analysis-section .table-responsive {
            margin-bottom: 20px;
        }

        .analysis-section .modern-table {
            width: 100%;
            margin-bottom: 0;
        }

        /* Summary cards styling */
        .modern-card-body {
            padding: 20px;
        }

        /* Analysis Summary Styling */
        .analysis-summary-item {
            margin-bottom: 8px;
        }

        .analysis-summary-item .border-bottom {
            border-color: #e2e8f0 !important;
        }

        .summary-stat {
            padding: 15px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #64748b;
        }

        /* Badge styling for analysis */
        .badge.fs-6 {
            font-size: 14px !important;
            padding: 8px 12px;
        }

        .badge.fs-5 {
            font-size: 16px !important;
            padding: 10px 15px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .modern-table thead th {
                font-size: 11px;
                padding: 8px 4px;
            }
            
            .modern-table tbody td {
                font-size: 11px;
                padding: 5px 3px;
            }
        }

        @media (max-width: 992px) {
            .modern-table thead th {
                font-size: 10px;
                padding: 6px 3px;
            }
            
            .modern-table tbody td {
                font-size: 10px;
                padding: 4px 2px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0 !important;
                padding: 10px;
            }
            
            .modern-card-header,
            .modern-card-body {
                padding: 16px;
            }
            
            .modern-table thead th,
            .modern-table tbody td {
                padding: 4px 2px;
                font-size: 9px;
            }
            
            .question-text {
                font-size: 16px;
            }
            
            .analysis-section {
                padding: 16px;
                margin-top: 16px;
            }
        }

        @media (max-width: 576px) {
            .modern-table thead th,
            .modern-table tbody td {
                padding: 3px 1px;
                font-size: 8px;
            }
            
            .question-text {
                font-size: 18px;
                padding: 15px;
            }
            
            .action-btn {
                width: 24px;
                height: 24px;
                font-size: 9px;
            }
        }

        /* Prevent horizontal scroll */
        .table-responsive {
            overflow-x: visible !important;
        }

        .container-fluid {
            overflow-x: hidden;
        }

        /* Ensure text wrapping in all cells */
        .modern-table td div {
            word-break: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        /* Ensure proper z-index layering */
        .main-content {
            position: relative;
            z-index: 1;
            overflow-x: hidden;
            max-width: 100%;
        }

        .sidenav {
            z-index: 1000;
        }

        /* Fix any overflow issues */
        body {
            overflow-x: hidden;
        }

        /* Ensure all containers prevent horizontal scroll */
        .container-fluid {
            overflow-x: hidden;
            max-width: 100%;
        }

        .modern-card {
            overflow-x: hidden;
            max-width: 100%;
        }

        .table-container {
            overflow-x: hidden;
            max-width: 100%;
        }



        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Legacy styles maintained for compatibility */

/* Removed old table-responsive styles that caused horizontal scrolling */

.table-bordered th,
  .table-bordered td {
    border: 1px solid #dee2e6; /* Border color */
  }

  .table-bordered {
    border-collapse: collapse; /* Ensures borders are not doubled */
  }
  .title-cell {
        max-width: 150px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }
.question-cell {
        background-color: #ffffff;
        max-width: 1000px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }

    .question-text {
        white-space: normal; /* Allow text to wrap */
        word-wrap: break-word; /* Ensure long words break onto the next line */
        text-align: right; /* Right-align text */
        max-width: 100%; /* Ensure text doesn't overflow beyond the cell */
    }
    @media (max-width: 768px) {
        .question-text {
            white-space: normal;
            word-wrap: break-word;
        }
    }
.folder-entries {
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
    }
    .folder-entry {
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 5px;
        border: 1px solid #ccc; /* Border for each entry */
        border-radius: 4px;
        background-color: #f9f9f9; /* Background color for better contrast */
        transition: background-color 0.3s; /* Smooth transition for hover effect */
    }
    .folder-entry:hover {
        background-color: #e9ecef; /* Highlight color on hover */
    }
    .folder-date, .folder-status {
        white-space: nowrap;
        margin: 2px 0;
    }
    .date-link {
        text-decoration: none;
        color: #007bff; /* Link color */
    }
    .date-link:hover {
        text-decoration: underline; /* Underline on hover */
    }
                .arrow {
            transition: transform 0.3s;
        }
        .rotate-180 {
            transform: rotate(180deg);
        }
                .clickable-link {
        cursor: pointer;
        color: blue;
        text-decoration: underline;
    }
    .clickable-link:hover {
        color: darkblue;
    }
                .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
.main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }
                .view a i {
    color: #4CAF50; /* Green color for view icon */
}

.download a i {
    color: #FF5733; /* Orange color for download icon */
}
/* Removed old td styles that prevented text wrapping */
                .calendar-style {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 items per row, adjust as needed */
    gap: 5px; /* smaller gap */
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.month-year {
    display: flex;
    align-items: center;
    padding: 3px; /* reduced padding */
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.month-year input[type="checkbox"] {
    margin-right: 5px; /* smaller margin */
}

.month-year label {
    margin: 0;
    font-size: 12px; /* smaller font size */
}


                 .right-aligned {
        text-align: right;
    }
    .card-title-row {
        display: flex;          /* Enable flexbox for the container */
        justify-content: space-between; /* Spread children to each end */
        align-items: center;    /* Center items vertically */
        width: 100%;            /* Ensure the container spans the full width */
    }

    h2.card-title {
        margin: 0;             /* Remove margin to avoid unnecessary spacing */
        flex-grow: 1;          /* Allow the title to take up necessary space */
        white-space: nowrap;   /* Keep the title in a single line */
    }

    select {
        margin-left: auto;     /* Push the select box to the end of the container */
    }
    /* .question-section,
.chat-section {
    display: none;
} */
        
                    .custom-bg-light-red {
                    background-color: #FFDDDD; /* Lightest shade of red */
        }
        
        .custom-bg-light-blue {
            background-color: #DDDDFF; /* Lightest shade of blue */
        }
        
        .custom-bg-light-green {
            background-color: #DDFFDD; /* Lightest shade of green */
        }
        
        
        .custom-bg-light-yellow {
            background-color: #FFFFCC; /* Lightest shade of yellow */
        }
        .custom-text-dark-black {
            color: #000; /* Dark black color */
        }
                    .table{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 30px
                    
                  }
                  .card{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 30px
                    
                  }
                  .table2{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 30px
                    
                  }
                    .not-assigned {
            color: red;
            /* Add any other styles for not assigned here */
        }
        .future-date {
            color: red !important;
            border: 1px solid red;
        }
        
        .past-date {
            border: 1px solid green;
        }
        .increased-font {
                font-size: 20px; /* Change the font size as needed */
            }
            table {
            table-layout: auto;
            font-size: 2030px; /* Adjust the font size to your preference */
            
        }
        th, td {
                font-size: 20px; /* Adjust the font size for table headers and table data cells */
            }

</style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
            <x-navbars.navs.auth titlePage="Fatwa Detail"></x-navbars.navs.auth>
            
            <div class="container-fluid py-4">
                @php
                    $totalCounts = 0; // Initialize a variable to store the overall total count
                    $overallFolderCount = 0;
                @endphp
        <!-- Modern Loading Animation -->
        <div wire:loading>
            <div class="modern-loading">
                <div class="loading-spinner"></div>
            </div>
        </div>
        
        
            
            <!-- Modern Main Card -->
            <div class="modern-card fade-in mb-4" id="big-card-1">
                <div class="modern-card-header">
                    <h1 class="modern-card-title">
                        <i class="fas fa-file-alt text-primary"></i>
                        Fatwa Detail of {{$fatwa}}
                    </h1>
                </div>
                
                <!-- Modern Question Section -->
                <div class="question-section fade-in">
                    <div class="question-label">
                        <i class="fas fa-question-circle me-2"></i>
                        Question
                    </div>
                    @foreach ($que_day_r as $day)
                        @if (strtolower($fatwa) == strtolower($day->ifta_code))
                            <div class="question-text urdu-font">
                                سوال: {{ $day->question }}
                            </div>
                        @endif
                    @endforeach
                </div>

        
                
                <div class="card-body px-0 pb-2">
    @php
        $serialNumber_fl = 1; // Initialize the serial number    
        $colors = ['#F0FFF0', '#F0F8FF', '#FFFFE0', '#FFE4E1', '#F0F8FF', '#F5FFFA', '#E6E6FA', '#FAEBD7', '#ADD8E6']; // Add more colors as needed
    @endphp


                <!-- Modern Table Section -->
                <div class="modern-card fade-in">
                    <div class="modern-card-header">
                        <h3 class="modern-card-title">
                            <i class="fas fa-table text-info"></i>
                            Fatwa Details
                        </h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="table-container" style="width: 100%; overflow: visible;">
                            <table class="modern-table">
                                <thead>
                                    <tr>
                                        <th class="text-center col-sno">
                                            S.No
                                        </th>
                                        <th class="col-fatwa-no">
                                            Fatwa No & D/E
                                        </th>
                                        <th class="col-sayel">
                                            Sayel Info
                                        </th>
                                        <th class="text-center col-category">
                                            Category & Title
                                        </th>
                                        <th class="text-center col-rec-date">
                                            Rec Date
                                        </th>
                                        <th class="col-mujeeb">
                                            Mujeeb
                                        </th>
                                        <th class="col-mail-folder">
                                            Mail Folder
                                        </th>
                                        <th class="col-checked-date">
                                            Checked Date
                                        </th>
                                        <th class="col-checked-folder">
                                            Checked Folder
                                        </th>
                                        <th class="col-final-status">
                                            Final Status
                                        </th>
                                        <th class="col-expected-date">
                                            Expected Date
                                        </th>
                                        <th class="text-center col-actions">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                        <tbody>
                            @php $serialNumber_re = 1; @endphp
                            @php
                                $file = $remainingFatawa->last();
                            @endphp
                                                        <tr class="fade-in">
                                                            <td class="text-center col-sno">
                                                                <strong>{{ $serialNumber_re++ }}</strong>
                                                            </td>
                                                            <td class="col-fatwa-no">
                                                                <div style="color: #007bff; font-weight: bold; font-size: 13px;">{{ $file->file_code }}</div>
                                                                <div style="color: #28a745; font-size: 11px;">{{ $file->question_type }}</div>
                                                            </td>
                                                            <td class="col-sayel" style="text-align: left; padding-left: 8px;">
                                                                <div style="color: #000; font-weight: bold; font-size: 13px;">{{ $file->sayel }}</div>
                                                                <div style="color: #007bff; font-size: 11px;">Email: {{ $file->email }}</div>
                                                                <div style="color: #28a745; font-size: 11px;">Address: {{ Str::limit($file->address, 30) }}</div>
                                                                <div style="color: #dc3545; font-size: 11px;">Phone: {{ $file->phone }}</div>
                                                            </td>
                                                            <td class="col-category">
                                                                <div style="color: #007bff; font-weight: bold; font-size: 13px; margin-bottom: 2px;" class="urdu-font">{{ Str::limit($file->category, 20) }}</div>
                                                                <div style="color: #28a745; font-size: 12px;" class="urdu-font">{{ Str::limit($file->question_title, 30) }}</div>
                                                            </td>
                                                            <td class="text-center col-rec-date">
                                                                @php
                                                                    $recDate = new \DateTime($file->rec_date);
                                                                    if ($file->checked_folder === 'ok' && !is_null($file->checked_date)) {
                                                                        $checkedDate = new \DateTime($file->checked_date);
                                                                        $daysDifference = $checkedDate->diff($recDate)->days;
                                                                    } else {
                                                                        $currentDate = now();
                                                                        $daysDifference = $currentDate->diff($recDate)->days;
                                                                    }
                                                                @endphp
                                                                <div style="color: #007bff; font-weight: bold; font-size: 12px;">{{ $file->rec_date }}</div>
                                                                <div style="color: green; font-size: 13px;">{{ $daysDifference }} days</div>
                                                            </td>
                                                            <td class="text-center col-mujeeb">
                                                                @if($file->assign_id === null)
                                                                    <span style="color: red; font-weight: bold; font-size: 14px;">Not assigned</span>
                                                                @else
                                                                    <span style="color: blue; font-weight: bold; font-size: 14px;">{{ $file->assign_id }}</span>
                                                                @endif
                                                            </td>
                                            @php
    use Carbon\Carbon;
    $serialNumber_ma = 1;
    $mailFolderDates = [];
    $checkedDates = [];
    $checkedFolders = [];
    $viewLinks = [];
    $fileCodes = [];

    foreach ($remainingFatawa as $files) {
        $mailFolderDates[] = $files->mail_folder_date;
        $checkedDates[] = $files->checked_date; 
        $checkedFolders[] = $files->checked_folder;
        $fileCodes[] = $files->file_code;

        if (!is_null($files->file_code)) {
            if (!is_null($files->checked_folder)) {
                // Build the correct date parameter with checker and by_mufti info
                $baseDateParam = $files->mail_folder_date . $files->darulifta_name;
                
                if (empty($files->by_mufti)) {
                    $checkerPart = empty($files->checker) ? 'Checked' : 'Checked_by_' . $files->checker;
                } else {
                    $checkerPart = 'Checked_by_' . $files->checker . '_' . $files->by_mufti;
                }
                
                $dateParam = $baseDateParam . $checkerPart;
                
                $viewLinks[] = route('viewCheck', [
                    'date' => $dateParam,
                    'folder' => $files->checked_folder,
                    'filename' => $files->file_name
                ]);
            } else {
                $viewLinks[] = route('viewRemain', [
                    'date' => $files->mail_folder_date . $files->darulifta_name . $files->checker,
                    'filename' => $files->file_name
                ]);
            }
        } else {
            $viewLinks[] = null;
        }
    }
@endphp

<!-- Displaying Data -->
<td class="text-center col-mail-folder">
    @foreach ($mailFolderDates as $index => $date)
        <div style="display: flex; align-items: center; justify-content: center; gap: 5px; margin-bottom: 3px;">
            @if (is_null($fileCodes[$index]))
                <span class="text-warning" style="cursor: not-allowed; font-size: 12px;" data-bs-toggle="tooltip" title="Not Available">
                    <i class="fas fa-ban"></i>
                </span>
            @else
                @if (!empty($viewLinks[$index]))
                    <a href="{{ $viewLinks[$index] }}" target="_blank" style="color: green; font-size: 12px;">
                        <i class="fas fa-eye"></i>
                    </a>
                @endif
            @endif
            <span style="color: blue; font-size: 13px; font-weight: bold;">{{ Carbon::parse($date)->format('d-m-y') }}</span>
            <span style="color: red; font-size: 12px;">({{ $serialNumber_ma++ }})</span>
        </div>
    @endforeach
</td>
<td class="text-center col-checked-date">
    @foreach($checkedDates as $date)
        @if (!is_null($date))
            <div style="color: green; font-weight: bold; font-size: 14px; margin-bottom: 3px;">{{ Carbon::parse($date)->format('d-m-y') }}</div>
        @else
            <div style="color: red; font-weight: bold; font-size: 14px; margin-bottom: 3px;">Null</div>
        @endif
    @endforeach
</td>
    <td class="text-center col-checked-folder">
        @foreach($checkedFolders as $folder)
            <div style="color: blue; font-weight: bold; font-size: 14px; margin-bottom: 3px;">{{ $folder }}</div>
        @endforeach
    </td>                                                        <td class="text-center col-final-status">
                                                @foreach ($notsentiftacode as $notsent)
                                                    @if ($file->ifta_code != $notsent->ifta_code)
                                                    
                                                        @php $innerLoopBreak = false; @endphp
                                                        
                                                        @foreach ($resultstatus as $status)
                                                        @if (Str::lower($status->file_code) == Str::lower($file->ifta_code))
                                                                @if ($status->checked_folder != null)
                                                                    <div style="color: blue; font-weight: bold; font-size: 14px;">
                                                                        {{ $status->checked_folder }}
                                                                    </div>
                                                                @else
                                                                    <div style="color: green; font-weight: bold; font-size: 13px;">
                                                                        Sent for Checking
                                                                    </div>
                                                                    <div style="color: gray; font-size: 12px;">
                                                                        {{Carbon::parse($status->mail_folder_date)->format('d-m-y')}}
                                                                    </div>
                                                                @endif
                                            
                                                                @php $innerLoopBreak = true; break; @endphp
                                                            @endif
                                                        @endforeach
                                            
                                                        @if (!$innerLoopBreak)
                                                            <div style="color: red; font-weight: bold; font-size: 14px;">
                                                                Not Sent
                                                            </div>
                                                        @endif
                                            @break
                                                    @endif
                                                    
                                                @endforeach
                                                
                                            </td>
                                            <td class="text-center col-expected-date">
                                                @php
                                                    // Assuming $file->expected_date is a valid date string in the format 'Y-m-d'
                                                    $expectedDate = \Carbon\Carbon::parse($file->expected_date);
                                                    $currentDate = \Carbon\Carbon::now();
                                                    $color = ($currentDate->greaterThan($expectedDate)) ? ' future-date' : '';
                                                @endphp
                                                <div style="font-weight: bold; font-size: 14px;" class="{{$color}}">
                                                    {{ Carbon::parse($file->expected_date)->format('d-m-y') }}
                                                </div>
                                            </td>        

                                            
                                                     


                                                            <td class="text-center col-actions">
                                                                <div class="d-flex justify-content-center align-items-center gap-1">
                                                                    @if (is_null($file->file_code))
                                                                        <div class="action-btn action-btn-disabled" data-bs-toggle="tooltip" title="Not Available">
                                                                            <i class="fas fa-ban"></i>
                                                                        </div>
                                                                    @else
                                                                        @if (!is_null($file->checked_folder))
                                                                            @php
                                                                                // Build the correct date parameter with checker and by_mufti info
                                                                                $baseDateParam = $file->mail_folder_date . $file->darulifta_name;
                                                                                
                                                                                if (empty($file->by_mufti)) {
                                                                                    $checkerPart = empty($file->checker) ? 'Checked' : 'Checked_by_' . $file->checker;
                                                                                } else {
                                                                                    $checkerPart = 'Checked_by_' . $file->checker . '_' . $file->by_mufti;
                                                                                }
                                                                                
                                                                                $dateParam = $baseDateParam . $checkerPart;
                                                                            @endphp
                                                                            <a href="{{ route('viewCheck',
                                                                                ['date' => $dateParam,
                                                                                'folder' => $file->checked_folder,
                                                                                'filename' => $file->file_name]) }}" 
                                                                               target="_blank" 
                                                                               class="action-btn action-btn-view"
                                                                               data-bs-toggle="tooltip" title="View File">
                                                                                <i class="fas fa-eye"></i>
                                                                            </a>
                                                                            <a href="{{ route('downloadCheck',
                                                                                ['date' => $dateParam,
                                                                                'filename' => $file->file_name,
                                                                                'folder' => $file->checked_folder]) }}"
                                                                               class="action-btn action-btn-download"
                                                                               data-bs-toggle="tooltip" title="Download File">
                                                                                <i class="fas fa-download"></i>
                                                                            </a>
        @php
            $deleteFile = route('deleteCheckedFile', [
                'mailfolderDates' => $file->mail_folder_date,
                'daruliftaName' => $file->darulifta_name,
                'checker' => $file->checker ?? ''
            ]);

            $deleteFileFormId = 'delete-file-form-' . $file->id;
            $downloadfileByadmin = $file->downloaded_by_admin;
            $userRoles = Auth::user()->roles->pluck('name')->toArray();
            $isAdmin = in_array('Admin', $userRoles);
        @endphp

        <span class="delete">
            @if ($isAdmin)
                <a href="#" onclick="event.preventDefault(); if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('{{ $deleteFileFormId }}').submit(); }" class="text-danger">
                    <i class="fas fa-trash"></i>
                </a>
                <form id="{{ $deleteFileFormId }}" action="{{ $deleteFile }}" method="POST" class="d-none">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" name="id" value="{{ $file->id }}">
                    <input type="hidden" name="file_name" value="{{ $file->file_name }}">
                    <input type="hidden" name="file_code" value="{{ $file->file_code }}">
                    <input type="hidden" name="checked_folder" value="{{ $file->checked_folder }}">
                </form>
            @else
                <span class="text-danger" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Cannot delete, only Admins can delete.">
                    <i class="fas fa-trash mx-1"></i>
                </span>
            @endif
        </span>
    @else
        <span class="view">
            <a href="{{ route('viewRemain',
                ['date' => $file->mail_folder_date . $file->darulifta_name .$file->checker,
                'filename' => $file->file_name]) }}" target="_blank">
                <i class="fas fa-eye"></i> 
            </a>
        </span>
        <span class="download">
            <a href="{{ route('downloadFile',
                ['date' => $file->mail_folder_date . $file->darulifta_name .$file->checker,
                'filename' => $file->file_name,
                'id' => $file->id]) }}">
                <i class="fas fa-download"></i> 
            </a>
        </span>
        @php
            $deleteFile = route('deleteFile', [
                'mailfolderDates' => $file->mail_folder_date,
                'daruliftaName' => $file->darulifta_name,
                'checker' => $file->checker ?? ''
            ]);

            $deleteFileFormId = 'delete-file-form-' . $file->id;
            $downloadfileByadmin = $file->downloaded_by_admin;
            $canDeletefile = is_null($downloadfileByadmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
        @endphp

        <span class="delete">
            @if ($canDeletefile)
                <a href="#" onclick="event.preventDefault(); if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('{{ $deleteFileFormId }}').submit(); }" class="text-danger">
                    <i class="fas fa-trash"></i>
                </a>
                <form id="{{ $deleteFileFormId }}" action="{{ $deleteFile }}" method="POST" class="d-none">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" name="id" value="{{ $file->id }}">
                    <input type="hidden" name="file_name" value="{{ $file->file_name }}">
                    <input type="hidden" name="file_code" value="{{ $file->file_code }}">
                </form>
            @else
                <span class="text-danger" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Cannot delete, downloaded by admin on {{ $downloadfileByadmin }}">
                    <i class="fas fa-trash mx-1"></i>
                </span>
            @endif
        </span>
    @endif

    
    @endif
</td>

                                                    </tr>
                                                    <tr>
                                                    <td colspan="12" class="text-center">
@php
        // Use fully qualified name to avoid conflicts
        $Qr = 1;
        $Qc = 1;
        $totalDays = 0;
        $gapTotal = 0;
        $previousCheckedDate = null;
        $previousMailFolderDate = null;
        $finalGap = 0;
        $lastCheckedFolder = null;
        $isFirstIteration = true;
    @endphp

                <!-- Fatwa Trail Analysis Section -->
                <div class="container-fluid mt-4">
                    <div class="analysis-section fade-in">
                        <h2 class="analysis-title">
                            <i class="fas fa-chart-line"></i>
                            Fatwa Trail Analysis
                        </h2>

                        <div class="table-container" style="width: 100%; overflow: visible;">
                            <table class="modern-table table mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-center">
                                            <i class="fas fa-hashtag me-1"></i>#
                                        </th>
                                        <th>
                                            <i class="fas fa-calendar-plus me-1"></i>Received Date
                                        </th>
                                        <th>
                                            <i class="fas fa-calendar-check me-1"></i>Checked Date
                                        </th>
                                        <th>
                                            <i class="fas fa-clock me-1"></i>Processing Days
                                        </th>
                                        <th>
                                            <i class="fas fa-hourglass-half me-1"></i>Gap Days
                                        </th>
                                    </tr>
                                </thead>
            <tbody>
                @foreach ($allfatawa as $fatawa)
                    @if ($file->file_code == $fatawa->file_code)
                        @php
                            $mailFolderDate = \Carbon\Carbon::parse($fatawa->mail_folder_date);
                            $checkedDate = $fatawa->checked_date ? \Carbon\Carbon::parse($fatawa->checked_date) : null;
                            $daysDifference1 = $checkedDate ? $mailFolderDate->diffInDays($checkedDate) : '-';
                            if ($checkedDate) {
                                $totalDays += $daysDifference1;
                            }

                            $gap = $previousCheckedDate ? $previousCheckedDate->diffInDays($mailFolderDate) : 0;
                            if ($previousCheckedDate && !$isFirstIteration) {
                                $gapTotal += $gap;
                            }

                            $previousCheckedDate = $checkedDate;
                            $previousMailFolderDate = $mailFolderDate;
                            $lastCheckedFolder = $fatawa->checked_folder;
                            $isFirstIteration = false;
                        @endphp

                        <tr class="fade-in">
                            <td class="text-center">
                                <div class="status-badge status-info">{{ $Qr++ }}</div>
                            </td>
                            <td>
                                <div class="info-card">
                                    <div class="info-label">Received</div>
                                    <div class="info-value text-primary">{{ $mailFolderDate->format('d-m-Y') }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="info-card">
                                    <div class="info-label">Checked</div>
                                    <div class="info-value {{ $checkedDate ? 'text-success' : 'text-danger' }}">
                                        {{ $checkedDate ? $checkedDate->format('d-m-Y') : 'Not Checked' }}
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                @if($daysDifference1 !== '-')
                                    <div class="status-badge {{ $daysDifference1 > 7 ? 'status-danger' : ($daysDifference1 > 3 ? 'status-warning' : 'status-success') }}">
                                        {{ $daysDifference1 }} days
                                    </div>
                                @else
                                    <div class="status-badge status-danger">Pending</div>
                                @endif
                            </td>
                            <td class="text-center">
                                @if($gap > 0 && !$isFirstIteration)
                                    <div class="status-badge status-warning">{{ $gap }} days</div>
                                @else
                                    <div class="status-badge status-success">0 days</div>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Summary Cards -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="modern-card text-center">
                <div class="modern-card-body">
                    <i class="fas fa-calendar-alt text-primary fa-2x mb-2"></i>
                    <h5 class="text-primary">Total Days</h5>
                    <h3 class="fw-bold">{{ $totalDays }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="modern-card text-center">
                <div class="modern-card-body">
                    <i class="fas fa-hourglass-half text-warning fa-2x mb-2"></i>
                    <h5 class="text-warning">Gap Days</h5>
                    <h3 class="fw-bold">{{ $gapTotal }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="modern-card text-center">
                <div class="modern-card-body">
                    <i class="fas fa-chart-line text-success fa-2x mb-2"></i>
                    <h5 class="text-success">Efficiency</h5>
                    <h3 class="fw-bold">{{ $totalDays > 0 ? round((($totalDays - $gapTotal) / $totalDays) * 100, 1) : 0 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="modern-card text-center">
                <div class="modern-card-body">
                    <i class="fas fa-flag-checkered text-info fa-2x mb-2"></i>
                    <h5 class="text-info">Status</h5>
                    <h3 class="fw-bold">{{ $lastCheckedFolder ?? 'Pending' }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analysis Summary -->
    <div class="mt-4">
        <div class="modern-card">
            <div class="modern-card-header">
                <h4 class="modern-card-title">
                    <i class="fas fa-calculator text-info"></i>
                    Detailed Analysis Summary
                </h4>
            </div>
            <div class="modern-card-body">
                @php
                    // Calculate final gap if necessary
                    $finalGap = 0;
                    if ($previousCheckedDate && $lastCheckedFolder !== 'ok') {
                        $currentDate = \Carbon\Carbon::now();
                        $finalGap = $previousCheckedDate->diffInDays($currentDate);
                    }
                    
                    // Calculate total of all components
                    $grandTotal = $totalDays + $gapTotal + ($lastCheckedFolder !== 'ok' ? intval($finalGap) : 0);
                @endphp

                <div class="row">
                    <div class="col-md-6">
                        <div class="analysis-summary-item">
                            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                                <span class="fw-bold text-primary">
                                    <i class="fas fa-clock me-2"></i>Last Gap Held By Mujeeb:
                                </span>
                                <span class="badge bg-warning fs-6">
                                    @if($lastCheckedFolder !== 'ok' && $finalGap > 0)
                                        {{ $finalGap }} days
                                    @else
                                        0 days
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="analysis-summary-item">
                            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                                <span class="fw-bold text-success">
                                    <i class="fas fa-calendar-check me-2"></i>Total Days Between Rec & Checked:
                                </span>
                                <span class="badge bg-success fs-6">{{ $totalDays }} days</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="analysis-summary-item">
                            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                                <span class="fw-bold text-warning">
                                    <i class="fas fa-hourglass-half me-2"></i>Gap Days Held By Mujeeb:
                                </span>
                                <span class="badge bg-warning fs-6">{{ $gapTotal }} days</span>
                            </div>
                        </div>

                        <div class="analysis-summary-item">
                            <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                                <span class="fw-bold text-info fs-5">
                                    <i class="fas fa-calculator me-2"></i>Total (Days + Gap Days @if($lastCheckedFolder !== 'ok' && $finalGap > 0) + Last Gap @endif):
                                </span>
                                <span class="badge bg-info fs-5">{{ $grandTotal }} days</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Summary Information -->
                <div class="mt-4 p-3 bg-light rounded">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value text-primary">{{ $totalDays }}</div>
                                <div class="stat-label">Processing Days</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value text-warning">{{ $gapTotal }}</div>
                                <div class="stat-label">Gap Days</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value text-danger">
                                    @if($lastCheckedFolder !== 'ok' && $finalGap > 0)
                                        {{ $finalGap }}
                                    @else
                                        0
                                    @endif
                                </div>
                                <div class="stat-label">Last Gap</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value text-info fs-4 fw-bold">{{ $grandTotal }}</div>
                                <div class="stat-label fw-bold">Grand Total</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

                </div>
            </div>
        </main>

    <!-- Initialize tooltips and fix sidebar -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Simple main content positioning
            function ensureMainContentPosition() {
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.style.marginLeft = '270px';
                    mainContent.style.transition = 'none';
                }
            }

            // Ensure positioning immediately and after page load
            ensureMainContentPosition();
            setTimeout(ensureMainContentPosition, 100);

            // Ensure main content stays in proper position
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.marginLeft = '270px';
                mainContent.style.width = 'calc(100% - 270px)';
            }

            // Override any Material Dashboard JavaScript that might affect sidebar
            if (window.bootstrap && window.bootstrap.Collapse) {
                const collapseElements = document.querySelectorAll('#sidenav-collapse-main');
                collapseElements.forEach(function(element) {
                    element.style.display = 'block';
                    element.style.width = 'auto';
                });
            }
        });

            // Ensure main content positioning on window resize
            window.addEventListener('resize', function() {
                ensureMainContentPosition();
            });
    </script>
</div>
</tr>
                            
                        </tbody>
                    </table>
                    </div>
        </div>
    

            </div>
        
   
   

   <script>
    document.addEventListener('DOMContentLoaded', function () {
    const checkboxes = document.querySelectorAll('.calendar-style input[type="checkbox"]');

    // Example: Adding an event listener to each checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if(this.checked) {
                console.log(this.value + ' is checked');
            } else {
                console.log(this.value + ' is unchecked');
            }
        });
    });
});

   </script>
 
    <x-footers.auth></x-footers.auth>

</main>
<x-plugins></x-plugins>



</div>

