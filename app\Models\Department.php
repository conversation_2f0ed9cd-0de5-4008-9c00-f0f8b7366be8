<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the users assigned to this department.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_departments')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get the tasks assigned to this department.
     */
    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    /**
     * Get department supervisor/assistant assignments.
     */
    public function departmentAssignments()
    {
        return $this->hasMany(DepartmentSupervisorAssistant::class);
    }

    /**
     * Get active department supervisor/assistant assignments.
     */
    public function activeDepartmentAssignments()
    {
        return $this->hasMany(DepartmentSupervisorAssistant::class)->active();
    }

    /**
     * Get supervisors assigned to this department.
     */
    public function supervisors()
    {
        return $this->belongsToMany(User::class, 'department_supervisor_assistants')
                    ->select('users.id', 'users.name', 'users.email')
                    ->wherePivot('role_type', DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type')
                    ->withTimestamps();
    }

    /**
     * Get assistants assigned to this department.
     */
    public function assistants()
    {
        return $this->belongsToMany(User::class, 'department_supervisor_assistants')
                    ->select('users.id', 'users.name', 'users.email')
                    ->wherePivot('role_type', DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type', 'supervisor_id')
                    ->withTimestamps();
    }

    /**
     * Get all team members (supervisors + assistants) for this department.
     */
    public function teamMembers()
    {
        return $this->belongsToMany(User::class, 'department_supervisor_assistants')
                    ->select('users.id', 'users.name', 'users.email')
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type', 'supervisor_id')
                    ->withTimestamps();
    }

    /**
     * Scope to get only active departments.
     */
    public function scopeActive($query)
    {
        return $query->where('departments.is_active', true);
    }

    /**
     * Check if department can be safely deleted.
     */
    public function canBeDeleted()
    {
        // Check old system assignments
        if ($this->users()->count() > 0) {
            return false;
        }

        // Check new system assignments
        if ($this->activeDepartmentAssignments()->count() > 0) {
            return false;
        }

        // Check if department has tasks
        if ($this->tasks()->count() > 0) {
            return false;
        }

        return true;
    }

    /**
     * Get the reason why department cannot be deleted.
     */
    public function getDeletionBlockReason()
    {
        $reasons = [];

        if ($this->users()->count() > 0) {
            $reasons[] = $this->users()->count() . ' user assignment(s)';
        }

        if ($this->activeDepartmentAssignments()->count() > 0) {
            $reasons[] = $this->activeDepartmentAssignments()->count() . ' team assignment(s)';
        }

        if ($this->tasks()->count() > 0) {
            $taskCount = $this->tasks()->count();
            $reasons[] = $taskCount . ' task' . ($taskCount > 1 ? 's' : '');
        }

        return empty($reasons) ? null : 'Cannot delete: Department has ' . implode(', ', $reasons) . '. Please remove them first.';
    }
}
