<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Workflow Task Management</h5>
                            <p class="text-sm mb-0">Manage tasks and assignments across departments</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <div class="btn-group me-2" role="group">
                                    <a href="{{ route('departments.index') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-building"></i>&nbsp;&nbsp;Departments
                                    </a>
                                    <a href="{{ route('supervisor-assistant-mapping') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-users"></i>&nbsp;&nbsp;Team Mapping
                                    </a>
                                </div>
                                <button wire:click="toggleViewMode" class="btn btn-outline-info btn-sm mb-0 me-2">
                                    <i class="fas fa-{{ $viewMode === 'list' ? 'sitemap' : 'list' }}"></i>&nbsp;&nbsp;{{ $viewMode === 'list' ? 'Hierarchy View' : 'List View' }}
                                </button>
                                <button wire:click="openCreateModal" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-plus"></i>&nbsp;&nbsp;Create Task
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search tasks...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterStatus" class="form-select">
                                    <option value="all">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterDepartment" class="form-select">
                                    <option value="all">All Departments</option>
                                    @foreach($departments as $department)
                                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterAssignedTo" class="form-select">
                                    <option value="all">All Users</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterPriority" class="form-select">
                                    <option value="all">All Priorities</option>
                                    <option value="3">High</option>
                                    <option value="2">Medium</option>
                                    <option value="1">Low</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                                                <select wire:model.live="filterType" class="form-select">
                                    <option value="all">All Types</option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="operational">Operational</option>
                                    <option value="one-time">One-time Task</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterRole" class="form-select">
                                    <option value="all">All Roles</option>
                                    <option value="superior">Superior</option>
                                    <option value="assistant">Assistant</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <div class="text-sm text-secondary">
                                    {{ $tasks->total() }} tasks
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks Table -->
    @if($viewMode === 'list')
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-0 pb-2">
                                        <div class="p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task #</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Assigned To</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Dept/Type</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Reports</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tasks as $task)
                                <tr>
                                    <td class="p-1">
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-xs font-weight-bold">{{ $task->task_number }}</h6>
                                                @if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1)
                                                    <p class="text-xs text-info mb-0">
                                                        <i class="fas fa-users"></i> Group
                                                    </p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-1">
                                        <div class="px-2 py-1">
                                            <h6 class="mb-0 text-xs text-truncate" title="{{ $task->title }}">{{ $task->title }}</h6>
                                            @if($task->description)
                                                <p class="text-xs text-secondary mb-0 text-truncate" title="{{ $task->description }}">{{ $task->description }}</p>
                                            @endif
                                            <span class="badge badge-xs bg-gradient-{{ $task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary') }}">
                                                {{ ucfirst($task->type) }}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="p-1">
                                        @if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1)
                                            <!-- Grouped tasks -->
                                            <div class="px-2 py-1">
                                                @foreach($task->grouped_tasks->take(2) as $groupedTask)
                                                <div class="d-flex align-items-center mb-1" title="{{ $groupedTask->assignedTo->name }} ({{ ucfirst($groupedTask->role_type) }})">
                                                    <div class="avatar avatar-xs bg-gradient-{{ $groupedTask->role_type === 'superior' ? 'success' : 'info' }} me-1">
                                                        <span class="text-white text-xs">{{ strtoupper(substr($groupedTask->assignedTo->name, 0, 1)) }}</span>
                                                    </div>
                                                    <span class="text-xs text-truncate">{{ $groupedTask->assignedTo->name }}</span>
                                                </div>
                                                @endforeach
                                                @if($task->grouped_tasks->count() > 2)
                                                <span class="text-xs text-muted">+{{ $task->grouped_tasks->count() - 2 }}</span>
                                                @endif
                                            </div>
                                        @else
                                            <!-- Single assignee -->
                                            <div class="px-2 py-1" title="{{ $task->assignedTo->name }} - {{ $task->assignedTo->email }}">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-xs bg-gradient-info me-1">
                                                        <span class="text-white text-xs">{{ strtoupper(substr($task->assignedTo->name, 0, 1)) }}</span>
                                                    </div>
                                                    <div>
                                                        <p class="text-xs font-weight-bold mb-0 text-truncate">{{ $task->assignedTo->name }}</p>
                                                        <span class="badge badge-xs bg-gradient-{{ $task->assignedTo->isSuperior() ? 'success' : 'info' }}">
                                                            {{ $task->assignedTo->isSuperior() ? 'Sup' : 'Ast' }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center text-xs p-1">
                                        @if($task->type === 'operational')
                                            <span class="badge badge-sm bg-gradient-primary">Op</span>
                                        @elseif($task->department)
                                            <span class="badge badge-sm bg-gradient-secondary text-truncate">{{ $task->department->name }}</span>
                                        @else
                                            <span class="text-secondary">-</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center p-1">
                                        <span class="badge badge-sm bg-gradient-{{ $this->getTaskPriorityColor($task->priority) }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center p-1">
                                        <div class="dropdown">
                                            <span class="badge badge-sm bg-gradient-{{ $this->getTaskStatusColor($task->status) }} dropdown-toggle" 
                                                  data-bs-toggle="dropdown" style="cursor: pointer;">
                                                {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                            </span>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus({{ $task->id }}, 'pending')">Pending</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus({{ $task->id }}, 'in_progress')">In Progress</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus({{ $task->id }}, 'completed')">Completed</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus({{ $task->id }}, 'cancelled')">Cancelled</a></li>
                                                
                                            </ul>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center text-xs p-1">
                                        @if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1)
                                            @php
                                                $totalPerformances = $task->grouped_tasks->sum('total_performances_count');
                                                $submittedPerformances = $task->grouped_tasks->sum('submitted_performances_count');
                                            @endphp
                                        @else
                                            @php
                                                $totalPerformances = $task->total_performances_count ?? 0;
                                                $submittedPerformances = $task->submitted_performances_count ?? 0;
                                            @endphp
                                        @endif

                                        <div class="text-center">
                                            <span class="text-xs font-weight-bold text-{{ $submittedPerformances > 0 ? 'success' : 'secondary' }}">
                                                {{ $submittedPerformances }}/{{ $totalPerformances }}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center text-xs p-1">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $task->due_date->format('M d, Y') }}
                                        </span>
                                        @if($task->due_date->isPast() && $task->status !== 'completed')
                                            <br><span class="badge badge-sm bg-gradient-danger">Overdue</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center p-1">
                                        <div class="d-flex justify-content-center text-nowrap">
                                            <button wire:click="openEditModal({{ $task->id }})"
                                                    class="btn btn-xs btn-outline-primary me-1 mb-0" title="Edit Task">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button wire:click="deleteTask({{ $task->id }})"
                                                    wire:confirm="@if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1)Are you sure you want to delete this task group? This will delete all {{ $task->grouped_tasks->count() }} related tasks.@elseAre you sure you want to delete this task?@endif"
                                                    class="btn btn-xs btn-outline-danger mb-0" title="Delete Task">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-tasks fa-3x text-secondary mb-3"></i>
                                            <h6 class="text-secondary">No tasks found</h6>
                                            <p class="text-sm text-secondary">Create your first task to get started.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    @if($tasks->hasPages())
                    <div class="px-3 py-2">
                        {{ $tasks->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Hierarchy View -->
    @if($viewMode === 'hierarchy')
    <div class="row mt-4">
        <div class="col-12">
            @forelse($hierarchyData as $hierarchyItem)
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-lg bg-gradient-success me-3">
                            <span class="text-white font-weight-bold">
                                {{ strtoupper(substr($hierarchyItem['supervisor']->name, 0, 2)) }}
                            </span>
                        </div>
                        <div>
                            <h6 class="mb-0">{{ $hierarchyItem['supervisor']->name }}</h6>
                            <p class="text-sm text-secondary mb-0">
                                <span class="badge badge-sm bg-gradient-success">Superior</span>
                                {{ $hierarchyItem['supervisor']->email }}
                            </p>
                        </div>
                        <div class="ms-auto">
                            <span class="badge badge-lg bg-gradient-info">
                                {{ $hierarchyItem['supervisor_tasks']->count() }} Tasks
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Superior Tasks -->
                    @if($hierarchyItem['supervisor_tasks']->count() > 0)
                    <div class="mb-4">
                        <h6 class="text-sm font-weight-bold mb-2">Superior Tasks</h6>
                        <div class="row">
                            @foreach($hierarchyItem['supervisor_tasks'] as $task)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                            <span class="badge badge-sm bg-gradient-{{ $this->getTaskStatusColor($task->status) }}">
                                                {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                            </span>
                                        </div>
                                        @if($task->description)
                                            <p class="text-xs text-secondary mb-2">{{ Str::limit($task->description, 80) }}</p>
                                        @endif
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="badge badge-sm bg-gradient-{{ $this->getTaskPriorityColor($task->priority) }}">
                                                    {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                                </span>
                                                @if($task->department)
                                                    <span class="badge badge-sm bg-gradient-secondary">{{ $task->department->name }}</span>
                                                @endif
                                            </div>
                                            <small class="text-secondary">{{ $task->due_date->format('M d') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Assistant Tasks -->
                    @if(count($hierarchyItem['assistants']) > 0)
                    <div>
                        <h6 class="text-sm font-weight-bold mb-3">Assistant Tasks</h6>
                        @foreach($hierarchyItem['assistants'] as $assistantData)
                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <div class="avatar avatar-sm bg-gradient-info me-2">
                                    <span class="text-white text-xs font-weight-bold">
                                        {{ strtoupper(substr($assistantData['assistant']->name, 0, 2)) }}
                                    </span>
                                </div>
                                <div>
                                    <p class="text-sm font-weight-bold mb-0">{{ $assistantData['assistant']->name }}</p>
                                    <p class="text-xs text-secondary mb-0">
                                        <span class="badge badge-sm bg-gradient-info">Assistant</span>
                                        {{ $assistantData['tasks']->count() }} Tasks
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                @foreach($assistantData['tasks'] as $task)
                                <div class="col-md-4 mb-2">
                                    <div class="card border-light">
                                        <div class="card-body p-2">
                                            <div class="d-flex justify-content-between align-items-start mb-1">
                                                <h6 class="mb-0 text-xs">{{ Str::limit($task->title, 30) }}</h6>
                                                <span class="badge badge-sm bg-gradient-{{ $this->getTaskStatusColor($task->status) }}">
                                                    {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge badge-sm bg-gradient-{{ $this->getTaskPriorityColor($task->priority) }}">
                                                    {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                                </span>
                                                <small class="text-secondary">{{ $task->due_date->format('M d') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>
            @empty
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-sitemap fa-3x text-secondary mb-3"></i>
                    <h6 class="text-secondary">No Superior/Assistant hierarchy found</h6>
                    <p class="text-sm text-secondary">Create tasks with Superior/Assistant assignments to see the hierarchy view.</p>
                </div>
            </div>
            @endforelse
        </div>
    </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Create Task Modal -->
    @if($showCreateModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Task</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="createTask">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror" placeholder="Enter task title">
                                    @error('title') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Type *</label>
                                    <select wire:model="type" class="form-select @error('type') is-invalid @enderror" @if($assignment_type === 'operational') disabled @endif>
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="operational">Operational</option>
                                        <option value="one-time">One-time</option>

                                    </select>
                                    @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    @if($assignment_type === 'operational')
                                        <small class="text-muted">Operational tasks are automatically set to operational type</small>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select wire:model.live="department_id" class="form-select @error('department_id') is-invalid @enderror" @if($assignment_type === 'operational') disabled @endif>
                                        <option value="">Select Department</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}">{{ $department->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    @if($assignment_type === 'operational')
                                        <small class="text-muted">Operational tasks are not department-related</small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assignment Type *</label>
                                    <select wire:model.live="assignment_type" class="form-select">
                                        <option value="direct">Direct Assignment</option>
                                        @if(auth()->user()->isNazim())
                                            <option value="superior_assistant">Superior → Assistant</option>
                                            <option value="operational">Operational Task</option>
                                        @endif
                                    </select>
                                    @if($assignment_type === 'operational')
                                        <small class="text-muted">Operational tasks are not department-related and can be assigned to Superior/Assistant users</small>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($assignment_type === 'direct')
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                        <option value="">Select User</option>
                                        @if($department_id)
                                            @foreach($this->getDepartmentTeamMembers($department_id) as $member)
                                                <option value="{{ $member->id }}">{{ $member->name }} ({{ $member->email }})</option>
                                            @endforeach
                                        @else
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($assignment_type === 'superior_assistant')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Superior *</label>
                                    <select wire:model.live="superior_id" class="form-select @error('superior_id') is-invalid @enderror">
                                        <option value="">Select Superior</option>
                                        @foreach($this->getDepartmentSuperiors() as $superior)
                                            <option value="{{ $superior->id }}">{{ $superior->name }} ({{ $superior->email }})</option>
                                        @endforeach
                                    </select>
                                    @error('superior_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    @if($department_id)
                                        <small class="text-muted">Showing superiors from selected department only</small>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assistant (Optional)</label>
                                    <select wire:model="assistant_id" class="form-select @error('assistant_id') is-invalid @enderror">
                                        <option value="">Select Assistant</option>
                                        @foreach($this->getDepartmentAssistants() as $assistant)
                                            <option value="{{ $assistant->id }}">{{ $assistant->name }} ({{ $assistant->email }})</option>
                                        @endforeach
                                    </select>
                                    @error('assistant_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    <small class="text-muted">
                                        @if($superior_id)
                                            Showing assistants assigned to selected superior
                                        @else
                                            Select a superior to see available assistants
                                        @endif
                                    </small>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($assignment_type === 'operational')
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror" style="max-height: 200px; overflow-y: auto;">
                                        <option value="">Select User ({{ $this->getOperationalTaskUsers()->count() }} available)</option>
                                        @foreach($this->getOperationalTaskUsers() as $user)
                                            <option value="{{ $user->id }}">
                                                {{ $user->name }}
                                                @if($user->isSuperior())
                                                    (Superior)
                                                @elseif($user->isMujeeb())
                                                    (Assistant)
                                                @else
                                                    (User)
                                                @endif
                                                - {{ $user->email }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Operational tasks can be assigned to any user ({{ $this->getOperationalTaskUsers()->count() }} users available).
                                        Use the dropdown to scroll through all users.
                                    </small>
                                </div>
                            </div>
                        </div>
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Priority *</label>
                                    <select wire:model="priority" class="form-select @error('priority') is-invalid @enderror">
                                        <option value="1">Low</option>
                                        <option value="2">Medium</option>
                                        <option value="3">High</option>
                                    </select>
                                    @error('priority') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Due Date *</label>
                                    <input type="date" wire:model="due_date" class="form-control @error('due_date') is-invalid @enderror">
                                    @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control @error('description') is-invalid @enderror" rows="3" placeholder="Enter task description"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn bg-gradient-primary" wire:click="createTask">
                        <i class="fas fa-plus"></i> Create Task
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Edit Task Modal -->
    @if($showEditModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Task</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="updateTask">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror" placeholder="Enter task title">
                                    @error('title') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Type *</label>
                                    <select wire:model="type" class="form-select @error('type') is-invalid @enderror">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="operational">Operational</option>
                                        <option value="one-time">One-time Task</option>
                                    </select>
                                    @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select wire:model.live="department_id" class="form-select @error('department_id') is-invalid @enderror">
                                        <option value="">Select Department</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}">{{ $department->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                        <option value="">Select User</option>
                                        @if($department_id)
                                            @foreach($this->getDepartmentTeamMembers($department_id) as $member)
                                                <option value="{{ $member->id }}">{{ $member->name }} ({{ $member->email }})</option>
                                            @endforeach
                                        @else
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Priority *</label>
                                    <select wire:model="priority" class="form-select @error('priority') is-invalid @enderror">
                                        <option value="1">Low</option>
                                        <option value="2">Medium</option>
                                        <option value="3">High</option>
                                    </select>
                                    @error('priority') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Due Date *</label>
                                    <input type="date" wire:model="due_date" class="form-control @error('due_date') is-invalid @enderror">
                                    @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control @error('description') is-invalid @enderror" rows="3" placeholder="Enter task description"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn bg-gradient-primary" wire:click="updateTask">
                        <i class="fas fa-save"></i> Update Task
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>