<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class TransferFatwa extends Component
{
    public $fatwaId;
    public $fatwa;
    public $newSender;
    public $mujeebs = [];
    public $successMessage;
    public $errorMessage;

    public function mount($fatwaId)
    {
        // Check if user is admin, mujeeb, or has Darulifta role
        $userRoles = Auth::user()->roles->pluck('name')->toArray();
        $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', '<PERSON><PERSON><PERSON>-e-<PERSON><PERSON>', 'Markaz-ul-Iqtisaad'];
        $hasDaruliftaRole = !empty(array_intersect($userRoles, $daruliftaRoles));
        
        if (!Auth::check() || (!in_array('Admin', $userRoles) && !Auth::user()->isMujeeb() && !$hasDaruliftaRole)) {
            return redirect()->route('dashboard');
        }

        $this->fatwaId = $fatwaId;
        $this->loadFatwa();
        $this->loadMujeebs();
    }

    private function loadFatwa()
    {
        $this->fatwa = DB::table('uploaded_files')
            ->where('id', $this->fatwaId)
            ->first();

        if (!$this->fatwa) {
            $this->errorMessage = 'Fatwa not found.';
        }
    }

    private function loadMujeebs()
    {
        // Get all mujeebs from mujeebs table (same as mujeeb page)
        $this->mujeebs = DB::table('mujeebs')
            ->select('mujeeb_name', 'darul_name', 'munsab')
            ->when($this->fatwa, function($query) {
                // Exclude the current sender
                return $query->where('mujeeb_name', '!=', $this->fatwa->sender);
            })
            ->orderBy('mujeeb_name')
            ->get()
            ->toArray();
    }

    public function transferFatwa()
    {
        $this->validate([
            'newSender' => 'required|string',
        ], [
            'newSender.required' => 'Please select a sender to transfer the fatwa to.'
        ]);

        if (!$this->fatwa) {
            $this->errorMessage = 'Fatwa not found.';
            return;
        }

        if ($this->newSender === $this->fatwa->sender) {
            $this->errorMessage = 'Cannot transfer to the same sender.';
            return;
        }

        try {
            DB::beginTransaction();

            // Create a new entry in the uploaded_files table
            DB::table('uploaded_files')->insert([
                'file_name' => $this->fatwa->file_name,
                'file_code' => $this->fatwa->file_code,
                'darulifta_name' => $this->fatwa->darulifta_name,
                'mail_folder_date' => $this->fatwa->mail_folder_date,
                'mail_recived_date' => $this->fatwa->mail_recived_date,
                'ftype' => $this->fatwa->ftype,
                'category' => $this->fatwa->category,
                'transfer_by' => $this->fatwa->sender,
                'sender' => $this->newSender,
                'selected' => 2,
                'checked_folder' => 'Assigned',
                'file_created_date' => $this->fatwa->file_created_date ?? now()->toDateString(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Update the questions table where ifta_code matches file_code
            DB::table('questions')
                ->where('ifta_code', $this->fatwa->file_code)
                ->update([
                    'assign_id' => $this->newSender,
                    'updated_at' => now()
                ]);

            DB::commit();

            $this->successMessage = 'Fatwa has been successfully transferred.';
            
            // Redirect to the transferred fatawa page after a short delay
            return redirect()->route('transferred-fatawa')->with('message', 'Fatwa transferred successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorMessage = 'Error transferring fatwa: ' . $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.transfer-fatwa');
    }
}
