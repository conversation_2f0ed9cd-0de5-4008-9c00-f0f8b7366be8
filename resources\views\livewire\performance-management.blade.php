<div class="container-fluid py-4">







    <!-- Main Performance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Performance Management</h5>
                            <p class="text-sm mb-0">Monitor and manage daily performance reports</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <button wire:click="$set('viewMode', 'hierarchical')" 
                                        class="btn {{ $viewMode === 'hierarchical' ? 'bg-gradient-primary' : 'btn-outline-primary' }} btn-sm mb-0 me-2">
                                    <i class="fas fa-sitemap"></i>&nbsp;&nbsp;Hierarchical
                                </button>
                                <button wire:click="$set('viewMode', 'list')" 
                                        class="btn {{ $viewMode === 'list' ? 'bg-gradient-primary' : 'btn-outline-primary' }} btn-sm mb-0 me-2">
                                    <i class="fas fa-list"></i>&nbsp;&nbsp;List View
                                </button>
                                <button wire:click="exportReport" class="btn bg-gradient-success btn-sm mb-0">
                                    <i class="fas fa-download"></i>&nbsp;&nbsp;Export Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4 mb-3">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Date Mode</label>
                                <select wire:model.live="dateRangeMode" class="form-select form-select-sm">
                                    <option value="single">Single Date</option>
                                    <option value="range">Date Range</option>
                                </select>
                            </div>
                        </div>
                        @if($dateRangeMode === 'single')
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Date</label>
                                <input wire:model.live="selectedDate" type="date" class="form-control form-control-sm">
                            </div>
                        </div>
                        @else
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Start Date</label>
                                <input wire:model.live="selectedStartDate" type="date" class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">End Date</label>
                                <input wire:model.live="selectedEndDate" type="date" class="form-control form-control-sm">
                            </div>
                        </div>
                        @endif
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">User</label>
                                <select wire:model.live="selectedUser" class="form-select form-select-sm">
                                    <option value="all">All Users</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Department</label>
                                <select wire:model.live="selectedDepartment" class="form-select form-select-sm">
                                    <option value="all">All Departments</option>
                                    @foreach($departments as $department)
                                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Status</label>
                                <select wire:model.live="selectedStatus" class="form-select form-select-sm">
                                    <option value="all">All Status</option>
                                    <option value="submitted">Submitted</option>
                                    <option value="pending">Pending</option>
                                    <option value="overdue">Overdue</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Rating</label>
                                <select wire:model.live="selectedRating" class="form-select form-select-sm">
                                    <option value="all">All Ratings</option>
                                    <option value="excellent">Excellent</option>
                                    <option value="good">Good</option>
                                    <option value="fair">Fair</option>
                                    <option value="poor">Poor</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                @if(!empty($summaryData))
                <div class="card-body px-4 pt-0 pb-3">
                    <div class="row g-3">
                        <div class="col-xl-2 col-md-4 col-sm-6">
                            <div class="bg-white rounded-3 p-3 text-center shadow-sm border">
                                <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                    <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">people</i>
                                </div>
                                <h4 class="mb-0 text-primary">{{ $summaryData['total_users'] ?? 0 }}</h4>
                                <span class="text-xs text-secondary">
                                    @if(($summaryData['user_role'] ?? '') === 'nazim')
                                        Total Users
                                    @elseif(($summaryData['user_role'] ?? '') === 'superior')
                                        My Team
                                    @else
                                        My Reports
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 col-sm-6">
                            <div class="bg-white rounded-3 p-3 text-center shadow-sm border">
                                <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                    <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">check_circle</i>
                                </div>
                                <h4 class="mb-0 text-success">{{ $summaryData['submitted'] ?? 0 }}</h4>
                                <span class="text-xs text-secondary">
                                    @if(($summaryData['user_role'] ?? '') === 'nazim')
                                        Total Submitted
                                    @elseif(($summaryData['user_role'] ?? '') === 'superior')
                                        Team Submitted
                                    @else
                                        My Submitted
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 col-sm-6">
                            <div class="bg-white rounded-3 p-3 text-center shadow-sm border">
                                <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                    <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">pending</i>
                                </div>
                                <h4 class="mb-0 text-warning">{{ $summaryData['pending'] ?? 0 }}</h4>
                                <span class="text-xs text-secondary">
                                    @if(($summaryData['user_role'] ?? '') === 'nazim')
                                        Total Pending
                                    @elseif(($summaryData['user_role'] ?? '') === 'superior')
                                        Team Pending
                                    @else
                                        My Pending
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 col-sm-6">
                            <div class="bg-white rounded-3 p-3 text-center shadow-sm border">
                                <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                    <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">schedule</i>
                                </div>
                                <h4 class="mb-0 text-danger">{{ $summaryData['overdue'] ?? 0 }}</h4>
                                <span class="text-xs text-secondary">
                                    @if(($summaryData['user_role'] ?? '') === 'nazim')
                                        Total Overdue
                                    @elseif(($summaryData['user_role'] ?? '') === 'superior')
                                        Team Overdue
                                    @else
                                        My Overdue
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 col-sm-6">
                            <div class="bg-white rounded-3 p-3 text-center shadow-sm border">
                                <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                    <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">trending_up</i>
                                </div>
                                <h4 class="mb-0 text-info">{{ $summaryData['submission_rate'] ?? 0 }}%</h4>
                                <span class="text-xs text-secondary">
                                    @if(($summaryData['user_role'] ?? '') === 'nazim')
                                        Overall Rate
                                    @elseif(($summaryData['user_role'] ?? '') === 'superior')
                                        Team Rate
                                    @else
                                        My Rate
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 col-sm-6">
                            <div class="bg-white rounded-3 p-3 text-center shadow-sm border">
                                <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                    <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">access_time</i>
                                </div>
                                <h4 class="mb-0 text-secondary">{{ $summaryData['avg_hours'] ?? 0 }}h</h4>
                                <span class="text-xs text-secondary">
                                    @if(($summaryData['user_role'] ?? '') === 'nazim')
                                        Avg Hours
                                    @elseif(($summaryData['user_role'] ?? '') === 'superior')
                                        Team Avg Hours
                                    @else
                                        My Avg Hours
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Performance Table -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Task #</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hours</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($performances as $performance)
                                <tr class="{{ $performance->is_overdue ? 'table-warning' : '' }}">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $performance->user->name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $performance->user->email }}</p>
                                                @if($performance->user->isSuperior())
                                                    <span class="badge badge-xs bg-gradient-success">Superior</span>
                                                @elseif($performance->user->isMujeeb())
                                                    <span class="badge badge-xs bg-gradient-info">Assistant</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm font-weight-bold">{{ $performance->task_number ?? $performance->task->task_number }}</h6>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $performance->task->title }}</h6>
                                            <p class="text-xs text-secondary mb-0">{{ $performance->task->type }} task</p>
                                            @if($performance->task->department)
                                                <span class="badge badge-xs bg-gradient-secondary">{{ $performance->task->department->name }}</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ Carbon\Carbon::parse($performance->performance_date)->format('M d, Y') }}</p>
                                        @if($performance->submitted_at)
                                            <p class="text-xs text-secondary mb-0">{{ Carbon\Carbon::parse($performance->submitted_at)->format('h:i A') }}</p>
                                        @endif
                                        @if($performance->is_overdue)
                                            <span class="badge badge-xs bg-gradient-danger">Overdue</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center text-sm">
                                        <span class="badge badge-sm bg-gradient-{{ $performance->is_submitted ? 'success' : ($performance->is_overdue ? 'danger' : 'warning') }}">
                                            {{ $performance->is_submitted ? 'Submitted' : ($performance->is_overdue ? 'Overdue' : 'Pending') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->performance && $performance->performance->overall_rating)
                                            <span class="badge badge-sm bg-gradient-{{ $this->getRatingColor($performance->performance->overall_rating) }}">
                                                {{ ucfirst($performance->performance->overall_rating) }}
                                            </span>
                                        @else
                                            <span class="text-secondary">-</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $performance->hours_worked ?? '-' }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            @php
                                                $authUser = auth()->user();
                                                $canRate = false;
                                                
                                                // Determine if current user can rate this performance
                                                if ($authUser->hasRole('nazim-ul-umoor')) {
                                                    $canRate = true;
                                                } elseif ($authUser->isSuperior()) {
                                                    // Superior can rate their assistants' performance
                                                    $assistantIds = $authUser->assistants->pluck('id');
                                                    $canRate = $assistantIds->contains($performance->user->id);
                                                }
                                                
                                                // Check if user can send reminders (nazim-ul-umoor or superior of this user)
                                                $canSendReminder = $authUser->hasRole('nazim-ul-umoor') || 
                                                    ($authUser->isSuperior() && $authUser->assistants->pluck('id')->contains($performance->user->id));
                                            @endphp

                                            @if($performance->performance)
                                                <button wire:click="openViewModal({{ $performance->performance->id }})" 
                                                        class="btn btn-sm btn-outline-info mb-0" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            @endif

                                            @if(!$performance->is_submitted && $canSendReminder)
                                                <button wire:click="sendReminder({{ $performance->user->id }})"
                                                        class="btn btn-sm btn-outline-warning mb-0" title="Send Reminder">
                                                    <i class="fas fa-bell"></i>
                                                </button>
                                            @endif

                                            @if(!$performance->is_overdue)
                                                {{-- Only show rate and lock icons for non-overdue tasks --}}
                                                @if($authUser->hasRole('nazim-ul-umoor') && !$performance->is_submitted)
                                                    <button wire:click="openResetModal({{ $performance->user->id }})"
                                                            class="btn btn-sm btn-outline-danger mb-0" title="Reset Access">
                                                        <i class="fas fa-unlock"></i>
                                                    </button>
                                                @endif

                                                @if($performance->performance && $canRate)
                                                    <button wire:click="openRatingModal({{ $performance->performance->id }})"
                                                            class="btn btn-sm btn-outline-warning mb-0" title="Rate">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                @endif
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-clipboard-list fa-3x text-secondary mb-3"></i>
                                            <h6 class="text-secondary">No performance records found</h6>
                                            <p class="text-sm text-secondary mb-0">No users with active tasks found for the selected criteria.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination removed -->
                </div>
            </div>
        </div>
    </div>

    <!-- Hierarchical View -->
    @if($viewMode === 'hierarchical')
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>Hierarchical Performance View</h6>
                    <p class="text-sm mb-0">Performance organized by Department → User → Task → Daily Reports</p>
                </div>
                <div class="card-body">
                    @if(!empty($hierarchicalData))
                        @foreach($hierarchicalData as $departmentData)
                            <!-- Department Level -->
                            <div class="department-section mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md me-3">
                                        <i class="fas fa-building text-white text-lg"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0">{{ $departmentData['department']->name }}</h5>
                                        <p class="text-sm text-muted mb-0">
                                            {{ count($departmentData['users']) }} user(s) with performance records
                                        </p>
                                    </div>
                                </div>

                                @foreach($departmentData['users'] as $userData)
                                    <!-- User Level -->
                                    <div class="user-section ms-4 mb-4">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md me-3" style="width: 35px; height: 35px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $userData['user']->name }}</h6>
                                                <p class="text-xs text-muted mb-0">
                                                    {{ $userData['user']->email }} • {{ count($userData['tasks']) }} task(s)
                                                </p>
                                            </div>
                                        </div>

                                        @foreach($userData['tasks'] as $taskData)
                                            <!-- Task Level -->
                                            <div class="task-section ms-4 mb-3">
                                                <div class="card border">
                                                    <div class="card-header pb-2">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div class="d-flex align-items-center">
                                                                <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md me-2" style="width: 30px; height: 30px;">
                                                                    <i class="fas fa-tasks text-white text-sm"></i>
                                                                </div>
                                                                <div>
                                                                    <h6 class="mb-0">{{ $taskData['task']->title }}</h6>
                                                                    <p class="text-xs text-muted mb-0">
                                                                        Due: {{ $taskData['task']->due_date ? $taskData['task']->due_date->format('M d, Y') : 'No due date' }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <div class="text-end">
                                                                <span class="badge bg-primary me-1">{{ $taskData['total_performances'] }} reports</span>
                                                                <span class="badge bg-success me-1">{{ $taskData['submitted_count'] }} submitted</span>
                                                                @php
                                                                    $overdueCount = $taskData['performances']->filter(function($p) {
                                                                        return isset($p->is_overdue) && $p->is_overdue;
                                                                    })->count();
                                                                @endphp
                                                                @if($overdueCount > 0)
                                                                    <span class="badge bg-danger me-1">{{ $overdueCount }} overdue</span>
                                                                @endif
                                                                @if($taskData['attachment_count'] > 0)
                                                                    <span class="badge bg-info">{{ $taskData['attachment_count'] }} files</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-body pt-2">
                                                        @if($taskData['total_performances'] > 0)
                                                            <div class="table-responsive">
                                                                <table class="table table-sm">
                                                                    <thead>
                                                                        <tr>
                                                                            <th class="text-xs">Date</th>
                                                                            <th class="text-xs">Task #</th>
                                                                            <th class="text-xs">Status</th>
                                                                            <th class="text-xs">Hours</th>
                                                                            <th class="text-xs">Tasks Completed</th>
                                                                            <th class="text-xs">Rating</th>
                                                                            <th class="text-xs">Files</th>
                                                                            <th class="text-xs">Actions</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @foreach($taskData['performances'] as $performance)
                                                                            <tr class="{{ (isset($performance->is_overdue) && $performance->is_overdue) ? 'table-warning' : '' }}">
                                                                                <td>
                                                                                    <span class="text-xs font-weight-bold">
                                                                                        {{ \Carbon\Carbon::parse($performance->performance_date)->format('M d') }}
                                                                                    </span>
                                                                                </td>
                                                                                <td>
                                                                                    <span class="text-xs font-weight-bold text-primary">
                                                                                        {{ $taskData['task']->task_number ?? 'N/A' }}
                                                                                    </span>
                                                                                </td>
                                                                                <td>
                                                                                    @if($performance->is_submitted)
                                                                                        <span class="badge badge-sm bg-gradient-success">Submitted</span>
                                                                                    @elseif(isset($performance->is_overdue) && $performance->is_overdue)
                                                                                        <span class="badge badge-sm bg-gradient-danger">Overdue</span>
                                                                                    @else
                                                                                        <span class="badge badge-sm bg-gradient-warning">Draft</span>
                                                                                    @endif
                                                                                </td>
                                                                                <td>
                                                                                    <span class="text-xs">{{ $performance->hours_worked ?? 0 }}h</span>
                                                                                </td>
                                                                                <td>
                                                                                    <span class="text-xs" title="{{ $performance->tasks_completed ?? 'No description' }}">
                                                                                        {{ \Str::limit($performance->tasks_completed ?? 'No description', 30) }}
                                                                                    </span>
                                                                                </td>
                                                                                <td>
                                                                                    @php
                                                                                        $isOverdue = isset($performance->is_overdue) && $performance->is_overdue;
                                                                                        $hasPerformanceId = isset($performance->id) && $performance->id;
                                                                                    @endphp
                                                                                    
                                                                                    @if($performance->superior_rating)
                                                                                        <span class="badge badge-sm bg-gradient-{{ $performance->superior_rating === 'excellent' ? 'success' : ($performance->superior_rating === 'good' ? 'info' : ($performance->superior_rating === 'fair' ? 'warning' : 'danger')) }}">
                                                                                            {{ ucfirst($performance->superior_rating) }}
                                                                                        </span>
                                                                                    @elseif($isOverdue)
                                                                                        <span class="text-xs text-muted">Overdue</span>
                                                                                    @else
                                                                                        @php
                                                                                            $related = $taskData['task']->relatedTasks();
                                                                                            $superiorTask = $related->firstWhere('role_type', 'superior');
                                                                                            $canRate = $hasPerformanceId && ((auth()->user()->isNazim() || auth()->user()->hasRole('Nazim-ul-Umoor'))
                                                                                                || ($superiorTask && (int)$superiorTask->assigned_to === (int)auth()->id() && $performance->user_id !== auth()->id()));
                                                                                        @endphp
                                                                                        @if($canRate)
                                                                                            <button wire:click="openRatingModal({{ $performance->id }})" 
                                                                                                    class="btn btn-xs btn-outline-primary">
                                                                                                Rate
                                                                                            </button>
                                                                                        @else
                                                                                            <span class="text-xs text-muted">Not rated</span>
                                                                                        @endif
                                                                                    @endif
                                                                                </td>
                                                                                <td>
                                                                                    @if($performance->attachments->count() > 0)
                                                                                        <button wire:click="viewAttachments({{ $performance->id }})" 
                                                                                                class="btn btn-xs btn-outline-info">
                                                                                            <i class="fas fa-paperclip"></i> {{ $performance->attachments->count() }}
                                                                                        </button>
                                                                                    @else
                                                                                        <span class="text-xs text-muted">None</span>
                                                                                    @endif
                                                                                </td>
                                                                                <td>
                                                                                    <div class="btn-group" role="group">
                                                                                        @php
                                                                                            $isOverdue = isset($performance->is_overdue) && $performance->is_overdue;
                                                                                            $hasPerformanceId = isset($performance->id) && $performance->id;
                                                                                        @endphp
                                                                                        
                                                                                        @if($hasPerformanceId)
                                                                                            <button wire:click="viewPerformance({{ $performance->id }})" 
                                                                                                    class="btn btn-xs btn-outline-primary" 
                                                                                                    title="View Details">
                                                                                                <i class="fas fa-eye"></i>
                                                                                            </button>
                                                                                        @endif
                                                                                        
                                                                                        @if($hasPerformanceId && $performance->attachments->count() > 0)
                                                                                            <button wire:click="viewAttachments({{ $performance->id }})" 
                                                                                                    class="btn btn-xs btn-outline-info" 
                                                                                                    title="View Attachments">
                                                                                                <i class="fas fa-paperclip"></i>
                                                                                            </button>
                                                                                        @endif
                                                                                        
                                                                                        @if($isOverdue)
                                                                                            {{-- For overdue tasks, only show reminder button --}}
                                                                                            @php
                                                                                                $authUser = auth()->user();
                                                                                                $canSendReminder = $authUser->hasRole('nazim-ul-umoor') || 
                                                                                                    ($authUser->isSuperior() && $authUser->assistants->pluck('id')->contains($performance->user_id));
                                                                                            @endphp
                                                                                            @if($canSendReminder)
                                                                                                <button wire:click="sendReminder({{ $performance->user_id }})"
                                                                                                        class="btn btn-xs btn-outline-warning" 
                                                                                                        title="Send Reminder">
                                                                                                    <i class="fas fa-bell"></i>
                                                                                                </button>
                                                                                            @endif
                                                                                        @else
                                                                                            {{-- For non-overdue tasks, show rating and other actions --}}
                                                                                            @if($hasPerformanceId && (auth()->user()->isNazim() || auth()->user()->hasRole('Nazim-ul-Umoor')))
                                                                                                <button wire:click="openRatingModal({{ $performance->id }})" 
                                                                                                        class="btn btn-xs btn-outline-warning" 
                                                                                                        title="Rate Performance">
                                                                                                    <i class="fas fa-star"></i>
                                                                                                </button>
                                                                                            @endif
                                                                                            
                                                                                            @php
                                                                                                // Determine if current user can adopt: they must be superior for grouped tasks
                                                                                                $canAdopt = false;
                                                                                                if($hasPerformanceId) {
                                                                                                    $related = $taskData['task']->relatedTasks();
                                                                                                    $superiorTask = $related->firstWhere('role_type', 'superior');
                                                                                                    if($superiorTask && (int)$superiorTask->assigned_to === (int)auth()->id() && $performance->user_id !== auth()->id()) {
                                                                                                        $canAdopt = true;
                                                                                                    }
                                                                                                }
                                                                                            @endphp
                                                                                            @if($canAdopt)
                                                                                                <button wire:click="adoptAssistantPerformance({{ $performance->id }})"
                                                                                                        class="btn btn-xs btn-outline-success"
                                                                                                        title="Adopt assistant's performance for this date">
                                                                                                    <i class="fas fa-user-check"></i>
                                                                                                </button>
                                                                                            @endif
                                                                                        @endif
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        @endforeach
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        @else
                                                            <div class="text-center py-3">
                                                                <i class="fas fa-inbox text-muted" style="font-size: 2rem;"></i>
                                                                <p class="text-muted mt-2">No performance records found for this task in the selected date range.</p>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-3">No Performance Data Found</h5>
                            <p class="text-muted">No performance records match your current filters. Try adjusting the date range or filters.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- View Performance Modal -->
    @if($showViewModal && $selectedPerformance)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Performance Report Details</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">{{ $selectedPerformance->user->name }}</h6>
                            <p class="text-sm text-secondary mb-0">{{ $selectedPerformance->user->email }}</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge badge-lg bg-gradient-{{ $this->getStatusColor($selectedPerformance->is_submitted) }}">
                                {{ $selectedPerformance->is_submitted ? 'Submitted' : 'Pending' }}
                            </span>
                            @if($selectedPerformance->overall_rating)
                                <span class="badge badge-lg bg-gradient-{{ $this->getRatingColor($selectedPerformance->overall_rating) }} ms-2">
                                    {{ ucfirst($selectedPerformance->overall_rating) }}
                                </span>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Date</small>
                                <p class="mb-0">{{ $selectedPerformance->performance_date->format('F d, Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Hours Worked</small>
                                <p class="mb-0">{{ $selectedPerformance->hours_worked ?? 'Not specified' }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Task</small>
                                <p class="mb-0">{{ $selectedPerformance->task->title ?? 'No task assigned' }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Department</small>
                                <p class="mb-0">{{ $selectedPerformance->department->name ?? 'No department' }}</p>
                            </div>
                        </div>
                    </div>

                    @if($selectedPerformance->tasks_completed)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Tasks Completed</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->tasks_completed }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->challenges_faced)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Challenges Faced</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->challenges_faced }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->next_day_plan)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Next Day Plan</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->next_day_plan }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->additional_notes)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Additional Notes</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->additional_notes }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->submitted_at)
                    <div class="info-item">
                        <small class="text-uppercase text-secondary font-weight-bold">Submitted At</small>
                        <p class="mb-0">{{ $selectedPerformance->submitted_at->format('F d, Y h:i A') }}</p>
                    </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Close</button>
                    @if(!$selectedPerformance->is_submitted)
                        <button type="button" class="btn btn-warning" wire:click="sendReminder({{ $selectedPerformance->user_id }})">
                            <i class="fas fa-bell"></i> Send Reminder
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Reset Access Modal -->
    @if($showResetModal && $selectedUserForReset)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-unlock text-danger me-2"></i>
                        Reset User Access
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeResetModal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Confirm Access Reset
                        </h6>
                        <p class="mb-3">
                            You are about to reset access for <strong>{{ $selectedUserForReset->name }}</strong>.
                        </p>
                        <hr>
                        <p class="mb-0">
                            This will:
                        </p>
                        <ul class="mb-0">
                            <li>Remove any active performance restrictions</li>
                            <li>Create or mark today's performance as submitted</li>
                            <li>Allow the user to access the system immediately</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <label for="resetReason" class="form-label">Reason for Reset (Optional)</label>
                        <textarea wire:model="resetReason"
                                  id="resetReason"
                                  class="form-control"
                                  rows="3"
                                  placeholder="Enter reason for resetting user access..."></textarea>
                    </div>

                    <div class="user-info mt-3">
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <h6 class="mb-2">User Information</h6>
                                <p class="mb-1"><strong>Name:</strong> {{ $selectedUserForReset->name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $selectedUserForReset->email }}</p>
                                <p class="mb-0">
                                    <strong>Role:</strong>
                                    @if($selectedUserForReset->isSuperior())
                                        <span class="badge bg-success">Superior</span>
                                    @elseif($selectedUserForReset->isMujeeb())
                                        <span class="badge bg-primary">Mujeeb</span>
                                    @else
                                        <span class="badge bg-secondary">Other</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeResetModal">Cancel</button>
                    <button type="button" class="btn btn-danger" wire:click="resetUserAccess">
                        <i class="fas fa-unlock me-2"></i>
                        Reset Access
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Attachments Modal -->
    @if($showAttachmentsModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-paperclip me-2"></i>
                        Performance Attachments
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeAttachmentsModal"></button>
                </div>
                <div class="modal-body">
                    @if(!empty($selectedAttachments))
                        <div class="row">
                            @foreach($selectedAttachments as $attachment)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="{{ $this->getFileIcon($attachment) }}" style="font-size: 2rem;"></i>
                                                </div>
                                                <div class="flex-grow-1 min-w-0">
                                                    <h6 class="mb-1 text-truncate" title="{{ $attachment['original_name'] }}">
                                                        {{ $attachment['original_name'] }}
                                                    </h6>
                                                    <small class="text-muted">
                                                        {{ $this->formatFileSize($attachment['file_size']) }}
                                                    </small>
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ \Carbon\Carbon::parse($attachment['created_at'])->format('M d, Y H:i') }}
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <div class="btn-group w-100" role="group">
                                                    @if($this->canViewInBrowser($attachment))
                                                        <a href="{{ route('daily-performance.attachment.view', $attachment['id']) }}" 
                                                           target="_blank"
                                                           class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye me-1"></i>
                                                            View
                                                        </a>
                                                    @endif
                                                    <a href="{{ route('daily-performance.attachment.download', $attachment['id']) }}" 
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-download me-1"></i>
                                                        Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-paperclip text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3 text-muted">No attachments found.</p>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeAttachmentsModal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Rating Modal -->
    @if($showRatingModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-star me-2"></i>
                        Rate Performance
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeRatingModal"></button>
                </div>
                <div class="modal-body">
                    @if($selectedPerformanceForRating)
                        <div class="mb-3">
                            <h6>Performance Details</h6>
                            <p class="text-sm text-muted mb-1">
                                <strong>User:</strong> {{ $selectedPerformanceForRating->user->name }}
                            </p>
                            <p class="text-sm text-muted mb-1">
                                <strong>Date:</strong> {{ $selectedPerformanceForRating->performance_date->format('M d, Y') }}
                            </p>
                            <p class="text-sm text-muted mb-3">
                                <strong>Task:</strong> {{ $selectedPerformanceForRating->task->title ?? 'N/A' }}
                            </p>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">Superior Rating *</label>
                            <select wire:model="superiorRating" class="form-select @error('superiorRating') is-invalid @enderror">
                                <option value="">Select Rating</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                            @error('superiorRating') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">Comments</label>
                            <textarea wire:model="superiorComments" 
                                      class="form-control @error('superiorComments') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Optional comments about the performance..."></textarea>
                            @error('superiorComments') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeRatingModal">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-primary" wire:click="submitRating">
                        <i class="fas fa-star me-1"></i>
                        Submit Rating
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
