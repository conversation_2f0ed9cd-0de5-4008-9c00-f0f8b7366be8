@php
    use Illuminate\Support\Facades\DB;
@endphp

<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Send Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->


        <!-- Modern UI Styles -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        
        <style>
            :root {
                --primary-color: #4f46e5;
                --primary-hover: #4338ca;
                --secondary-color: #f8fafc;
                --accent-color: #06b6d4;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --border-color: #e5e7eb;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
            }

            * {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            }

            .modern-container {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 2rem 0;
            }

            .main-card {
                background: white;
                border-radius: var(--radius-lg);
                box-shadow: var(--shadow-lg);
                overflow: hidden;
                margin: 0 auto;
                max-width: 1400px;
            }

            .card-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
                color: white;
                padding: 2rem;
                text-align: center;
            }

            .card-header h1 {
                font-size: 2rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .card-body {
                padding: 2rem;
            }

            /* Role Selection Styling */
            .role-selection {
                background: var(--secondary-color);
                border-radius: var(--radius-md);
                padding: 1.5rem;
                margin-bottom: 2rem;
                border: 1px solid var(--border-color);
            }

            .role-selection label {
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
                display: block;
            }

            .role-selection select {
                width: 100%;
                padding: 0.75rem 1rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-md);
                font-size: 1rem;
                transition: all 0.2s ease;
                background: white;
            }

            .role-selection select:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            .role-selection p {
                background: var(--primary-color);
                color: white;
                padding: 0.75rem 1rem;
                border-radius: var(--radius-md);
                margin: 0;
                font-weight: 500;
            }

            /* Form Controls */
            .form-controls {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
                margin-bottom: 2rem;
            }

            .form-group {
                position: relative;
            }

            .form-group label {
                display: block;
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
            }

            .file-input-wrapper {
                position: relative;
                display: inline-block;
                width: 100%;
            }

            /* Enhanced File Input with Drag & Drop */
            .file-input-button {
                background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
                color: white;
                padding: 3rem 2rem;
                border-radius: var(--radius-lg);
                border: 3px dashed transparent;
                cursor: pointer;
                font-weight: 600;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 1rem;
                transition: all 0.3s ease;
                width: 100%;
                box-shadow: var(--shadow-md);
                min-height: 200px;
                position: relative;
                overflow: hidden;
            }

            .file-input-button::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .file-input-button:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
                border-color: rgba(255,255,255,0.3);
            }

            .file-input-button:hover::before {
                opacity: 1;
            }

            .file-input-button.drag-over {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
                border-color: rgba(255,255,255,0.5);
                transform: scale(1.02);
                box-shadow: 0 0 30px rgba(79, 70, 229, 0.3);
            }

            .file-input-button i {
                font-size: 3rem;
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;
            }

            .file-input-button:hover i {
                transform: scale(1.1);
            }

            .file-input-button.drag-over i {
                animation: bounce 0.6s ease-in-out;
            }

            .file-upload-text {
                font-size: 1.25rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
                text-align: center;
            }

            .file-upload-subtext {
                font-size: 1rem;
                opacity: 0.9;
                text-align: center;
                font-weight: 400;
            }

            @keyframes bounce {
                0%, 20%, 60%, 100% {
                    transform: translateY(0) scale(1);
                }
                40% {
                    transform: translateY(-10px) scale(1.1);
                }
                80% {
                    transform: translateY(-5px) scale(1.05);
                }
            }

            .date-input {
                width: 100%;
                padding: 1rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-md);
                font-size: 1rem;
                transition: all 0.2s ease;
                background: white;
            }

            .date-input:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            /* Modern Table Styling */
            .modern-table-wrapper {
                background: white;
                border-radius: var(--radius-lg);
                overflow: hidden;
                box-shadow: var(--shadow-md);
                margin-bottom: 2rem;
            }

            .modern-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 0.875rem;
            }

            .modern-table thead {
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            }

            .modern-table thead th {
                color: white;
                font-weight: 600;
                padding: 1rem 0.75rem;
                text-align: center;
                font-size: 0.8rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                border: none;
            }

            .modern-table tbody tr {
                border-bottom: 1px solid var(--border-color);
                transition: all 0.2s ease;
            }

            .modern-table tbody tr:hover {
                background-color: #f9fafb;
            }

            .modern-table tbody tr:nth-child(even) {
                background-color: #fafafa;
            }

            .modern-table tbody tr:nth-child(even):hover {
                background-color: #f3f4f6;
            }

            .modern-table tbody td {
                padding: 0.75rem 0.5rem;
                text-align: center;
                vertical-align: middle;
                border: none;
            }

            .modern-table input[type="text"],
            .modern-table input[type="date"] {
                width: 100%;
                padding: 0.5rem;
                border: 1px solid var(--border-color);
                border-radius: var(--radius-sm);
                font-size: 0.875rem;
                transition: all 0.2s ease;
            }

            .modern-table input:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
            }

            .modern-table select {
                width: 100%;
                padding: 0.5rem;
                border: 1px solid var(--border-color);
                border-radius: var(--radius-sm);
                font-size: 0.875rem;
                background: white;
                transition: all 0.2s ease;
            }

            .modern-table select:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
            }

            /* Submit Button */
            .submit-button {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
                color: white;
                padding: 1rem 2rem;
                border: none;
                border-radius: var(--radius-md);
                font-size: 1.1rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                box-shadow: var(--shadow-md);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin: 0 auto;
            }

            .submit-button:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }

            .submit-button:active {
                transform: translateY(0);
            }

            /* Alert Styling */
            .alert {
                padding: 1rem 1.5rem;
                border-radius: var(--radius-md);
                margin-bottom: 1.5rem;
                border: none;
                font-weight: 500;
            }

            .alert-success {
                background: #ecfdf5;
                color: #065f46;
                border-left: 4px solid var(--success-color);
            }

            .alert-danger {
                background: #fef2f2;
                color: #991b1b;
                border-left: 4px solid var(--danger-color);
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .form-controls {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }
                
                .card-body {
                    padding: 1rem;
                }
                
                .modern-table {
                    font-size: 0.75rem;
                }
                
                .modern-table thead th,
                .modern-table tbody td {
                    padding: 0.5rem 0.25rem;
                }
            }

            /* Loading Animation */
            .loading {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid rgba(255,255,255,.3);
                border-radius: 50%;
                border-top-color: #fff;
                animation: spin 1s ease-in-out infinite;
            }

            @keyframes spin {
                to { transform: rotate(360deg); }
            }

            /* Custom File Input */
            input[type="file"] {
                display: none;
            }

            .file-input-label {
                cursor: pointer;
                display: block;
            }
        </style>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                // Get the input element by its ID
                var dateInput = document.getElementById("name");
                
                // Create a new date object with the current date
                var currentDate = new Date();
                
                // Format the current date as "YYYY-MM-DD"
                var formattedDate = currentDate.toISOString().split('T')[0];
                
                // Set the formatted date as the input's value
                dateInput.value = formattedDate;

                // Initialize drag and drop functionality
                initializeDragAndDrop();
            });

            // Drag and Drop Functionality (without changing existing logic)
            function initializeDragAndDrop() {
                const dropArea = document.getElementById('fileDropArea');
                const fileInput = document.getElementById('files');

                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    dropArea.addEventListener(eventName, preventDefaults, false);
                    document.body.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    dropArea.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    dropArea.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                dropArea.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    dropArea.classList.add('drag-over');
                }

                function unhighlight(e) {
                    dropArea.classList.remove('drag-over');
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    // Update the file input with dropped files (preserving existing logic)
                    fileInput.files = files;
                    
                    // Trigger the existing change event to maintain all existing functionality
                    const event = new Event('change', { bubbles: true });
                    fileInput.dispatchEvent(event);
                }
            }
        </script>
        
   
        
         
        <div class="modern-container">
            <div class="main-card">
                <div class="card-header">
                    @php
                        $checker = request()->route('checker') ?? null;
                        $transfer_by = request()->route('transfer_by') ?? null;
                    @endphp
                    <h1><i class="fas fa-paper-plane"></i> Send Fatawa For Checking</h1>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-weight: 400;">To {{$checker}} by {{$transfer_by}}</p>
                </div>
                
                <div class="card-body">
                    <!-- Role Selection Section -->
                    <div class="role-selection">
                        @php
                            // Get darulifta names from mujeebs table where mujeeb_name matches user's name
                            $userMujeebData = DB::table('mujeebs')
                                ->where('mujeeb_name', Auth::user()->name)
                                ->pluck('darul_name')
                                ->toArray();
                            
                            // Get role-based darulifta names
                            $roleBasedDarulIfta = [];
                            foreach(Auth::user()->roles as $role) {
                                if(in_array($role->name, ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'])) {
                                    $roleBasedDarulIfta[] = $role->name;
                                }
                            }
                            
                            // Combine both arrays and remove duplicates
                            $availableDarulIfta = array_unique(array_merge($userMujeebData, $roleBasedDarulIfta));
                        @endphp

                        @if(count($availableDarulIfta) > 1)
                            <label for="select-role"><i class="fas fa-university"></i> Select Darulifta:</label>
                            <select id="select-role" name="selected_role">
                                @foreach($availableDarulIfta as $darulIfta)
                                    <option value="{{ $darulIfta }}">{{ $darulIfta }}</option>
                                @endforeach
                            </select>
                        @elseif(count($availableDarulIfta) == 1)
                            <label><i class="fas fa-university"></i> Assigned Darulifta:</label>
                            <p id="single-role">{{ $availableDarulIfta[0] }}</p>
                        @else
                            <label><i class="fas fa-exclamation-triangle"></i> Status:</label>
                            <p id="single-role" style="background: var(--warning-color);">No Darulifta assigned</p>
                        @endif
                    </div>
            
                    <!-- Alert Messages -->
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> {{ session('success') }}
                        </div>
                    @elseif(session('fileErrors'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i>
                            @foreach(session('fileErrors') as $error)
                                {{ $error }}<br>
                            @endforeach
                        </div>
                    @elseif(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
                        </div>
                    @endif
                    
                    <form action="{{ route('files.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- Form Controls -->
                        <div class="form-controls">
                            <div class="form-group">
                                <label for="name"><i class="fas fa-calendar-alt"></i> Select Folder Date</label>
                                <input type="date" class="date-input" id="name" name="name" value="" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="file-input-label">
                                    <div class="file-input-button" id="fileDropArea">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <div class="file-upload-text">Drag & Drop Fatawa Files</div>
                                        <div class="file-upload-subtext">or click to browse files</div>
                                    </div>
                                    <input type="file" id="files" name="files[]" multiple required>
                                </label>
                            </div>
                        </div>

                        <!-- Modern Table -->
                        <div class="modern-table-wrapper">
                            <table class="modern-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-file"></i> File Name</th>
                                        <th><i class="fas fa-code"></i> Fatwa Code</th>
                                        <th><i class="fas fa-user"></i> Sender</th>
                                        <th><i class="fas fa-calendar"></i> Created Date</th>
                                        <th><i class="fas fa-tags"></i> Category</th>
                                        <th><i class="fas fa-list"></i> Fatwa Type</th>
                                        <th><i class="fas fa-university"></i> Darulifta</th>
                                        <th><i class="fas fa-user-check"></i> Checker</th>
                                        <th><i class="fas fa-share"></i> Transfer By</th>
                                        
                                        <input type="hidden" name="mail_folder_date_header" value="Mail Folder Date">
                                        <input type="hidden" name="mail_sender_date_header" value="Mail Sender Date">
                                    </tr>
                                </thead>
                                <tbody id="file-details">
                                    <!-- Dynamically generated rows will go here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <button type="submit" class="submit-button">
                            <i class="fas fa-paper-plane"></i>
                            Upload Fatawa
                        </button>
                    </form>
                </div>
            </div>
        </div>
            
            <script>
                
                document.addEventListener('DOMContentLoaded', function () {
                    document.getElementById('files').addEventListener('change', function (e) {
                    // Clear existing file details
                    
                    document.getElementById('file-details').innerHTML = '';
                
                    

                    let folderName = document.getElementById('name').value;
                    let selectedRoleElement = document.getElementById('select-role');
                    let selectedRole = selectedRoleElement ? selectedRoleElement.value : '';
                    let singleRoleElement = document.getElementById('single-role');
                    let singleRole = singleRoleElement ? singleRoleElement.textContent : '';

                    // Check if the user has multiple roles
                    if (singleRole === '' && selectedRole !== '') {
                        singleRole = selectedRole;
                    }                                    
                
                      // Loop through selected files and create input fields
                       // Create an array to store selected files
            const selectedFiles = Array.from(e.target.files);

            

// Sort selected files based on their order
            selectedFiles.sort((a, b) => a.index - b.index);

            
            console.log(selectedFiles);
            // Clear existing file details
            

            // Loop through sorted selected files and create input fields
            selectedFiles.forEach(function (file, index) {
                // console.log(file);
                        // Replace spaces with underscores in the original file name
                        const originalFileName = file.name;

                        const modifiedFileName = originalFileName.replace(/ /g, '_');
                        // console.log('Original File Name:', originalFileName);
                        // console.log('Modified File Name:', modifiedFileName);

                        let tr = document.createElement('tr');

                    // Create a hidden input field to store the original file name
                        let inputOriginalFileName = document.createElement('input');
                        inputOriginalFileName.type = 'hidden';
                        inputOriginalFileName.value = originalFileName;
                        inputOriginalFileName.name = 'original_file_name[]';
                        
                        tr.appendChild(inputOriginalFileName);
                        console.log('Original File Name Input Value:', inputOriginalFileName.value);




                        // Create a hidden input field to store the modified file name
                        let inputModifiedFileName = document.createElement('input');
                        inputModifiedFileName.type = 'hidden';
                        inputModifiedFileName.name = 'modified_file_name[]';
                        inputModifiedFileName.value = modifiedFileName;
                        tr.appendChild(inputModifiedFileName);
                        console.log('Modified File Name Input Value:', inputModifiedFileName.value);

                         // Create a hidden input field to store the file order
                        

                        // Display the modified file name in a separate cell in the table
                        let tdFileName = document.createElement('td');
                        tdFileName.textContent = modifiedFileName;

                
                        // Extract information from the file name
                        let fileNameParts = modifiedFileName.split('_');
                        if (fileNameParts.length >= 7) {
                            // Fatwa Code
                            let fatwaCode = fileNameParts[0] + '-' + fileNameParts[1];
                            fetch('/getQuestionData?ifta_code=' + fatwaCode)
                    .then(response => response.json())
                    .then(data => {
                            // Fatwa Code (Editable)
                            let tdFatwaCode = document.createElement('td');
                        let inputFatwaCode = document.createElement('input');
                        inputFatwaCode.type = 'text';
                        inputFatwaCode.name = 'fatwa_code[]'; // Use appropriate name for form submission
                        inputFatwaCode.value = fatwaCode; // Set the initial value
                        tdFatwaCode.appendChild(inputFatwaCode);

                        // Sender Name
                        let senderName = data.assign_id ? data.assign_id : "First send this fatwa to Mujeeb";
                        let tdSender = document.createElement('td');
                        let inputSender = document.createElement('input');
                        inputSender.type = 'text';
                        inputSender.name = 'sender[]';
                        inputSender.value = senderName;
                        inputSender.required = true;
                        tdSender.appendChild(inputSender);
                        if (!data.assign_id) {
        // If assign_id is null, set the row color to red
        tdSender.style.backgroundColor = 'red';

        // Disable the input field
        inputSender.disabled = true;
        inputSender.value = "No assign_id"; // You can set any desired text
    } else {
        // If assign_id is not null, allow form submission
        inputSender.required = true;
    }
                
                            // File Created Date
                            let createdDate = data.mujeeb_send_date ? data.mujeeb_send_date : "First send this fatwa to Mujeeb";
                            // File Created Date (Editable)
                            let tdDate = document.createElement('td');
                            let inputDate = document.createElement('input');
                            inputDate.type = 'text';
                            inputDate.name = 'file_created_date[]'; // Use appropriate name for form submission
                            inputDate.value = createdDate; // Set the initial value
                            tdDate.appendChild(inputDate);

                
                            // Category
                            let category = data.issue ? data.issue : "First send this fatwa to Mujeeb";
                            // Category (Editable)
                            let tdCategory = document.createElement('td');
                            let inputCategory = document.createElement('input');
                            inputCategory.type = 'text';
                            inputCategory.name = 'category[]'; // Use appropriate name for form submission
                            inputCategory.value = category; // Set the initial value
                            tdCategory.appendChild(inputCategory);

                
                            // Fatwa Type (Dropdown)
                            let tdFatwaType = document.createElement('td');
                            let selectFatwaType = document.createElement('select');
                            selectFatwaType.name = 'fatwa_type[]';
                            let optionNew = document.createElement('option');
                            optionNew.value = 'New';
                            optionNew.textContent = 'New';
                            let optionMahlENazar = document.createElement('option');
                            optionMahlENazar.value = 'Mahl e Nazar';
                            optionMahlENazar.textContent = 'Mahl e Nazar';
                            selectFatwaType.appendChild(optionNew);
                            selectFatwaType.appendChild(optionMahlENazar);
                            tdFatwaType.appendChild(selectFatwaType);
                            // Darulifta (Auto)
                            
                            let tdDarulifta = document.createElement('td');
                            let inputDarulifta = document.createElement('input');
                            inputDarulifta.type = 'text';
                            inputDarulifta.name = 'darulifta[]';

                            // Set the value for 'darulifta_name' based on singleRole
                            inputDarulifta.value = singleRole;
                            // Function to update the 'darulifta' column value with '3btn' if selected
                            function updateDaruliftaValue() {
                            const select3btn = document.getElementById('3btn');
                            const selected3btnValue = select3btn ? select3btn.value : '';

                            // Concatenate 'darulifta_name' with '3btn' if '3btn' is selected
                            if (selected3btnValue === '3btn') {
                                inputDarulifta.value = singleRole + '3btn';
                            } else {
                                // Set the 'darulifta' value to 'darulifta_name' if '3btn' is not selected
                                inputDarulifta.value = singleRole;
                            }
                        }

                        // Add an event listener to the '3btn' dropdown to update the 'darulifta' value
                        // document.getElementById('3btn').addEventListener('change', updateDaruliftaValue);

                        // Append the <input> element to the <td> element
                        tdDarulifta.appendChild(inputDarulifta);

                        // Initially update the 'darulifta' value based on '3btn' selection
                        updateDaruliftaValue();
                            // Mail Folder Date (Using the stored folderName variable)
                            let tdFolderDate = document.createElement('td');
                            let inputFolderDate = document.createElement('input');
                            inputFolderDate.type = 'date';
                            inputFolderDate.name = 'mail_folder_date[]';
                            inputFolderDate.value = folderName;
                            inputFolderDate.hidden = true; // Hide this field
                            tdFolderDate.appendChild(inputFolderDate);
                
                            // Mail Sender Date (Auto)
                            let tdSenderDate = document.createElement('td');
                            let inputSenderDate = document.createElement('input');
                            inputSenderDate.type = 'text';
                            inputSenderDate.name = 'mail_sender_date[]';
                            inputSenderDate.value = new Date().toISOString().split('T')[0];
                            inputSenderDate.disabled = true;
                            inputSenderDate.hidden = true; // Hide this field
                            tdSenderDate.appendChild(inputSenderDate);
                
                            let tdChecker = document.createElement('td');
                            let inputChecker = document.createElement('input');
                            inputChecker.type = 'text'; // Since you're likely not displaying this value
                            inputChecker.name = 'checker[]';
                            // Assuming you've already assigned `$checker` to a JavaScript variable
                            // You need to output its value here safely
                            inputChecker.value = "{{ $checker }}"; // Use Blade syntax to insert the PHP variable
                            tdChecker.appendChild(inputChecker);

                            let tdTransferBy = document.createElement('td');
                            let inputTransferBy = document.createElement('input');
                            inputTransferBy.type = 'text'; // Since you're likely not displaying this value
                            inputTransferBy.name = 'transfer_by[]';
                            // Assuming you've already assigned `$checker` to a JavaScript variable
                            // You need to output its value here safely
                            inputTransferBy.value = "{{ $transfer_by }}"; // Use Blade syntax to insert the PHP variable
                            tdTransferBy.appendChild(inputTransferBy);
                
                            // Append elements to the row
                            tr.appendChild(tdFileName);
                            
                            tr.appendChild(tdFatwaCode);
                            tr.appendChild(tdSender);
                            tr.appendChild(tdDate);
                            tr.appendChild(tdCategory);
                            tr.appendChild(tdFatwaType);
                            tr.appendChild(tdDarulifta);
                            tr.appendChild(tdChecker);
                            tr.appendChild(tdTransferBy);
                            tr.appendChild(tdFolderDate);
                            tr.appendChild(tdSenderDate);
                            
                
                            document.getElementById('file-details').appendChild(tr);
                    })
                    .catch(error => {
                        // Handle errors, for example, if the Fatwa Code is not found
                        console.error(error);
                    });
            } else {
                // If the file name doesn't match the expected pattern, provide a message
                let tdErrorMessage = document.createElement('td');
                tdErrorMessage.textContent = 'Invalid file name format';
                tr.appendChild(tdFileName);
                tr.appendChild(tdErrorMessage);
            }
       
    });
});

                document.addEventListener('DOMContentLoaded', function () {
    // Your code here

    // Find the form element
    const form = document.querySelector('form');

    // Add a submit event listener to the form
    form.addEventListener('submit', function (e) {
        e.preventDefault(); // Prevent the default form submission behavior

        // Collect the form data
        const formData = new FormData(form);

        // You can now send the formData to the server using a fetch request or other methods
        // For example:
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                // If you need to set headers, you can do it here
            },
        })
        .then(response => response.json()) // If expecting a JSON response
        .then(data => {
            // Handle the response from the server
            console.log(data);
            // You can add code to handle success or error responses here
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch
            console.error('Error:', error);
        });
    });
});
});

                
                </script>
                <script>
            document.addEventListener('DOMContentLoaded', function () {
                const folderDateInput = document.querySelector('[name="name"]');
                const filesInput = document.querySelector('[name="files[]"]');
                const selectRole = document.getElementById('select-role');
        
                filesInput.addEventListener('change', function (e) {
                    // Check if any files are selected
                    if (filesInput.files.length > 0) {
                        folderDateInput.disabled = true; // Disable the "Folder Date" input
                        selectRole.disabled = true;
                    } else {
                        folderDateInput.disabled = false; // Enable the "Folder Date" input
                        selectRole.disabled = false;
                    }
                });
            });
            document.getElementById('name').min = new Date().toISOString().split('T')[0];
        </script>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>