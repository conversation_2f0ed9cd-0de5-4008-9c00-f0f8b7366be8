<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="mutakhassis"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="mutakhassis"></x-navbars.navs.auth>

        <style>
            /* Modern UI Styles for Mutakhassis Page */
            .modern-page-container {
                padding: 2rem;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }

            .modern-header-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
                position: relative;
            }

            .modern-header-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                pointer-events: none;
            }

            .modern-header-content {
                padding: 2.5rem;
                color: white;
                text-align: center;
                position: relative;
                z-index: 2;
            }

            .modern-header-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                background: linear-gradient(45deg, #fff, #f0f0f0);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .modern-header-subtitle {
                font-size: 1.2rem;
                opacity: 0.9;
                margin-bottom: 2rem;
                font-weight: 300;
            }

            .modern-action-button {
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                border: none;
                color: white;
                padding: 1rem 2.5rem;
                border-radius: 50px;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.75rem;
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-size: 0.9rem;
            }

            .modern-action-button:hover {
                transform: translateY(-3px);
                box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
                color: white;
            }

            .modern-summary-card {
                background: white;
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .modern-summary-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            }

            .modern-summary-header {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                padding: 1.5rem 2rem;
                border: none;
                position: relative;
            }

            .modern-summary-header::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            }

            .modern-summary-title {
                font-size: 1.4rem;
                font-weight: 600;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }

            .modern-data-card {
                background: white;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .modern-data-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            }

            .modern-data-header {
                background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
                color: white;
                padding: 1.25rem 2rem;
                border: none;
                position: relative;
            }

            .modern-data-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                pointer-events: none;
            }

            .modern-data-title {
                font-size: 1.2rem;
                font-weight: 600;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                position: relative;
                z-index: 2;
            }

            .modern-table-container {
                padding: 0;
                overflow-x: auto;
                background: white;
            }

            .modern-table {
                width: 100%;
                margin: 0;
                border-collapse: separate;
                border-spacing: 0;
                font-family: 'Jameel Noori Nastaleeq', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            .modern-table thead th {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                color: #495057;
                font-weight: 600;
                padding: 1.25rem 1rem;
                text-align: center;
                border: none;
                font-size: 0.9rem;
                position: sticky;
                top: 0;
                z-index: 10;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .modern-table tbody td {
                padding: 1.25rem 1rem;
                text-align: center;
                border-bottom: 1px solid #f1f3f4;
                vertical-align: middle;
                transition: background-color 0.3s ease;
                font-size: 0.95rem;
            }

            .modern-table tbody tr:hover {
                background: linear-gradient(135deg, #f8f9ff 0%, #fff5f5 100%);
            }

            .modern-table tbody tr:last-child td {
                border-bottom: none;
            }

            .modern-select {
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                width: 100%;
                max-width: 200px;
                background: white;
                color: #495057;
                font-weight: 500;
            }

            .modern-select:focus {
                border-color: #4facfe;
                box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
                outline: none;
                transform: translateY(-1px);
            }

            .modern-file-input {
                border: 2px dashed #e9ecef;
                border-radius: 10px;
                padding: 0.75rem;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                width: 100%;
                max-width: 150px;
                background: #f8f9fa;
            }

            .modern-file-input:hover {
                border-color: #4facfe;
                background: #f0f8ff;
            }

            .modern-upload-button {
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
                border: none;
                color: #495057;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-weight: 600;
                transition: all 0.3s ease;
                margin-left: 0.5rem;
                font-size: 0.8rem;
            }

            .modern-upload-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 154, 158, 0.3);
            }

            .status-badge {
                padding: 0.4rem 1rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .status-received {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .status-processing {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
            }

            .file-code-badge {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 10px;
                font-weight: 600;
                font-size: 0.9rem;
            }

            .sender-badge {
                background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                color: #2d3748;
                padding: 0.4rem 0.8rem;
                border-radius: 8px;
                font-weight: 600;
                font-size: 0.85rem;
            }

            .category-text {
                color: #6c757d;
                font-style: italic;
                direction: rtl;
                text-align: right;
            }

            @media (max-width: 768px) {
                .modern-page-container {
                    padding: 1rem;
                }

                .modern-header-content {
                    padding: 2rem 1.5rem;
                }

                .modern-header-title {
                    font-size: 2rem;
                }

                .modern-table-container {
                    font-size: 0.8rem;
                }

                .modern-table thead th,
                .modern-table tbody td {
                    padding: 0.75rem 0.5rem;
                }

                .modern-select,
                .modern-file-input {
                    max-width: 120px;
                    font-size: 0.8rem;
                }
            }
        </style>

        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <div class="modern-page-container">
            <!-- Modern Header Card -->
            <div class="modern-header-card">
                <div class="modern-header-content">
                    <h1 class="modern-header-title">
                        <i class="fas fa-user-graduate me-2"></i>
                        Mutakhassis Dashboard
                    </h1>
                    <p class="modern-header-subtitle">
                        Manage and transfer fatawa for specialized review and processing
                    </p>
                    <a href="{{ route('create', ['checker' => 'mufti_ali_asghar', 'transfer_by' => $checker]) }}" class="modern-action-button">
                        <i class="fas fa-paper-plane"></i>
                        Send Fatawa to Mufti Sahib
                    </a>
                </div>
            </div>

            <!-- Modern Summary Card -->
            <div class="modern-summary-card">
                <div class="modern-summary-header">
                    <h2 class="modern-summary-title">
                        <i class="fas fa-inbox me-2"></i>
                        Received Fatawa Folders for Checking
                    </h2>
                </div>
                <div class="modern-table-container">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-university me-1"></i>
                                    Darulifta
                                </th>
                                <th>
                                    <i class="fas fa-folder-open me-1"></i>
                                    Folders for Checking
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($summaryReport as $item)
                                <tr>
                                    <td>
                                        <span class="sender-badge">{{ $item['Darulifta'] }}</span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-received">{{ $item['Mail Recived'] }} Folders</span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Main Data Display Section -->
            @if (empty($dataByDaruliftaName))
                <div class="modern-data-card">
                    <div class="modern-data-header">
                        <h3 class="modern-data-title">
                            <i class="fas fa-info-circle me-2"></i>
                            No Data Available
                        </h3>
                    </div>
                    <div class="p-4 text-center">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No data found for the selected criteria.</p>
                    </div>
                </div>
            @else
                @foreach ($mailfolderDates as $mailfolderDate)
                    <div class="modern-data-card">
                        <div class="modern-data-header">
                            <h3 class="modern-data-title">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Received Folder Date: {{ \Carbon\Carbon::parse($mailfolderDate)->format('d M Y') }}
                            </h3>
                        </div>

                        <div class="card-body p-0">
                            @foreach ($daruliftaNames as $daruliftaName)
                                @if (isset($dataByDaruliftaName[$daruliftaName][$mailfolderDate]))
                                    <div class="modern-data-card mb-3 mx-3 mt-3">
                                        <div class="modern-data-header">
                                            <h4 class="modern-data-title">
                                                <i class="fas fa-university me-2"></i>
                                                {{ $daruliftaName }}
                                                <span class="status-badge status-processing ms-2">
                                                    {{ count($dataByDaruliftaName[$daruliftaName][$mailfolderDate]) }} Fatawa
                                                </span>
                                            </h4>
                                        </div>

                                        <div class="modern-table-container">
                                            <table class="modern-table">
                                                <thead>
                                                    <tr>
                                                        <th>
                                                            <i class="fas fa-hashtag me-1"></i>
                                                            S.No
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-file-alt me-1"></i>
                                                            File Code
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-user me-1"></i>
                                                            Sender
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-tag me-1"></i>
                                                            Fatwa Type
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-clock me-1"></i>
                                                            Received Date
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-list me-1"></i>
                                                            Category
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-exchange-alt me-1"></i>
                                                            Transfer to Checker
                                                        </th>
                                                        <th>
                                                            <i class="fas fa-upload me-1"></i>
                                                            Upload File
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                        $serialNumber = 1;
                                                    @endphp
                                                    @foreach ($dataByDaruliftaName[$daruliftaName][$mailfolderDate] as $item)
                                                        <tr>
                                                            <td>
                                                                <span class="fw-bold text-primary">{{ $serialNumber++ }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="file-code-badge">{{ $item->file_code }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="sender-badge">{{ $item->sender }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="status-badge {{ $item->ftype == 'New' ? 'status-received' : 'status-processing' }}">
                                                                    {{ $item->ftype }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <span class="text-muted">
                                                                    {{ \Carbon\Carbon::parse($item->mail_recived_date)->format('d M Y') }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <span class="category-text">{{ $item->category }}</span>
                                                            </td>
                                                            <td>
                                                                <select class="modern-select mufti-select"
                                                                        data-fatwa-id="{{ $item->id }}"
                                                                        data-mail-folder-date="{{ $item->mail_folder_date }}"
                                                                        data-darulifta-name="{{ $item->darulifta_name }}"
                                                                        data-checker="{{ $item->checker }}"
                                                                        data-file-name="{{ $item->file_name }}"
                                                                        data-transfer-by="{{ $item->transfer_by }}">
                                                                    <option value="" selected>Select Checker</option>
                                                                    @foreach($checkers as $check)
                                                                        <option value="{{ $check->checker_name }}">
                                                                            {{ $check->checker_name }}
                                                                        </option>
                                                                    @endforeach
                                                                </select>
                                                            </td>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <input type="file"
                                                                           class="modern-file-input upload-file"
                                                                           data-mail-folder-date="{{ $item->mail_folder_date }}"
                                                                           data-darulifta-name="{{ $item->darulifta_name }}"
                                                                           data-checker="{{ $item->checker }}"
                                                                           data-transfer-by="{{ $item->transfer_by }}">
                                                                    <button class="modern-upload-button upload-button" data-id="{{ $item->id }}">
                                                                        <i class="fas fa-upload me-1"></i>
                                                                        Upload
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @else
                                    <div class="p-4 text-center">
                                        <i class="fas fa-folder-open fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">No data available for {{ $daruliftaName }} on this date.</p>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

        <!-- Scripts Section -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

        <script>
            $(document).ready(function () {
                // Initialize DataTables if needed
                if ($('#myTable').length) {
                    $('#myTable').DataTable();
                }

                // Add loading states and animations
                $('.modern-select').on('change', function() {
                    $(this).addClass('loading');
                    setTimeout(() => {
                        $(this).removeClass('loading');
                    }, 1000);
                });

                $('.modern-upload-button').on('click', function() {
                    $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>Uploading...');
                });
            });

            document.addEventListener("DOMContentLoaded", function() {
                // Select all dropdowns with the class 'mufti-select'
                const mujeebSelects = document.querySelectorAll('.mufti-select');

                mujeebSelects.forEach(select => {
                    select.addEventListener('change', async function() {
                        // Add visual feedback
                        this.style.borderColor = '#4facfe';
                        this.style.boxShadow = '0 0 0 0.2rem rgba(79, 172, 254, 0.25)';

                        // Fetch attributes and values
                        const fatwaId = this.getAttribute('data-fatwa-id');
                        const selectedMufti = this.value;
                        const mailFolderDate = this.getAttribute('data-mail-folder-date');
                        const daruliftaName = this.getAttribute('data-darulifta-name');
                        const checker = this.getAttribute('data-checker');
                        const fileName = this.getAttribute('data-file-name');
                        const transferBy = this.getAttribute('data-transfer-by');

                        // Log values to the console
                        console.log('Selected Mufti:', selectedMufti);
                        console.log('Mail Folder Date:', mailFolderDate);
                        console.log('Darulifta Name:', daruliftaName);
                        console.log('Checker:', checker);
                        console.log('File Name:', fileName);
                        console.log('Transfer By:', transferBy);

                        try {
                            // Include the CSRF token in the request headers
                            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                            const response = await axios.post(`/transfer-mufti/${fatwaId}`, {
                                selectedMufti,
                                mailFolderDate,
                                daruliftaName,
                                checker,
                                transferBy,
                                fileName
                            }, {
                                headers: {
                                    'X-CSRF-TOKEN': csrfToken,
                                },
                            });

                            if (response.data.success) {
                                // Handle successful response with modern UI feedback
                                this.style.borderColor = '#38ef7d';
                                this.style.boxShadow = '0 0 0 0.2rem rgba(56, 239, 125, 0.25)';

                                // Show success message
                                const successMsg = document.createElement('div');
                                successMsg.innerHTML = '<i class="fas fa-check-circle me-1"></i>Transfer Successful!';
                                successMsg.className = 'alert alert-success mt-2 p-2 text-center';
                                successMsg.style.fontSize = '0.8rem';
                                this.parentNode.appendChild(successMsg);

                                setTimeout(() => {
                                    successMsg.remove();
                                    this.style.borderColor = '#e9ecef';
                                    this.style.boxShadow = 'none';
                                }, 3000);

                                console.log('Response:', response.data);
                            }
                        } catch (error) {
                            // Handle error with modern UI feedback
                            this.style.borderColor = '#ff6b6b';
                            this.style.boxShadow = '0 0 0 0.2rem rgba(255, 107, 107, 0.25)';

                            const errorMsg = document.createElement('div');
                            errorMsg.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Transfer Failed!';
                            errorMsg.className = 'alert alert-danger mt-2 p-2 text-center';
                            errorMsg.style.fontSize = '0.8rem';
                            this.parentNode.appendChild(errorMsg);

                            setTimeout(() => {
                                errorMsg.remove();
                                this.style.borderColor = '#e9ecef';
                                this.style.boxShadow = 'none';
                            }, 3000);

                            console.error('Error during the request:', error);
                        }
                    });
                });

                // Add file upload functionality
                const uploadButtons = document.querySelectorAll('.upload-button');

                uploadButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const id = this.dataset.id;
                        const fileInput = this.parentNode.querySelector('.upload-file');
                        const file = fileInput.files[0];
                        const mailFolderDate = fileInput.dataset.mailFolderDate;
                        const daruliftaName = fileInput.dataset.daruliftaName;
                        const checker = fileInput.dataset.checker;
                        const transferBy = fileInput.dataset.transferBy;

                        if (file) {
                            // Show loading state
                            const originalText = this.innerHTML;
                            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
                            this.disabled = true;

                            const formData = new FormData();
                            formData.append('file', file);
                            formData.append('id', id);
                            formData.append('mail_folder_date', mailFolderDate);
                            formData.append('darulifta_name', daruliftaName);
                            formData.append('checker', checker);
                            formData.append('transferBy', transferBy);

                            fetch('/upload-update', {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                }
                            }).then(response => response.json())
                              .then(data => {
                                  if (data.success) {
                                      // Success feedback
                                      this.innerHTML = '<i class="fas fa-check me-1"></i>Uploaded!';
                                      this.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';

                                      setTimeout(() => {
                                          this.innerHTML = originalText;
                                          this.style.background = '';
                                          this.disabled = false;
                                      }, 2000);

                                      console.log('File uploaded successfully:', data.filePath);
                                  } else {
                                      // Error feedback
                                      this.innerHTML = '<i class="fas fa-times me-1"></i>Failed!';
                                      this.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';

                                      setTimeout(() => {
                                          this.innerHTML = originalText;
                                          this.style.background = '';
                                          this.disabled = false;
                                      }, 2000);
                                  }
                              }).catch(error => {
                                  // Error feedback
                                  this.innerHTML = '<i class="fas fa-times me-1"></i>Error!';
                                  this.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';

                                  setTimeout(() => {
                                      this.innerHTML = originalText;
                                      this.style.background = '';
                                      this.disabled = false;
                                  }, 2000);

                                  console.error('Error:', error);
                              });
                        } else {
                            // Show file selection prompt
                            fileInput.style.borderColor = '#ff6b6b';
                            fileInput.style.background = '#fff5f5';

                            setTimeout(() => {
                                fileInput.style.borderColor = '#e9ecef';
                                fileInput.style.background = '#f8f9fa';
                            }, 2000);
                        }
                    });
                });
            });
        </script>

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
