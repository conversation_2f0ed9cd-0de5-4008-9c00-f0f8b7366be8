<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="reciption"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Reciption Page"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Include Libraries -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2-bootstrap-5-theme.min.css">

        <!-- Modern Styles -->
        <style>
            .modern-card {
                background: #ffffff;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
            }

            .modern-card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1.5rem 2rem;
                border: none;
            }

            .modern-card-body {
                padding: 2rem;
            }

            .modern-form-group {
                margin-bottom: 1.5rem;
            }

            .modern-label {
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 0.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .modern-input {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            .modern-input:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .modern-select {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            .modern-select:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .modern-textarea {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
                min-height: 120px;
                resize: vertical;
            }

            .modern-textarea:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .btn-modern {
                border-radius: 8px;
                padding: 0.75rem 2rem;
                font-weight: 600;
                border: none;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1rem;
            }

            .btn-modern-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .btn-modern-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                color: white;
            }

            .required-field {
                color: #e53e3e;
                font-weight: bold;
            }

            .form-section {
                background: #f8f9ff;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                border: 1px solid #e9ecef;
            }

            .section-title {
                color: #2d3748;
                font-weight: 700;
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1.1rem;
            }

            .white-box {
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            /* Select2 Custom Styling */
            .select2-container--bootstrap-5 .select2-selection--single {
                border: 2px solid #e9ecef !important;
                border-radius: 8px !important;
                height: 50px !important;
                padding: 0.75rem 1rem !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                background-color: white !important;
            }

            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
                line-height: 32px !important;
                padding-left: 0 !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                color: #495057 !important;
            }

            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
                height: 48px !important;
                right: 10px !important;
            }

            .select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
                border-color: #667eea !important;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            }

            .select2-dropdown {
                border: 2px solid #667eea !important;
                border-radius: 8px !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                z-index: 9999 !important;
            }

            .select2-results__option {
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                padding: 0.75rem 1rem !important;
            }

            .select2-results__option--highlighted {
                background-color: #667eea !important;
                color: white !important;
            }

            .select2-search__field {
                border: 1px solid #e9ecef !important;
                border-radius: 6px !important;
                padding: 0.5rem !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
            }

            .alert-danger {
                background-color: #fed7d7;
                border: 1px solid #feb2b2;
                color: #c53030;
                padding: 0.5rem;
                border-radius: 6px;
                margin-top: 0.25rem;
                font-size: 0.875rem;
            }
        </style>
        <div class="container-fluid py-4">
            <div class="white-box">
                <!-- Header Section -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="text-center">
                            <h4 class="mb-0 text-white">
                                <i class="fas fa-edit me-2"></i>
                                Edit Darulifta Ahlesunnat Reception
                            </h4>
                            <p class="mb-0 opacity-75">Update question details and manage reception information</p>
                        </div>
                    </div>

                    <div class="modern-card-body">
                        <form action="{{ route('reciptque.update', $reciptque->id) }}" method="POST" enctype="multipart/form-data" class="form-horizontal answer-form" role="form" id="action">
                            @method('PUT')
                            @csrf

                            <!-- Basic Information Section -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle text-primary"></i>
                                    Basic Information
                                </h5>
                                <div class="row">
                                    <!-- Date -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="rec_date" class="modern-label">
                                                <i class="fas fa-calendar-alt text-primary"></i>
                                                Date <span class="required-field">*</span>
                                            </label>
                                            <input type="date" name="rec_date" id="rec_date" tabindex="1" class="modern-input w-100" value="{{ $reciptque->rec_date }}" required>
                                            @error('rec_date')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Question Type -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="question_type" class="modern-label">
                                                <i class="fas fa-envelope text-primary"></i>
                                                Question Daily/Email <span class="required-field">*</span>
                                            </label>
                                            <select name="question_type" id="question_type" tabindex="2" class="modern-select w-100 searchable-select" required>
                                                <option value="Daily" {{ $reciptque->question_type === 'Daily' ? 'selected' : '' }}>Daily</option>
                                                <option value="Email" {{ $reciptque->question_type === 'Email' ? 'selected' : '' }}>Email</option>
                                            </select>
                                            @error('question_type')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Ifta Code -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="ifta_code" class="modern-label">
                                                <i class="fas fa-file-code text-primary"></i>
                                                Ifta Code <span class="required-field">*</span>
                                            </label>
                                            <input type="text" name="ifta_code" id="ifta_code" tabindex="1" class="modern-input w-100" value="{{ $reciptque->ifta_code }}" required>
                                            @error('ifta_code')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Phone -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="number_phone" class="modern-label">
                                                <i class="fas fa-phone text-primary"></i>
                                                Phone No
                                            </label>
                                            <input type="tel" name="phone" id="number_phone" tabindex="1" class="modern-input w-100" placeholder="Enter Phone No" minlength="10" maxlength="15" value="{{ $reciptque->phone }}">
                                            @error('phone')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Email -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="qs_email" class="modern-label">
                                                <i class="fas fa-envelope text-primary"></i>
                                                Email
                                            </label>
                                            <input type="email" name="email" id="qs_email" tabindex="3" class="modern-input w-100" placeholder="Enter Email" value="{{ $reciptque->email }}">
                                            @error('email')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Sayel -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="qs_title" class="modern-label">
                                                <i class="fas fa-user text-primary"></i>
                                                Sayel <span class="required-field">*</span>
                                            </label>
                                            <input type="text" name="sayel" id="qs_title" tabindex="2" class="modern-input w-100" placeholder="Sayel" value="{{ $reciptque->sayel }}" required>
                                            @error('sayel')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Address -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="qs_address" class="modern-label">
                                                <i class="fas fa-map-marker-alt text-primary"></i>
                                                Address
                                            </label>
                                            <input type="text" name="address" id="qs_address" tabindex="4" class="modern-input w-100" placeholder="Address" value="{{ $reciptque->address }}">
                                            @error('address')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Muzo -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="issue" class="modern-label">
                                                <i class="fas fa-tags text-primary"></i>
                                                Muzo <span class="required-field">*</span>
                                            </label>
                                            <select id="issue" name="issue" tabindex="5" class="modern-select w-100 searchable-select">
                                                <option value="">Select Muzo</option>
                                                @foreach($issues as $issue)
                                                    <option value="{{ $issue->id }}" {{ $reciptque->issue == $issue->name ? 'selected' : '' }}>
                                                        {{ $issue->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('issue')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Zeli Muzo -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="sub_issue" class="modern-label">
                                                <i class="fas fa-tag text-primary"></i>
                                                Zeli Muzo
                                            </label>
                                            <select id="sub_issue" name="sub_issue" tabindex="6" class="modern-select w-100 searchable-select">
                                                <option value="">Select a Zeli-Muzo</option>
                                                @foreach($subissues as $subIssue)
                                                    <option value="{{ $subIssue->id }}" {{ $reciptque->sub_issue == $subIssue->name ? 'selected' : '' }}>
                                                        {{ $subIssue->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('sub_issue')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Question Details Section -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-question-circle text-primary"></i>
                                    Question Details
                                </h5>
                                

                                <div class="row">
                                    <!-- Question Title -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="question_title" class="modern-label">
                                                <i class="fas fa-heading text-primary"></i>
                                                Question Title <span class="required-field">*</span>
                                            </label>
                                            <input type="text" name="question_title" tabindex="7" class="modern-input w-100" placeholder="Enter Question Title" value="{{ $reciptque->question_title }}" required>
                                            @error('question_title')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Expected Date -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="expected_date" class="modern-label">
                                                <i class="fas fa-calendar-check text-primary"></i>
                                                Expected Date <span class="required-field">*</span>
                                            </label>
                                            <input type="date" name="expected_date" tabindex="8" class="modern-input w-100" id="expected_date" value="{{ $reciptque->expected_date }}" required>
                                            @error('expected_date')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Branch -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="question_branch" class="modern-label">
                                                <i class="fas fa-building text-primary"></i>
                                                Branch <span class="required-field">*</span>
                                            </label>
                                            <select name="question_branch" tabindex="9" class="modern-select w-100">
                                                <option value="{{ $reciptque->question_branch }}">{{ $reciptque->question_branch }}</option>
                                            </select>
                                            @error('question_branch')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Question Text -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="modern-form-group">
                                            <label for="summernote" class="modern-label">
                                                <i class="fas fa-question-circle text-primary"></i>
                                                Question <span class="required-field">*</span>
                                            </label>

                                            
                                            <!-- Hidden input to preserve question data -->
                                            <input type="hidden" id="question-data" value="{!! htmlspecialchars($reciptque->question ?? '', ENT_QUOTES, 'UTF-8') !!}">
                                            
                                            <textarea name="question" tabindex="10" class="modern-textarea w-100" id="summernote" rows="8" placeholder="Enter Question Here" required>{!! old('question', $reciptque->question ?? '') !!}</textarea>
                                            @error('question')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Assignment Section -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-user-check text-primary"></i>
                                    Assignment Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="modern-form-group">
                                            <label for="assign_id" class="modern-label">
                                                <i class="fas fa-user-plus text-primary"></i>
                                                Assign Question To Mujeeb
                                            </label>
                                            <select name="assign_id" tabindex="12" class="modern-select w-100 searchable-select">
                                                <option value="">Select Mujeeb For Send Fatwa</option>
                                                @foreach($mujeeb as $assignUser)
                                                    <option value="{{ $assignUser->mujeeb_name }}" {{ $reciptque->assign_id == $assignUser->mujeeb_name ? 'selected' : '' }}>
                                                        {{ $assignUser->mujeeb_name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('assign_id')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Section -->
                            <div class="text-center">
                                <button id="btn_submit" class="btn-modern btn-modern-primary" type="submit">
                                    <i class="fas fa-save"></i>
                                    Update Question
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>        
        <!-- Scripts -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
        
        <!-- Summernote CSS and JS -->
        <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
        
        <script>
        $(document).ready(function () {
            // Initialize Select2 for searchable dropdowns
            $('.searchable-select').select2({
                placeholder: 'Search and select...',
                allowClear: true,
                width: '100%',
                theme: 'bootstrap-5',
                dropdownParent: $('body')
            });

            // Handle Muzo change to load Zeli Muzo
            $('#issue').on('change', function () {
                var issueId = $(this).val();
                var subIssueSelect = $('#sub_issue');

                if (issueId) {
                    // Show loading state
                    subIssueSelect.prop('disabled', true);
                    subIssueSelect.empty().append('<option value="">Loading...</option>');

                    $.ajax({
                        url: '{{ route("get-sub-issues") }}',
                        type: 'GET',
                        data: {issue_id: issueId},
                        success: function (data) {
                            subIssueSelect.empty();
                            subIssueSelect.append('<option value="">Select a Zeli-Muzo</option>');
                            $.each(data, function (key, value) {
                                subIssueSelect.append('<option value="' + value.id + '">' + value.name + '</option>');
                            });

                            // Re-initialize Select2 for the updated dropdown
                            subIssueSelect.select2('destroy').select2({
                                placeholder: 'Search and select Zeli-Muzo...',
                                allowClear: true,
                                width: '100%',
                                theme: 'bootstrap-5',
                                dropdownParent: $('body')
                            });

                            subIssueSelect.prop('disabled', false);
                        },
                        error: function() {
                            subIssueSelect.empty().append('<option value="">Error loading data</option>');
                            subIssueSelect.prop('disabled', false);
                        }
                    });
                } else {
                    subIssueSelect.empty();
                    subIssueSelect.append('<option value="">Select a Zeli-Muzo</option>');
                    subIssueSelect.select2('destroy').select2({
                        placeholder: 'Search and select Zeli-Muzo...',
                        allowClear: true,
                        width: '100%',
                        theme: 'bootstrap-5',
                        dropdownParent: $('body')
                    });
                }
            });

            // Phone number auto-fill functionality
            $('#number_phone').on('blur', function () {
                let phone = $(this).val();
                if (phone) {
                    $.ajax({
                        url: '{{ route("checkPhoneExist") }}',
                        type: 'GET',
                        data: { phone: phone },
                        success: function (data) {
                            if (data) {
                                if (data.sayel) {
                                    $('#qs_title').val(data.sayel);
                                }
                                if (data.address) {
                                    $('#qs_address').val(data.address);
                                }
                            }
                        }
                    });
                }
            });

            // Email auto-fill functionality
            $('#qs_email').on('blur', function () {
                let email = $(this).val();
                if (email) {
                    $.ajax({
                        url: '{{ route("checkEmailExist") }}',
                        type: 'GET',
                        data: { email: email },
                        success: function (data) {
                            if (data) {
                                if (data.sayel) {
                                    $('#qs_title').val(data.sayel);
                                }
                                if (data.address) {
                                    $('#qs_address').val(data.address);
                                }
                            }
                        }
                    });
                }
            });

            // Question type change handler
            $('#question_type').on('change', function () {
                if ($(this).val() === 'Email') {
                    $('#number_phone').prop('disabled', true);
                } else {
                    $('#number_phone').prop('disabled', false);
                }
            });

            // Expected date calculation (only when rec_date changes, not on page load)
            function setExpectedDate() {
                const recDate = new Date($('#rec_date').val());
                const expectedDate = new Date(recDate);
                expectedDate.setDate(recDate.getDate() + 3);
                const formattedExpectedDate = expectedDate.toISOString().split('T')[0];
                $('#expected_date').val(formattedExpectedDate);
            }

            // Only update expected date when rec_date changes, preserve existing dates on edit
            $('#rec_date').on('change', setExpectedDate);

            // Ensure question content is loaded first
            var questionContent = {!! json_encode($reciptque->question ?? '') !!};
            
            // Set content immediately in textarea
            if (questionContent && questionContent.trim() !== '') {
                $('#summernote').val(questionContent);
            }

            // Try to initialize Summernote editor
            try {
                $('#summernote').summernote({
                    height: 200,
                    placeholder: 'Enter Question Here',
                    toolbar: [
                        ['style', ['style']],
                        ['font', ['bold', 'underline', 'clear']],
                        ['fontname', ['fontname']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['table', ['table']],
                        ['insert', ['link']],
                        ['view', ['fullscreen', 'codeview', 'help']]
                    ],
                    fontNames: ['Arial', 'Arial Black', 'Comic Sans MS', 'Courier New', 'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande', 'Tahoma', 'Times New Roman', 'Verdana', 'Jameel Noori Nastaleeq'],
                    fontNamesIgnoreCheck: ['Jameel Noori Nastaleeq']
                });

                // Set the content after initialization
                if (questionContent && questionContent.trim() !== '') {
                    $('#summernote').summernote('code', questionContent);
                }
            } catch (error) {
                // Summernote initialization failed, using plain textarea
                // Fallback: ensure textarea has the content
                if (questionContent && questionContent.trim() !== '') {
                    $('#summernote').val(questionContent);
                } else {
                    var questionData = $('#question-data').val();
                    if (questionData) {
                        $('#summernote').val(questionData);
                    }
                }
            }

            // Trigger question type change on page load to set initial state
            $('#question_type').trigger('change');
        });
        </script>


        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
