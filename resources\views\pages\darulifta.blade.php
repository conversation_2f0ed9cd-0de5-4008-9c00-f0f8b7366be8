<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="darulifta"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Darulifta Management"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #4f46e5;
                --primary-hover: #4338ca;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 1200px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #6366f1 100%);
                color: white;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(79, 70, 229, 0.2);
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .page-subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
                margin-top: 0.5rem;
                margin-bottom: 0;
            }

            .content-grid {
                display: grid;
                grid-template-columns: 1fr 2fr;
                gap: 2rem;
                align-items: start;
            }

            .form-card {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
                height: fit-content;
                position: sticky;
                top: 2rem;
            }

            .form-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-icon {
                width: 24px;
                height: 24px;
                color: var(--primary-color);
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                font-weight: 600;
                color: var(--gray-700);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .form-input {
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.75rem;
                font-size: 1rem;
                transition: all 0.2s ease;
                background: var(--gray-50);
            }

            .form-input:focus {
                outline: none;
                border-color: var(--primary-color);
                background: white;
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            .btn-primary {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #6366f1 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
            }

            .table-card {
                background: white;
                border-radius: 1rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
            }

            .table-header {
                background: var(--gray-50);
                padding: 1.5rem 2rem;
                border-bottom: 1px solid var(--gray-200);
            }

            .table-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .table-count {
                background: var(--primary-color);
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 9999px;
                font-size: 0.875rem;
                font-weight: 600;
            }

            .modern-table {
                width: 100%;
                border-collapse: collapse;
            }

            .modern-table th {
                background: var(--gray-50);
                padding: 1rem 1.5rem;
                text-align: left;
                font-weight: 600;
                color: var(--gray-700);
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                border-bottom: 1px solid var(--gray-200);
            }

            .modern-table td {
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--gray-100);
                vertical-align: middle;
            }

            .modern-table tbody tr:hover {
                background: var(--gray-50);
            }

            .code-badge {
                background: linear-gradient(135deg, var(--primary-color) 0%, #6366f1 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 0.5rem;
                font-weight: 600;
                font-family: 'Monaco', 'Menlo', monospace;
                font-size: 0.875rem;
            }

            .name-cell {
                font-weight: 500;
                color: var(--gray-800);
            }

            .action-buttons {
                display: flex;
                gap: 0.5rem;
                align-items: center;
            }

            .btn-edit {
                padding: 0.5rem 1rem;
                background: var(--warning-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.25rem;
            }

            .btn-edit:hover {
                background: #d97706;
                transform: translateY(-1px);
                color: white;
                text-decoration: none;
            }

            .btn-delete {
                padding: 0.5rem 1rem;
                background: var(--danger-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.2s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.25rem;
            }

            .btn-delete:hover {
                background: #dc2626;
                transform: translateY(-1px);
            }

            .delete-form {
                display: inline;
            }

            .empty-state {
                text-align: center;
                padding: 3rem 2rem;
                color: var(--gray-600);
            }

            .empty-icon {
                width: 64px;
                height: 64px;
                margin: 0 auto 1rem;
                color: var(--gray-300);
            }

            .alert {
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                margin-bottom: 1.5rem;
                border: 1px solid;
            }

            .alert-success {
                background: #ecfdf5;
                border-color: #10b981;
                color: #065f46;
            }

            .alert-error {
                background: #fef2f2;
                border-color: #ef4444;
                color: #991b1b;
            }

            @media (max-width: 768px) {
                .content-grid {
                    grid-template-columns: 1fr;
                    gap: 1.5rem;
                }
                
                .modern-container {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                }
                
                .form-card {
                    position: static;
                }
                
                .action-buttons {
                    flex-direction: column;
                    align-items: stretch;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Darulifta Management</h1>
                <p class="page-subtitle">Manage and organize your Darulifta institutions efficiently</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success">
                    <strong>Success!</strong> {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    <strong>Error!</strong> {{ session('error') }}
                </div>
            @endif

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Add Form -->
                <div class="form-card">
                    <h2 class="form-title">
                        <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add New Darulifta
                    </h2>
                    
                    <form action="{{ route('darulifta.add') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="darul_code" class="form-label">Darulifta Code</label>
                            <input type="text" 
                                   class="form-input" 
                                   id="darul_code" 
                                   name="darul_code" 
                                   placeholder="e.g., DIF001"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label for="darul_name" class="form-label">Darulifta Name</label>
                            <input type="text" 
                                   class="form-input" 
                                   id="darul_name" 
                                   name="darul_name" 
                                   placeholder="Enter institution name"
                                   required>
                        </div>
                        
                        <button type="submit" class="btn-primary">
                            Add Darulifta
                        </button>
                    </form>
                </div>

                <!-- Data Table -->
                <div class="table-card">
                    <div class="table-header">
                        <h2 class="table-title">
                            <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                            </svg>
                            Registered Daruliftas
                            <span class="table-count">{{ count($daruliftas) }}</span>
                        </h2>
                    </div>

                    @if(count($daruliftas) > 0)
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Institution Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($daruliftas as $data)
                                <tr>
                                    <td>
                                        <span class="code-badge">{{ $data->darul_code }}</span>
                                    </td>
                                    <td class="name-cell">{{ $data->darul_name }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('update.darul', ['id' => $data->id]) }}" class="btn-edit">
                                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                                </svg>
                                                Edit
                                            </a>
                                            <form action="{{ route('darulifta.delete', ['id' => $data->id]) }}" method="POST" class="delete-form">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="btn-delete" 
                                                        onclick="return confirm('Are you sure you want to delete this Darulifta? This action cannot be undone.')">
                                                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                    </svg>
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div class="empty-state">
                            <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                            </svg>
                            <h3>No Daruliftas Found</h3>
                            <p>Start by adding your first Darulifta institution using the form on the left.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>