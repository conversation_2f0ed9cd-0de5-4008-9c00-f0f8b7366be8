<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="reciption"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="question_list"></x-navbars.navs.auth>

        <!-- Modern Styles -->
        <style>
            .modern-card {
                background: #ffffff;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
            }

            .modern-card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1.5rem 2rem;
                border: none;
            }

            .modern-card-body {
                padding: 2rem;
            }

            .btn-modern {
                border-radius: 8px;
                padding: 0.5rem 1.5rem;
                font-weight: 500;
                border: none;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .btn-modern-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .btn-modern-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                color: white;
            }

            .btn-modern-warning {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
            }

            .btn-modern-warning:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
                color: white;
            }

            .btn-modern-danger {
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                color: white;
            }

            .btn-modern-danger:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
                color: white;
            }

            .table-modern {
                border-collapse: separate;
                border-spacing: 0;
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            .table-modern thead th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: 600;
                padding: 1rem;
                border: none;
                text-align: center;
                position: relative;
            }

            .table-modern tbody td {
                padding: 1rem;
                border-bottom: 1px solid #e9ecef;
                vertical-align: middle;
                text-align: center;
            }

            .table-modern tbody tr:hover {
                background-color: #f8f9ff;
                transform: scale(1.01);
                transition: all 0.3s ease;
            }

            .modern-select {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.5rem 1rem;
                background: white;
                transition: all 0.3s ease;
                font-family: inherit;
            }

            .modern-select:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .badge-serial {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 0.4rem 0.8rem;
                border-radius: 20px;
                font-weight: 600;
                font-size: 0.85rem;
            }

            .page-title {
                color: #2d3748;
                font-weight: 700;
                margin-bottom: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .stats-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 12px;
                padding: 1rem;
                text-align: center;
                margin-bottom: 1rem;
            }

            /* DataTables Modern Styling */
            .dataTables_wrapper .dataTables_length select,
            .dataTables_wrapper .dataTables_filter input {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.5rem;
            }

            .dataTables_wrapper .dataTables_paginate .paginate_button {
                border-radius: 8px;
                margin: 0 2px;
            }

            .dataTables_wrapper .dataTables_paginate .paginate_button.current {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                border: none !important;
                color: white !important;
            }

            .table-modern thead th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: 600;
                padding: 1rem;
                border: none;
                text-align: center;
                position: relative;
            }

            .table-modern tbody td {
                padding: 1rem;
                vertical-align: middle;
                border-bottom: 1px solid #e9ecef;
            }

            .table-modern tbody tr:hover {
                background-color: #f8f9fa;
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }

            .badge-serial {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 0.5rem 0.75rem;
                border-radius: 50%;
                font-weight: 600;
                font-size: 0.875rem;
            }

            /* Force table headers to always be visible - CRITICAL */
            #myTable thead,
            table#myTable thead,
            .dataTables_wrapper #myTable thead,
            .dataTables_wrapper table#myTable thead {
                display: table-header-group !important;
                visibility: visible !important;
                opacity: 1 !important;
                height: auto !important;
                overflow: visible !important;
            }

            #myTable thead th,
            table#myTable thead th,
            .dataTables_wrapper #myTable thead th,
            .dataTables_wrapper table#myTable thead th {
                display: table-cell !important;
                visibility: visible !important;
                opacity: 1 !important;
                height: auto !important;
                overflow: visible !important;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                padding: 12px 8px !important;
                border: none !important;
                font-weight: 600 !important;
            }

            /* Prevent any DataTables CSS from hiding headers */
            .dataTables_wrapper .table-modern thead,
            .dataTables_wrapper .table thead,
            .dataTables_wrapper thead {
                display: table-header-group !important;
            }

            .dataTables_wrapper .table-modern thead th,
            .dataTables_wrapper .table thead th,
            .dataTables_wrapper thead th {
                display: table-cell !important;
            }

            /* Override any responsive hiding */
            @media (max-width: 768px) {
                #myTable thead,
                #myTable thead th {
                    display: table-header-group !important;
                    display: table-cell !important;
                }
            }

            /* Additional header fixing */
            .table-header-fixed {
                display: table-header-group !important;
                position: relative !important;
                z-index: 10 !important;
            }

            .table-header-fixed th {
                display: table-cell !important;
                position: relative !important;
                z-index: 10 !important;
            }
        </style>

        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">

        <div class="container-fluid py-4">
            <meta name="csrf-token" content="{{ csrf_token() }}">

            <!-- Header Section -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0 text-white">
                                <i class="fas fa-question-circle me-2"></i>
                                Question Management
                            </h4>
                            <p class="mb-0 opacity-75">Manage and assign questions to Mujeeb</p>
                        </div>
                        @if(!Auth::user()->isMujeeb())
                        <a href="{{ route('reciptque.index') }}" class="btn-modern btn-modern-primary">
                            <i class="fas fa-plus"></i>
                            Add Question
                        </a>
                        @endif
                    </div>
                </div>

                <div class="modern-card-body">
                    <div class="table-responsive">
                        <table class="table-modern w-100" id="myTable">
                            <thead class="table-header-fixed">
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>S.No</th>
                                    <th><i class="fas fa-envelope me-1"></i>Daily/Email</th>
                                    <th><i class="fas fa-file-code me-1"></i>Fatwa No</th>
                                    <th><i class="fas fa-question me-1"></i>Question Title</th>
                                    <th><i class="fas fa-tags me-1"></i>Category</th>
                                    <th><i class="fas fa-user-check me-1"></i>Send To Mujeeb</th>
                                    <th><i class="fas fa-cogs me-1"></i>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                $serialNumber = 1; // Initialize the serial number
                                $questonss = (count(Auth::user()->roles) > 1) ? $questions : $question_b;
                                @endphp
                                @foreach($questonss as $question)
                                <tr>
                                    <td>
                                        <span class="badge-serial">{{ $serialNumber++ }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-white px-3 py-1 rounded-pill">
                                            {{ $question->question_type }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-primary">{{ $question->ifta_code }}</div>
                                    </td>
                                    <td>
                                        <div class="text-start" style="max-width: 300px;">
                                            {{ Str::limit($question->question_title, 50) }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-success text-white px-3 py-1 rounded-pill">
                                            {{ $question->issue }}
                                        </span>
                                    </td>
                                    <td style="width: 15%;">
                                        <select class="modern-select mujeeb-select w-100" data-question-id="{{ $question->id }}">
                                            <option value="">Select Mujeeb</option>
                                            @foreach($mujeebs as $mujeeb)
                                                <option value="{{ $mujeeb->mujeeb_name }}">{{ $mujeeb->mujeeb_name }}</option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2 justify-content-center">
                                            <a href="{{ route('reciptque.edit', ['id' => $question->id]) }}"
                                               class="btn-modern btn-modern-warning btn-sm"
                                               title="Edit Question">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('reciptque.destroy', ['id' => $question->id]) }}"
                                                  method="POST"
                                                  style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                        class="btn-modern btn-modern-danger btn-sm"
                                                        onclick="return confirm('Are you sure you want to delete this entry?')"
                                                        title="Delete Question">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Scripts -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

        <script>
            $(document).ready(function () {
                $('#myTable').DataTable({
                    "pageLength": 25,
                    "responsive": true,
                    "language": {
                        "search": "Search Questions:",
                        "lengthMenu": "Show _MENU_ questions per page",
                        "info": "Showing _START_ to _END_ of _TOTAL_ questions",
                        "paginate": {
                            "first": "First",
                            "last": "Last",
                            "next": "Next",
                            "previous": "Previous"
                        }
                    },
                    "columnDefs": [
                        { "orderable": false, "targets": [5, 6] } // Disable sorting for Mujeeb select and Actions columns
                    ]
                });
            });
        </script>

        <script src="//cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>

        <script>
            $(document).ready(function () {
                console.log('Initializing DataTable for question page...');

                // Ensure table structure is correct before initialization
                if ($('#myTable thead').length === 0) {
                    console.error('Table head not found!');
                    return;
                }

                // Force headers to be visible
                $('#myTable thead').css({
                    'display': 'table-header-group',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                $('#myTable thead th').css({
                    'display': 'table-cell',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Initialize DataTable with minimal configuration
                try {
                    var table = $('#myTable').DataTable({
                        "pageLength": 25,
                        "responsive": false,
                        "autoWidth": false,
                        "destroy": true,
                        "dom": 'lfrtip', // Standard layout
                        "language": {
                            "search": "Search Questions:",
                            "lengthMenu": "Show _MENU_ questions per page",
                            "info": "Showing _START_ to _END_ of _TOTAL_ questions"
                        },
                        "columnDefs": [
                            { "orderable": false, "targets": [5, 6] }
                        ],
                        "createdRow": function(row, data, dataIndex) {
                            // Ensure headers remain visible after row creation
                            $('#myTable thead').show();
                        },
                        "drawCallback": function() {
                            // Force headers to show after each draw
                            $('#myTable thead').show().css('display', 'table-header-group');
                            $('#myTable thead th').show().css('display', 'table-cell');
                        },
                        "initComplete": function() {
                            console.log('DataTable initialized successfully');
                            // Final header visibility check
                            $('#myTable thead').show().css('display', 'table-header-group');
                            $('#myTable thead th').show().css('display', 'table-cell');
                        }
                    });

                    console.log('DataTable created successfully');

                } catch (error) {
                    console.error('Error initializing DataTable:', error);
                }

                // Continuous header visibility check
                setInterval(function() {
                    if ($('#myTable thead').is(':hidden')) {
                        console.log('Headers hidden, forcing to show...');
                        $('#myTable thead').show().css('display', 'table-header-group');
                        $('#myTable thead th').show().css('display', 'table-cell');
                    }
                }, 1000);
            });
        </script>

        <script>
            document.addEventListener("DOMContentLoaded", function() {
                const mujeebSelects = document.querySelectorAll('.mujeeb-select');

                mujeebSelects.forEach(select => {
                    select.addEventListener('change', async function() {
                        const questionId = this.getAttribute('data-question-id');
                        const selectedMujeebId = this.value;

                        // Add loading state
                        this.style.opacity = '0.6';
                        this.disabled = true;

                        try {
                            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                            const response = await axios.post(`/update-assign-id/${questionId}`, { selectedMujeebId }, {
                                headers: {
                                    'X-CSRF-TOKEN': csrfToken,
                                },
                            });

                            if (response.data.success) {
                                // Show success feedback
                                this.style.borderColor = '#28a745';
                                setTimeout(() => {
                                    this.style.borderColor = '#667eea';
                                }, 2000);
                            }
                        } catch (error) {
                            console.error(error);
                            // Show error feedback
                            this.style.borderColor = '#dc3545';
                            setTimeout(() => {
                                this.style.borderColor = '#e9ecef';
                            }, 2000);
                        } finally {
                            // Remove loading state
                            this.style.opacity = '1';
                            this.disabled = false;
                        }
                    });
                });
            });
        </script>

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
