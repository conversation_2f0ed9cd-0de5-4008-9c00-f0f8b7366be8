<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DailyPerformance;
use App\Models\Task;
use App\Models\UserRestriction;
use App\Models\UnlockRequest;
use App\Services\MahlENazarService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardNotifications extends Component
{
    public $notifications = [];
    public $sortedNotifications = [];
    public $showNotifications = false;
    public $isUnlockModalOpen = false;
    public $selectedUnlockRequest = null;
    public $unlockReason = '';

    protected $mahlENazarService;

    public function __construct()
    {
        $this->mahlENazarService = app(MahlENazarService::class);
    }

    public function mount()
    {
        $this->notifications = [];
        $this->sortedNotifications = [];
        $this->showNotifications = false;
        $this->loadNotifications();
    }

    public function render()
    {
        return view('livewire.dashboard-notifications');
    }

    public function loadNotifications()
    {
        $user = auth()->user();

        if (!$user) {
            $this->notifications = [];
            $this->sortedNotifications = [];
            $this->showNotifications = false;
            return;
        }

        $this->notifications = [];

        try {
            // Unlock request notifications for nazim-ul-umoor
            if (method_exists($user, 'hasRole') && $user->hasRole('nazim-ul-umoor')) {
                $this->checkUnlockRequests($user);
            }
            // Check daily performance submission
            $this->checkDailyPerformance($user);

            // Check Mahl-e-Nazar limit
            $this->checkMahlENazarLimit($user);

            // Check pending tasks
            $this->checkPendingTasks($user);

            // Check active restrictions
            $this->checkActiveRestrictions($user);

            // Check fatwa assignment notifications
            $this->checkFatwaAssignments($user);

            // Check team notifications (for Superiors)
            if (method_exists($user, 'isSuperior') && method_exists($user, 'isNazim')) {
                if ($user->isSuperior() || $user->isNazim()) {
                    $this->checkTeamNotifications($user);
                }
            }
        } catch (\Exception $e) {
            // Log error but don't break the page
            \Log::error('Error loading notifications: ' . $e->getMessage());
        }

        $this->showNotifications = count($this->notifications) > 0;
        $this->sortedNotifications = $this->getNotificationsByPriority();
        \Log::info('User roles:', [$user->roles->pluck('name')]);
        \Log::info('Has nazim-ul-umoor:', [$user->hasRole('nazim-ul-umoor')]);
    }

    private function checkDailyPerformance($user)
    {
        try {
            // Only check performance for users with assigned tasks
            if (!$user->hasAssignedTasks()) {
                return;
            }

            $hasSubmitted = DailyPerformance::where('user_id', $user->id)
                ->where('performance_date', Carbon::today())
                ->where('is_submitted', true)
                ->exists();

            if (!$hasSubmitted && !Carbon::today()->isWeekend()) {
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'assignment_turned_in',
                    'title' => 'Daily Performance Pending',
                    'message' => 'Please submit your daily performance report.',
                    'action_url' => route('daily-performance.create'),
                    'action_text' => 'Submit Now',
                    'priority' => 'high',
                ];
            }
        } catch (\Exception $e) {
            // Skip this notification if there's an error
        }
    }

    private function checkMahlENazarLimit($user)
    {
        try {
            if (!$this->mahlENazarService) {
                return;
            }

            $count = $this->mahlENazarService->getMahlENazarCount($user->name);
            $limit = $this->mahlENazarService->getUserLimit($user);

            if ($count >= $limit) {
                $this->notifications[] = [
                    'type' => 'danger',
                    'icon' => 'warning',
                    'title' => 'Fatawa Limit Reached',
                    'message' => "You have {$count} fatawa in Mahl-e-Nazar status (limit: {$limit}). Please resolve existing fatawa.",
                    'action_url' => route('mahlenazar-fatawa'),
                    'action_text' => 'View Fatawa',
                    'priority' => 'critical',
                ];
            } elseif ($count >= max(1, $limit - 2)) { // Near limit: last 2 before limit
                $remaining = $limit - $count;
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'info',
                    'title' => 'Approaching Fatawa Limit',
                    'message' => "You have {$count} fatawa in Mahl-e-Nazar status. Only {$remaining} more allowed.",
                    'action_url' => route('mahlenazar-fatawa'),
                    'action_text' => 'View Status',
                    'priority' => 'medium',
                ];
            }
        } catch (\Exception $e) {
            // Skip this notification if there's an error
        }
    }

    private function checkPendingTasks($user)
    {
        $pendingTasks = Task::where('assigned_to', $user->id)
            ->where('status', 'pending')
            ->where('due_date', '<=', Carbon::today()->addDays(2))
            ->count();

        if ($pendingTasks > 0) {
            $this->notifications[] = [
                'type' => 'info',
                'icon' => 'task',
                'title' => 'Pending Tasks',
                'message' => "You have {$pendingTasks} pending tasks due soon.",
                'action_url' => route('my-tasks'),
                'action_text' => 'View Tasks',
                'priority' => 'medium',
            ];
        }

        $overdueTasks = Task::where('assigned_to', $user->id)
            ->where('status', 'pending')
            ->where('due_date', '<', Carbon::today())
            ->count();

        if ($overdueTasks > 0) {
            $this->notifications[] = [
                'type' => 'danger',
                'icon' => 'schedule',
                'title' => 'Overdue Tasks',
                'message' => "You have {$overdueTasks} overdue tasks.",
                'action_url' => route('my-tasks'),
                'action_text' => 'View Tasks',
                'priority' => 'high',
            ];
        }
    }

    private function checkActiveRestrictions($user)
    {
        $restrictions = UserRestriction::where('user_id', $user->id)
            ->where('is_active', true)
            ->get();

        foreach ($restrictions as $restriction) {
            $this->notifications[] = [
                'type' => 'danger',
                'icon' => 'block',
                'title' => 'Account Restriction',
                'message' => $restriction->reason,
                'action_url' => null,
                'action_text' => null,
                'priority' => 'critical',
            ];
        }
    }

    private function checkFatwaAssignments($user)
    {
        try {
            // Get viewed notifications from session
            $viewedNotifications = session()->get('viewed_fatwa_notifications', []);
            
            // Check for newly assigned fatawa (assigned in last 24 hours and not yet viewed)
            $newAssignments = \DB::table('questions')
                ->where('assign_id', $user->name)
                ->where('mujeeb_send_date', '>=', Carbon::now()->subDay())
                ->whereNotIn('id', $viewedNotifications)
                ->get();

            foreach ($newAssignments as $assignment) {
                // Get the assigner's name from the question_branch or created_by field
                $assignerName = $this->getAssignerName($assignment);
                
                $this->notifications[] = [
                    'type' => 'info',
                    'icon' => 'assignment',
                    'title' => 'New Fatwa Assignment',
                    'message' => "{$assignerName} assigned you Fatwa No: {$assignment->ifta_code}",
                    'action_url' => route('m_question.index'),
                    'action_text' => 'View Fatwa',
                    'priority' => 'high',
                    'fatwa_id' => $assignment->id,
                    'is_fatwa_assignment' => true,
                ];
            }
        } catch (\Exception $e) {
            \Log::error('Error checking fatwa assignments: ' . $e->getMessage());
        }
    }

    private function getAssignerName($assignment)
    {
        // Try to determine who assigned the fatwa based on the question_branch
        $branchToAssigner = [
            'Noorulirfan' => 'Noorulirfan',
            'Faizan-e-Ajmair' => 'Faizan-e-Ajmair', 
            'Markaz-ul-Iqtisaad' => 'Markaz-ul-Iqtisaad',
            'Gulzar-e-Taiba' => 'Gulzar-e-Taiba',
        ];

        // If we can find a specific assigner based on branch, use that
        if (isset($branchToAssigner[$assignment->question_branch])) {
            return $branchToAssigner[$assignment->question_branch];
        }

        // Otherwise, use the branch name or default to the branch
        return $assignment->question_branch ?? 'Darulifta Admin';
    }

    private function checkTeamNotifications($user)
    {
        if ($user->isSuperior()) {
            // Check team performance submissions
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->departments->flatMap->users->pluck('id');
            $teamUserIds = $assistantIds->merge($departmentUserIds)->unique();

            // Only count users who have assigned tasks and haven't submitted performance
            $usersWithTasks = \App\Models\User::whereIn('id', $teamUserIds)
                ->whereHas('assignedTasks', function ($query) {
                    $query->whereNotIn('status', ['completed', 'cancelled']);
                })
                ->pluck('id');

            // Count users with tasks who haven't submitted today's performance
            $submittedUserIds = DailyPerformance::whereIn('user_id', $usersWithTasks)
                ->where('performance_date', Carbon::today())
                ->where('is_submitted', true)
                ->pluck('user_id');

            $pendingReports = $usersWithTasks->diff($submittedUserIds)->count();

            if ($pendingReports > 0) {
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'group',
                    'title' => 'Team Performance Pending',
                    'message' => "{$pendingReports} team members haven't submitted today's performance.",
                    'action_url' => route('performance-management'),
                    'action_text' => 'View Reports',
                    'priority' => 'medium',
                ];
            }

            // Check team task completion
            $pendingTeamTasks = Task::whereIn('assigned_to', $teamUserIds)
                ->where('status', 'pending')
                ->where('due_date', '<=', Carbon::today())
                ->count();

            if ($pendingTeamTasks > 0) {
                $this->notifications[] = [
                    'type' => 'info',
                    'icon' => 'assignment',
                    'title' => 'Team Tasks Pending',
                    'message' => "{$pendingTeamTasks} team tasks are pending or overdue.",
                    'action_url' => route('workflow-tasks.index'),
                    'action_text' => 'Manage Tasks',
                    'priority' => 'medium',
                ];
            }
        }

        if ($user->isNazim()) {
            // Check system-wide issues
            $usersAtLimit = $this->mahlENazarService->getStatistics()['users_at_limit'] ?? 0;
            $usersOverLimit = $this->mahlENazarService->getStatistics()['users_over_limit'] ?? 0;

            if ($usersOverLimit > 0) {
                $this->notifications[] = [
                    'type' => 'danger',
                    'icon' => 'warning',
                    'title' => 'Users Over Limit',
                    'message' => "{$usersOverLimit} users have exceeded the Mahl-e-Nazar limit.",
                    'action_url' => route('mahl-e-nazar-limits'),
                    'action_text' => 'Manage Limits',
                    'priority' => 'high',
                ];
            } elseif ($usersAtLimit > 0) {
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'info',
                    'title' => 'Users At Limit',
                    'message' => "{$usersAtLimit} users are at the Mahl-e-Nazar limit.",
                    'action_url' => route('mahl-e-nazar-limits'),
                    'action_text' => 'View Status',
                    'priority' => 'medium',
                ];
            }
        }
    }

    private function checkUnlockRequests($user)
    {
        try {
            $requests = UnlockRequest::where('status', 'pending')->with('user')->get();
            foreach ($requests as $request) {
                // Ensure we have valid data before creating notification
                if (!$request->id || !$request->user) {
                    \Log::warning('Invalid unlock request data', ['request_id' => $request->id ?? 'null']);
                    continue;
                }

                $this->notifications[] = [
                    'type' => 'info',
                    'icon' => 'lock_open',
                    'title' => 'Unlock Request',
                    'message' => 'User: ' . ($request->user ? $request->user->name : 'Unknown') . '<br>Message: ' . ($request->message ?: 'No message'),
                    'unlock_request_id' => $request->id,
                    'action_url' => null,
                    'action_text' => null,
                    'priority' => 'high',
                    'is_unlock_request' => true,
                ];
            }
            \Log::info('Unlock requests found:', [$requests->count()]);
        } catch (\Exception $e) {
            \Log::error('Error loading unlock requests: ' . $e->getMessage());
            // Don't break the notification system if unlock requests fail
        }
    }

    public function dismissNotification($index)
    {
        if (isset($this->notifications[$index])) {
            $notification = $this->notifications[$index];
            
            // If it's a fatwa assignment notification, mark it as viewed
            if (isset($notification['is_fatwa_assignment']) && $notification['is_fatwa_assignment'] && isset($notification['fatwa_id'])) {
                $this->markFatwaNotificationAsViewed($notification['fatwa_id']);
            }
            
            unset($this->notifications[$index]);
            $this->notifications = array_values($this->notifications); // Re-index array
            $this->showNotifications = count($this->notifications) > 0;
            $this->sortedNotifications = $this->getNotificationsByPriority();
        }
    }

    private function markFatwaNotificationAsViewed($fatwaId)
    {
        try {
            // Since we can't add a new column, we'll use a different approach
            // We'll store viewed notifications in session or use updated_at as a marker
            $viewedNotifications = session()->get('viewed_fatwa_notifications', []);
            $viewedNotifications[] = $fatwaId;
            
            // Clean up old notifications (keep only last 100 to prevent session bloat)
            $viewedNotifications = array_slice(array_unique($viewedNotifications), -100);
            
            session()->put('viewed_fatwa_notifications', $viewedNotifications);
        } catch (\Exception $e) {
            \Log::error('Error marking fatwa notification as viewed: ' . $e->getMessage());
        }
    }

    public function dismissAllNotifications()
    {
        // Mark all fatwa notifications as viewed before dismissing
        foreach ($this->notifications as $notification) {
            if (isset($notification['is_fatwa_assignment']) && $notification['is_fatwa_assignment'] && isset($notification['fatwa_id'])) {
                $this->markFatwaNotificationAsViewed($notification['fatwa_id']);
            }
        }
        
        $this->notifications = [];
        $this->sortedNotifications = [];
        $this->showNotifications = false;
    }

    public function refreshNotifications()
    {
        $this->loadNotifications();
    }

    public function getNotificationsByPriority()
    {
        if (empty($this->notifications)) {
            return [];
        }

        $priorityOrder = ['critical' => 0, 'high' => 1, 'medium' => 2, 'low' => 3];

        $sortedNotifications = $this->notifications;
        usort($sortedNotifications, function ($a, $b) use ($priorityOrder) {
            $aPriority = $priorityOrder[$a['priority']] ?? 999;
            $bPriority = $priorityOrder[$b['priority']] ?? 999;
            return $aPriority <=> $bPriority;
        });

        return $sortedNotifications;
    }

    public function showUnlockModal($unlockRequestId)
    {
        try {
            \Log::info('showUnlockModal called with ID: ' . $unlockRequestId);
            
            if (!$unlockRequestId) {
                \Log::error('Invalid unlock request ID provided');
                session()->flash('error', 'Invalid unlock request ID.');
                return;
            }

            $this->selectedUnlockRequest = UnlockRequest::with('user')->find($unlockRequestId);
            
            if (!$this->selectedUnlockRequest) {
                \Log::error('Unlock request not found for ID: ' . $unlockRequestId);
                session()->flash('error', 'Unlock request not found.');
                return;
            }

            \Log::info('Unlock request found: ' . $this->selectedUnlockRequest->id);
            
            $this->unlockReason = '';
            $this->isUnlockModalOpen = true;
            $this->resetValidation();
            
            \Log::info('Modal should be shown now');
        } catch (\Exception $e) {
            \Log::error('Error opening unlock modal: ' . $e->getMessage(), [
                'unlock_request_id' => $unlockRequestId,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);
            session()->flash('error', 'Error opening unlock modal. Please try again.');
        }
    }

    public function closeUnlockModal()
    {
        $this->isUnlockModalOpen = false;
        $this->selectedUnlockRequest = null;
        $this->unlockReason = '';
        $this->resetValidation();
    }

    public function dismissUnlockRequest($unlockRequestId)
    {
        try {
            if (!$unlockRequestId) {
                session()->flash('error', 'Invalid unlock request ID.');
                return;
            }

            $request = UnlockRequest::find($unlockRequestId);
            if (!$request) {
                session()->flash('error', 'Unlock request not found.');
                return;
            }

            $request->status = 'dismissed';
            $request->dismissed_by = auth()->id();
            $request->save();
            
            $this->refreshNotifications();
            session()->flash('success', 'Unlock request dismissed successfully.');
        } catch (\Exception $e) {
            \Log::error('Error dismissing unlock request: ' . $e->getMessage(), [
                'unlock_request_id' => $unlockRequestId,
                'user_id' => auth()->id()
            ]);
            session()->flash('error', 'Error dismissing unlock request. Please try again.');
        }
    }

    public function unlockAccount()
    {
        try {
            $this->validate([
                'unlockReason' => 'required|string|min:10|max:500',
            ]);
            
            if (!$this->selectedUnlockRequest) {
                session()->flash('error', 'Unlock request not found.');
                return;
            }
            
            $request = UnlockRequest::find($this->selectedUnlockRequest->id);
            if (!$request) {
                session()->flash('error', 'Unlock request not found.');
                return;
            }
            
            // Update unlock request status
            $request->status = 'unlocked';
            $request->unlocked_by = auth()->id();
            $request->unlock_reason = $this->unlockReason;
            $request->save();
            
            // Unlock the user account (deactivate restrictions)
            UserRestriction::where('user_id', $request->user_id)
                ->where('is_active', true)
                ->update([
                    'is_active' => false,
                    'lifted_by' => auth()->id(),
                    'lifted_at' => now(),
                    'lift_reason' => $this->unlockReason,
                    'was_manually_unlocked' => true,
                ]);
            
            // Log the unlock action
            \Log::info('User account unlocked via notification', [
                'unlocked_user_id' => $request->user_id,
                'unlocked_by' => auth()->id(),
                'unlock_request_id' => $request->id,
                'reason' => $this->unlockReason
            ]);
            
            $this->closeUnlockModal();
            $this->refreshNotifications();
            session()->flash('success', 'Account unlocked successfully.');
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Let validation errors bubble up naturally
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Error unlocking account via notification: ' . $e->getMessage(), [
                'unlock_request_id' => $this->selectedUnlockRequest->id ?? null,
                'user_id' => auth()->id()
            ]);
            session()->flash('error', 'Error unlocking account. Please try again.');
        }
    }
}
