@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
        <div class="page-header min-height-300 border-radius-xl mt-4" 
             style="background-image: url('{{ asset('assets/img/curved-images/curved0.jpg') }}'); background-position-y: 50%;">
            <span class="mask bg-gradient-primary opacity-6"></span>
        </div>
        
        <div class="card card-body blur shadow-blur mx-4 mt-n6 overflow-hidden">
            <div class="row gx-4">
                <div class="col-auto">
                    <div class="avatar avatar-xl position-relative">
                        <div class="avatar avatar-xl position-relative">
                            <div class="w-100 h-100 rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-users text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-auto my-auto">
                    <div class="h-100">
                        <h5 class="mb-1">Supervisor-Assistant Mapping</h5>
                        <p class="mb-0 font-weight-bold text-sm">Manage Superior-Muawin relationships and team assignments</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supervisor-Assistant Mapping Component -->
    <livewire:supervisor-assistant-mapping />
</div>

<style>
    .main-content {
        margin-left: 250px;
    }

    @media (max-width: 991.98px) {
        .main-content {
            margin-left: 0;
        }
    }

    /* Modern styling for supervisor-assistant mapping */
    .card {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: none;
        border-radius: 0.75rem;
    }

    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .badge {
        font-weight: 500;
        border-radius: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        border: 1px solid #d1d5db;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .alert {
        border-radius: 0.75rem;
        border: none;
    }

    /* Avatar styling */
    .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-weight: 600;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }

    .avatar-lg {
        width: 48px;
        height: 48px;
        font-size: 1rem;
    }

    .avatar-xl {
        width: 64px;
        height: 64px;
        font-size: 1.25rem;
    }

    /* Statistics cards styling */
    .icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem;
    }

    .icon i {
        font-size: 1.25rem;
    }

    .numbers h5 {
        font-size: 1.75rem;
        font-weight: 700;
        line-height: 1.2;
    }

    .numbers p {
        font-size: 0.875rem;
        margin-bottom: 0;
        color: #6c757d;
    }

    /* Team card styling */
    .assistant-list {
        min-height: 200px;
        max-height: 350px;
        overflow-y: auto;
        padding: 0.5rem 0;
    }

    .assistant-list::-webkit-scrollbar {
        width: 6px;
    }

    .assistant-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .assistant-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .assistant-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Fix dropdown positioning issues */
    .dropdown-menu {
        z-index: 1050;
        min-width: 180px;
    }

    .card {
        overflow: visible;
    }

    /* Improve assistant item spacing */
    .assistant-list .border-bottom {
        border-color: #e9ecef !important;
        border-width: 1px !important;
    }

    .assistant-list .border-bottom:last-child {
        border-bottom: none !important;
    }

    /* Supervisor card improvements */
    .supervisor-card {
        display: flex;
        flex-direction: column;
    }

    .supervisor-card .card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .supervisor-card .assistant-list {
        flex: 1;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .supervisor-card {
            min-height: 350px !important;
        }
        
        .assistant-list {
            min-height: 150px !important;
            max-height: 250px !important;
        }
    }

    @media (min-width: 1200px) {
        .supervisor-card {
            min-height: 500px !important;
        }
        
        .assistant-list {
            min-height: 250px !important;
            max-height: 400px !important;
        }
    }

    /* Status colors */
    .bg-gradient-success {
        background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(87deg, #8392ab 0, #96a2b2 100%);
    }

    /* Modal styling */
    .modal-content {
        border-radius: 1rem;
        border: none;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .modal-header {
        border-bottom: 1px solid #f1f3f4;
        border-radius: 1rem 1rem 0 0;
    }

    .modal-footer {
        border-top: 1px solid #f1f3f4;
        border-radius: 0 0 1rem 1rem;
    }

    .info-item {
        padding: 0.5rem 0;
    }

    .info-item small {
        display: block;
        margin-bottom: 0.25rem;
    }

    /* Form check styling */
    .form-check {
        padding: 0.75rem;
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .form-check:hover {
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }

    .form-check-input:checked ~ .form-check-label {
        color: #495057;
    }

    .form-check-input:checked ~ .form-check-label .avatar {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%) !important;
    }

    /* Dropdown styling */
    .dropdown-menu {
        border-radius: 0.5rem;
        border: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* Department badge close button styling */
    .badge .btn-close {
        padding: 0;
        margin: 0;
        width: 12px;
        height: 12px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .badge:hover .btn-close {
        opacity: 1;
    }

    .badge .btn-close:hover {
        opacity: 1;
        transform: scale(1.1);
    }

    /* Edit Departments Modal Scroll Styling */
    .current-departments-scroll,
    .available-departments-scroll {
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;
    }

    .current-departments-scroll::-webkit-scrollbar,
    .available-departments-scroll::-webkit-scrollbar {
        width: 8px;
    }

    .current-departments-scroll::-webkit-scrollbar-track,
    .available-departments-scroll::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .current-departments-scroll::-webkit-scrollbar-thumb,
    .available-departments-scroll::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .current-departments-scroll::-webkit-scrollbar-thumb:hover,
    .available-departments-scroll::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Modal body scroll styling */
    .modal-body::-webkit-scrollbar {
        width: 10px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 5px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 5px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Sticky headers in modal */
    .sticky-top {
        z-index: 1020;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 0.5rem !important;
    }

    /* Assistants selection scroll styling */
    .assistants-selection-scroll {
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;
    }

    .assistants-selection-scroll::-webkit-scrollbar {
        width: 8px;
    }

    .assistants-selection-scroll::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .assistants-selection-scroll::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .assistants-selection-scroll::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Create Superior assistants scroll styling */
    .create-superior-assistants-scroll {
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;
    }

    .create-superior-assistants-scroll::-webkit-scrollbar {
        width: 8px;
    }

    .create-superior-assistants-scroll::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .create-superior-assistants-scroll::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .create-superior-assistants-scroll::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    .dropdown-item {
        border-radius: 0.25rem;
        margin: 0.125rem 0.5rem;
        padding: 0.5rem 0.75rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    /* Empty state styling */
    .empty-state {
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }
</style>
@endsection
