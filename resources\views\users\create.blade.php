<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="user-management"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Create User"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #7c3aed;
                --primary-hover: #6d28d9;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 800px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white !important;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
            }

            .page-header * {
                color: white !important;
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                color: white !important;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .page-title svg {
                color: white !important;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            }

            .page-subtitle {
                font-size: 1.1rem;
                color: white !important;
                opacity: 0.95;
                margin-top: 0.5rem;
                margin-bottom: 0;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            }

            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1rem;
                font-size: 0.875rem;
            }

            .breadcrumb a {
                color: white !important;
                text-decoration: none;
                opacity: 1;
                transition: all 0.2s;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.1);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .breadcrumb a:hover {
                opacity: 1;
                text-decoration: none;
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
            }

            .breadcrumb span {
                color: white !important;
                opacity: 1;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.25);
            }

            .breadcrumb svg {
                color: white !important;
                opacity: 0.8;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            }

            .form-card {
                background: white;
                border-radius: 1rem;
                padding: 2.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
            }

            .form-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-icon {
                width: 24px;
                height: 24px;
                color: var(--primary-color);
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                font-weight: 600;
                color: var(--gray-700);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .form-input, .form-select {
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.75rem;
                font-size: 1rem;
                transition: all 0.2s ease;
                background: var(--gray-50);
            }

            .form-input:focus, .form-select:focus {
                outline: none;
                border-color: var(--primary-color);
                background: white;
                box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
            }

            .form-select {
                cursor: pointer;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 0.5rem center;
                background-repeat: no-repeat;
                background-size: 1.5em 1.5em;
                padding-right: 2.5rem;
            }

            .role-checkboxes {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                padding: 1rem;
                background: var(--gray-50);
                border-radius: 0.75rem;
                border: 2px solid var(--gray-200);
            }

            .role-checkbox {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 0.75rem;
                background: white;
                border-radius: 0.5rem;
                border: 1px solid var(--gray-200);
                transition: all 0.2s ease;
                cursor: pointer;
            }

            .role-checkbox:hover {
                border-color: var(--primary-color);
                box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
            }

            .role-checkbox input[type="checkbox"] {
                width: 1.25rem;
                height: 1.25rem;
                accent-color: var(--primary-color);
            }

            .role-checkbox label {
                font-weight: 500;
                color: var(--gray-700);
                cursor: pointer;
                margin: 0;
                text-transform: none;
                letter-spacing: normal;
            }

            .btn-primary {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.3);
            }

            .back-link {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                color: white !important;
                text-decoration: none;
                font-weight: 600;
                margin-bottom: 2rem;
                padding: 0.875rem 1.75rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                border-radius: 0.75rem;
                box-shadow: 0 4px 12px rgba(124, 58, 237, 0.25);
                transition: all 0.2s ease;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }

            .back-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
                color: white !important;
                text-decoration: none;
                background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .back-link svg {
                color: white !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
            }

            .error-message {
                color: var(--danger-color);
                font-size: 0.875rem;
                margin-top: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .alert {
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                margin-bottom: 1.5rem;
                border: 1px solid;
            }

            .alert-success {
                background: #ecfdf5;
                border-color: #10b981;
                color: #065f46;
            }

            .alert-error {
                background: #fef2f2;
                border-color: #ef4444;
                color: #991b1b;
            }

            @media (max-width: 768px) {
                .modern-container {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }
                
                .form-card {
                    padding: 1.5rem;
                }

                .role-checkboxes {
                    grid-template-columns: 1fr;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Back Link -->
            <a href="{{ route('users.index') }}" class="back-link">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Users List
            </a>

            <!-- Page Header -->
            <div class="page-header">
                <div class="breadcrumb">
                    <a href="{{ route('users.index') }}">User Management</a>
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                    <span>Create New User</span>
                </div>
                <h1 class="page-title">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                    </svg>
                    Create New User
                </h1>
                <p class="page-subtitle">Add a new user to the system with appropriate roles and permissions</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success">
                    <strong>Success!</strong> {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    <strong>Error!</strong> {{ session('error') }}
                </div>
            @endif

            <!-- Create Form -->
            <div class="form-card">
                <h2 class="form-title">
                    <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    User Information
                </h2>
                
                <form action="{{ route('users.store') }}" method="POST">
                    @csrf
                    
                    <div class="form-group">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" 
                               class="form-input" 
                               id="name" 
                               name="name" 
                               value="{{ old('name') }}"
                               placeholder="Enter user's full name"
                               required>
                        @error('name')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" 
                               class="form-input" 
                               id="email" 
                               name="email" 
                               value="{{ old('email') }}"
                               placeholder="Enter user's email address"
                               required>
                        @error('email')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" 
                               class="form-input" 
                               id="password" 
                               name="password" 
                               placeholder="Enter a secure password"
                               required 
                               pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W]).{8,}" 
                               title="Password must contain: At least one number, one uppercase and lowercase letter, one special character, and at least 8 or more characters">
                        @error('password')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">User Roles</label>
                        <div class="role-checkboxes">
                            @foreach($roles as $role)
                                <div class="role-checkbox">
                                    <input type="checkbox" 
                                           id="role_{{ $role->id }}" 
                                           name="roles[]" 
                                           value="{{ $role->id }}"
                                           {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}>
                                    <label for="role_{{ $role->id }}">{{ $role->name }}</label>
                                </div>
                            @endforeach
                        </div>
                        @error('roles')
                            <div class="error-message">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Create User
                    </button>
                </form>
            </div>
        </div>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
