<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UnlockHistoryController;

// Unlock History Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/unlock-history', [UnlockHistoryController::class, 'index'])->name('unlock-history.index');
    Route::get('/unlock-history/stats', [UnlockHistoryController::class, 'systemStats'])->name('unlock-history.stats');
    Route::get('/unlock-history/user/{userId}', [UnlockHistoryController::class, 'userHistory'])->name('unlock-history.user');
});