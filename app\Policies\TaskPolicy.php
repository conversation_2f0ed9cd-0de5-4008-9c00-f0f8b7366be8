<?php

namespace App\Policies;

use App\Models\Task;
use App\Models\User;

class TaskPolicy
{
    /**
     * Determine whether the user can view any tasks.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view tasks
    }

    /**
     * Determine whether the user can view the task.
     */
    public function view(User $user, Task $task): bool
    {
        return $user->isNazim() || 
               $user->isSuperior() || 
               $task->assigned_to === $user->id ||
               $task->assigned_by === $user->id;
    }

    /**
     * Determine whether the user can create tasks.
     */
    public function create(User $user): bool
    {
        return $user->isNazim() || $user->isSuperior();
    }

    /**
     * Determine whether the user can update the task.
     */
    public function update(User $user, Task $task): bool
    {
        return $user->isNazim() ||
               $task->assigned_by === $user->id ||
               ($user->isSuperior() && $this->isInSameDepartment($user, $task)) ||
               $task->assigned_to === $user->id; // Allow assigned user to update their own task
    }

    /**
     * Determine whether the user can delete the task.
     */
    public function delete(User $user, Task $task): bool
    {
        return $user->isNazim() || $task->assigned_by === $user->id;
    }

    /**
     * Determine whether the user can complete the task.
     */
    public function complete(User $user, Task $task): bool
    {
        return $task->assigned_to === $user->id;
    }

    /**
     * Determine whether the user can change task status to a specific status.
     */
    public function changeStatus(User $user, Task $task, string $newStatus): bool
    {
        // Nazim can change any status
        if ($user->isNazim()) {
            return true;
        }

        // Task creator can change any status
        if ($task->assigned_by === $user->id) {
            return true;
        }

        // For operational tasks, assigned user has full control
        if ($task->type === 'operational' && $task->assigned_to === $user->id) {
            return true;
        }

        // For department tasks with superior/assistant roles
        if ($task->assigned_to === $user->id) {
            // If user is assistant (role_type = 'assistant'), they can only start tasks
            if ($task->role_type === 'assistant') {
                return $newStatus === 'in_progress' && $task->status === 'pending';
            }

            // If user is superior or direct assignee, they can change to any status
            if ($task->role_type === 'superior' || !$task->role_type) {
                return true;
            }
        }

        // Superior can change status of tasks in their department
        if ($user->isSuperior() && $this->isInSameDepartment($user, $task)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can start the task (pending to in_progress).
     */
    public function start(User $user, Task $task): bool
    {
        return $this->changeStatus($user, $task, 'in_progress');
    }

    /**
     * Determine whether the user can mark task as completed.
     */
    public function markCompleted(User $user, Task $task): bool
    {
        return $this->changeStatus($user, $task, 'completed');
    }

    /**
     * Determine whether the user can assign tasks to others.
     */
    public function assign(User $user): bool
    {
        return $user->isNazim() || $user->isSuperior();
    }

    /**
     * Check if user and task are in the same department.
     */
    private function isInSameDepartment(User $user, Task $task): bool
    {
        if (!$task->department_id) {
            return false;
        }

        return $user->departments->contains('id', $task->department_id);
    }
}
