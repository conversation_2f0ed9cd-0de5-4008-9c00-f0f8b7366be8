<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class MahlenazarFatawa extends Component
{
    use WithPagination;

    public $filter;
    public $search;
    public $daruliftaNames;
    public $darulifta;
    public $daruliftalist;
    public $mujeebs = [];
    public $selectedmujeeb = 'all';
    public $selectedchecked = 'all';
    public $selectedTimeFrame = 'all';
    public $startDate;
    public $endDate;
    public $mailfolderDates;
    public $mahl_e_nazarfataw = [];
    public $mahl_e_mujeeb = [];
    public $totalCounts = 0;
    public $fileErrorMessage;
    public $expandedQuestions = [];
    public $expandedChats = [];
    public $roleInfo = [];

    protected $queryString = [
        'filter' => ['except' => ''],
        'search' => ['except' => ''],
        'selectedmujeeb' => ['except' => 'all'],
        'selectedchecked' => ['except' => 'all'],
        'selectedTimeFrame' => ['except' => 'all'],
        'startDate' => ['except' => ''],
        'endDate' => ['except' => ''],
    ];

    public function mount($darulifta = null)
    {
        $this->darulifta = $darulifta;
        $this->determineUserRole();
        $this->loadDaruliftaNames();
        $this->loadMujeebs();

        // Load filter values from the query string
        $this->selectedmujeeb = request()->query('selectedmujeeb', $this->selectedmujeeb);
        $this->selectedTimeFrame = request()->query('selectedTimeFrame', $this->selectedTimeFrame);
        $this->startDate = request()->query('startDate', $this->startDate);
        $this->endDate = request()->query('endDate', $this->endDate);

        // Pre-expand all chat sections when the component is mounted
        $this->expandAllChats();
    }

    private function determineUserRole()
    {
        $user = Auth::user();
        $userName = $user->name;
        $userRoles = $user->roles->pluck('name')->toArray();

        // Set default values
        $this->roleInfo = [
            'isSuperAdmin' => false,
            'isChecker' => false,
            'isMujeeb' => false,
            'userName' => $userName,
            'checkerName' => null,
            'mujeebName' => null,
            'darulNames' => [], // Changed to array to support multiple departments
            'assignedMujeebs' => [],
        ];

        // Check for SuperAdmin role (highest priority)
        if (in_array('SuperAdmin', $userRoles)) {
            $this->roleInfo['isSuperAdmin'] = true;
            return;
        }

        // Check for Checker role (second priority)
        if (in_array('Checker', $userRoles)) {
            $checker = DB::table('checker')
                ->where('checker_name', $userName)
                ->first();

            if ($checker) {
                $this->roleInfo['isChecker'] = true;
                $this->roleInfo['checkerName'] = $userName;
                $this->roleInfo['checkerFolderId'] = $checker->folder_id ?? null;
                return;
            }
        }

        // Check for Mujeeb role (lowest priority)
        if (in_array('mujeeb', $userRoles)) {
            $mujeebs = DB::table('mujeebs')
                ->where('mujeeb_name', $userName)
                ->get();

            if ($mujeebs->isNotEmpty()) {
                $this->roleInfo['isMujeeb'] = true;
                $this->roleInfo['mujeebName'] = $userName;
                // Get all darul_names for this mujeeb
                $this->roleInfo['darulNames'] = $mujeebs->pluck('darul_name')->unique()->toArray();
                return;
            }
        }

        // Check if user is in mujeebs table but not assigned the mujeeb role
        $mujeebs = DB::table('mujeebs')
            ->where('mujeeb_name', $userName)
            ->get();

        if ($mujeebs->isNotEmpty()) {
            $this->roleInfo['isMujeeb'] = true;
            $this->roleInfo['mujeebName'] = $userName;
            // Get all darul_names for this mujeeb
            $this->roleInfo['darulNames'] = $mujeebs->pluck('darul_name')->unique()->toArray();
        }
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);

        if ($this->roleInfo['isMujeeb']) {
            // If user is mujeeb, show all their associated darul's data
            $this->daruliftaNames = collect($this->roleInfo['darulNames']);
        } else if ($this->darulifta === null) {
            if (count($userRoles) > 1) {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('uploaded_files.darulifta_name')
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->pluck('uploaded_files.darulifta_name');
            } else {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', $firstRoleName)
                    ->pluck('darulifta_name');
            }
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('darulifta_name');
        }

        $this->daruliftalist = DB::table('daruliftas')
            ->join('uploaded_files', 'daruliftas.darul_name', '=', 'uploaded_files.darulifta_name')
            ->select('daruliftas.darul_name')
            ->distinct()
            ->orderBy('daruliftas.id')
            ->pluck('daruliftas.darul_name');
    }

    private function loadMailFolderDates()
    {
        $this->mailfolderDates = DB::table('uploaded_files')
            ->select('mail_folder_date')
            ->where('selected', 0)
            ->distinct()
            ->orderBy('mail_folder_date', 'desc')
            ->pluck('mail_folder_date');
    }

    private function loadMujeebs()
    {
        // Load mujeebs from both regular and Talaq fatawa
        $regularMujeebs = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->distinct();

        $talaqMujeebs = DB::table('talaq_fatawa_manage')
            ->where('checked_folder', 'mahl-e-nazar')
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->distinct();

        // If user is mujeeb, only show their own data
        if ($this->roleInfo['isMujeeb']) {
            $regularMujeebs->where('sender', $this->roleInfo['userName']);
            $talaqMujeebs->where('sender', $this->roleInfo['userName']);
        }

        $regularSenders = $regularMujeebs->pluck('sender')->toArray();
        $talaqSenders = $talaqMujeebs->pluck('sender')->toArray();

        $this->mujeebs = array_unique(array_merge($regularSenders, $talaqSenders));
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatingFilter()
    {
        $this->resetPage();
    }
    
    public function downloadFile($fileName)
    {
        // Define the path to the file in the storage directory
        $filePath = 'public/fatawa/' . $fileName;

        // Check if the file exists
        if (Storage::exists($filePath)) {
            // Download the file from storage
            return Storage::download($filePath, $fileName);
        } else {
            // Handle the error if the file does not exist
            $this->fileErrorMessage = "The file does not exist.";
        }
    }
    
    public function toggleViral($id)
    {
        $file = DB::table('uploaded_files')->where('id', $id)->first();

        if ($file) {
            $currentUserId = Auth::id();
            $newViralValue = $file->viral == $currentUserId ? 0 : $currentUserId;

            DB::table('uploaded_files')
                ->where('id', $id)
                ->update(['viral' => $newViralValue]);
        }
    }

    public function filterByMujeeb($mujeeb)
    {
        $this->selectedmujeeb = $mujeeb;
        $this->resetPage();
    }

    public function toggleQuestion($fatwaId)
    {
        if (in_array($fatwaId, $this->expandedQuestions)) {
            $this->expandedQuestions = array_diff($this->expandedQuestions, [$fatwaId]);
        } else {
            $this->expandedQuestions[] = $fatwaId;
        }
    }

    public function toggleChat($fatwaId)
    {
        if (in_array($fatwaId, $this->expandedChats)) {
            $this->expandedChats = array_diff($this->expandedChats, [$fatwaId]);
        } else {
            $this->expandedChats[] = $fatwaId;
        }
    }

    public function expandAllChats()
    {
        // Clear existing expanded sections
        $this->expandedChats = [];
        
        // Add all fatawa IDs to expandedChats
        $query = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select('id')
            ->get();
            
        foreach ($query as $item) {
            $this->expandedChats[] = $item->id;
        }
    }

    public function render()
    {
        // First, get a list of all file codes that are currently assigned
        $assignedFileCodes = DB::table('uploaded_files')
            ->where('checked_folder', 'Assigned')
            ->select('file_code')
            ->distinct()
            ->pluck('file_code')
            ->toArray();

        // For each assigned file code, get the latest sender
        $currentAssignees = [];
        foreach ($assignedFileCodes as $fileCode) {
            $latestAssignment = DB::table('uploaded_files')
                ->where('file_code', $fileCode)
                ->where('checked_folder', 'Assigned')
                ->orderBy('id', 'desc')
                ->first();

            if ($latestAssignment) {
                $currentAssignees[$fileCode] = $latestAssignment->sender;
            }
        }

        // Build the base query for regular Mahl-e-Nazar fatawa
        $query = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->where('u1.selected', 1)
            ->whereIn('u1.darulifta_name', $this->daruliftaNames);

        // If user is mujeeb, only show their own data
        if ($this->roleInfo['isMujeeb']) {
            $query->where('u1.sender', $this->roleInfo['userName']);
        } else {
            $query->when($this->selectedmujeeb != 'all', function($query) {
                return $query->where('u1.sender', $this->selectedmujeeb);
            });
        }

        // Get latest Mahl-e-Nazar entry for each file code FIRST (same as service logic)
        $latestIds = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        $query->whereIn('u1.id', $latestIds) // Apply latest IDs filter first
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.*', 'users.name as user_name', DB::raw("'regular' as fatwa_type"))
            ->leftJoin('users', 'u1.viral', '=', 'users.id');

        // Get latest Talaq entries for each file code FIRST (same as service logic)
        $latestTalaqIds = DB::table('talaq_fatawa_manage')
            ->where('checked_folder', 'mahl-e-nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        // Build query for Talaq Mahl-e-Nazar fatawa
        $talaqQuery = DB::table('talaq_fatawa_manage as tfm')
            ->join('talaq_fatawa as tf', 'tfm.talaq_checked_id', '=', 'tf.id')
            ->where('tfm.checked_folder', 'mahl-e-nazar')
            ->where('tfm.selected', 1)
            ->whereIn('tfm.id', $latestTalaqIds) // Apply latest IDs filter first
            ->whereIn('tfm.darulifta_name', $this->daruliftaNames);

        // Filter out Talaq fatawa that have been marked as "ok" (same logic as regular fatawa)
        $talaqQuery->leftJoin('talaq_fatawa_manage as tfm2', function ($join) {
            $join->on('tfm.file_code', '=', 'tfm2.file_code')
                ->where('tfm2.checked_folder', 'ok');
        })
        ->whereNull('tfm2.file_code');

        // Apply same user filtering for Talaq fatawa
        if ($this->roleInfo['isMujeeb']) {
            $talaqQuery->where('tfm.sender', $this->roleInfo['userName']);
        } else {
            $talaqQuery->when($this->selectedmujeeb != 'all', function($query) {
                return $query->where('tfm.sender', $this->selectedmujeeb);
            });
        }

        $talaqQuery->select(
                'tfm.id',
                'tfm.file_code',
                'tfm.sender',
                'tfm.darulifta_name',
                'tfm.mail_folder_date',
                'tfm.category',
                'tfm.checked_date',
                'tfm.checked_folder',
                'tfm.file_name',
                'tfm.checker',
                'tfm.by_mufti',
                'tfm.viral',
                'tfm.selected',
                'tfm.talaq_checked_id',
                'tf.content',
                'tf.ifta_code',
                DB::raw("null as user_name"),
                DB::raw("'talaq' as fatwa_type")
            );

        // Now apply the critical filtering logic for assigned fatwas
        // First, find all the records we need to exclude
        $excludeFileCodesForSender = [];
        foreach ($currentAssignees as $fileCode => $assignedSender) {
            // If current filter is not 'all', and the mujeeb is not the currently assigned one
            if ($this->selectedmujeeb != 'all' && $this->selectedmujeeb != $assignedSender) {
                // We should exclude this file code for this sender
                $excludeFileCodesForSender[] = $fileCode;
            }
        }

        // Apply exclusions
        foreach ($excludeFileCodesForSender as $fileCode) {
            $query->where('u1.file_code', '!=', $fileCode);
        }

        // For all other cases, if a file code is assigned, we need to check the sender
        if ($this->selectedmujeeb == 'all') {
            // If showing all mujeebs, we need to exclude file codes that are assigned to anyone
            // except the ones currently assigned to them
            foreach ($currentAssignees as $fileCode => $assignedSender) {
                $query->where(function($q) use ($fileCode, $assignedSender) {
                    $q->where('u1.file_code', '!=', $fileCode)
                      ->orWhere('u1.sender', $assignedSender);
                });
            }
        }

        // Apply search conditions to regular fatawa
        if ($this->search) {
            $query->where(function ($subQuery) {
                $subQuery->where('u1.sender', 'like', '%' . $this->search . '%')
                    ->orWhere('u1.file_code', 'like', '%' . $this->search . '%')
                    ->orWhere('u1.category', 'like', '%' . $this->search . '%')
                    ->orWhere('u1.darulifta_name', 'like', '%' . $this->search . '%');
            });
        }

        // Apply time frame filter to regular fatawa
        if ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(u1.mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'last_month') {
            $query->whereBetween(DB::raw('DATE(u1.mail_folder_date)'), [
                now()->subMonth()->startOfMonth(),
                now()->subMonth()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween(DB::raw('DATE(u1.mail_folder_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }

        // Apply darulifta filter to regular fatawa
        if ($this->darulifta && $this->darulifta != 'all') {
            $query->where('u1.darulifta_name', $this->darulifta);
        }

        // Apply search conditions to Talaq fatawa
        if ($this->search) {
            $talaqQuery->where(function ($subQuery) {
                $subQuery->where('tfm.sender', 'like', '%' . $this->search . '%')
                    ->orWhere('tfm.file_code', 'like', '%' . $this->search . '%')
                    ->orWhere('tfm.category', 'like', '%' . $this->search . '%')
                    ->orWhere('tfm.darulifta_name', 'like', '%' . $this->search . '%');
            });
        }

        // Apply time frame filter to Talaq fatawa
        if ($this->selectedTimeFrame == 'this_month') {
            $talaqQuery->whereBetween(DB::raw('DATE(tfm.mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'last_month') {
            $talaqQuery->whereBetween(DB::raw('DATE(tfm.mail_folder_date)'), [
                now()->subMonth()->startOfMonth(),
                now()->subMonth()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $talaqQuery->whereBetween(DB::raw('DATE(tfm.mail_folder_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }

        // Apply darulifta filter to Talaq fatawa
        if ($this->darulifta && $this->darulifta != 'all') {
            $talaqQuery->where('tfm.darulifta_name', $this->darulifta);
        }

        // Get all records for summary (both regular and Talaq)
        $allRegularRecords = $query->get();
        $allTalaqRecords = $talaqQuery->get();
        $allRecords = $allRegularRecords->merge($allTalaqRecords);

        // Prepare summary data
        $summaryData = [];

        foreach ($allRecords as $record) {
            $daruliftaName = $record->darulifta_name;
            $sender = $record->sender;
            $fatwaType = $record->fatwa_type;

            if (!isset($summaryData[$daruliftaName])) {
                $summaryData[$daruliftaName] = [
                    'total' => 0,
                    'senders' => [],
                    'regular_total' => 0,
                    'talaq_total' => 0
                ];
            }

            if (!isset($summaryData[$daruliftaName]['senders'][$sender])) {
                $summaryData[$daruliftaName]['senders'][$sender] = [
                    'total' => 0,
                    'regular' => 0,
                    'talaq' => 0
                ];
            }

            $summaryData[$daruliftaName]['senders'][$sender]['total']++;
            $summaryData[$daruliftaName]['senders'][$sender][$fatwaType]++;
            $summaryData[$daruliftaName]['total']++;
            $summaryData[$daruliftaName][$fatwaType . '_total']++;
        }

        // Get total count after all conditions are applied
        $totalCounts = count($allRecords);

        // Sort combined records by ID descending and paginate manually
        $sortedRecords = $allRecords->sortByDesc('id');
        $perPage = 50;
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedItems = $sortedRecords->slice($offset, $perPage)->values();

        // Create a paginator instance
        $mahlenazar = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedItems,
            $totalCounts,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );

        // Get mahlenazar_null data for Mahl-e-Nazar Folder (regular fatawa)
        $mahlenazar_null = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select('file_code', 'mail_folder_date', 'sender')
            ->get();

        // Get Talaq Mahl-e-Nazar data
        $talaq_mahlenazar_null = DB::table('talaq_fatawa_manage')
            ->where('checked_folder', 'mahl-e-nazar')
            ->select('file_code', 'mail_folder_date', 'sender')
            ->get();

        // Combine both null data collections
        $combined_mahlenazar_null = $mahlenazar_null->merge($talaq_mahlenazar_null);

        // Get questions data for Q.Rec. Days
        $que_day_r = DB::table('questions')
            ->whereIn('question_branch', $this->daruliftaNames)
            ->select('ifta_code', 'rec_date', 'question')
            ->get();

        return view('livewire.mahlenazar-fatawa', [
            'mahlenazar' => $mahlenazar,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'summaryData' => $summaryData,
            'totalCounts' => $totalCounts,
            'mahlenazar_null' => $combined_mahlenazar_null,
            'que_day_r' => $que_day_r,
        ])->layout('layouts.app');
    }

    public function downloadTalaqFile($talaqCheckedId)
    {
        // Get the Talaq fatawa content
        $talaqFatawa = DB::table('talaq_fatawa')->where('id', $talaqCheckedId)->first();

        if (!$talaqFatawa) {
            $this->fileErrorMessage = "Talaq fatawa not found.";
            return;
        }

        // Use the existing download-word route
        return redirect()->route('download-word')->with([
            'content' => $talaqFatawa->content,
            'ifta_code' => $talaqFatawa->ifta_code
        ]);
    }
}
