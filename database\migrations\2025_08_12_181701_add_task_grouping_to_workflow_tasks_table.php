<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add the missing columns to the workflow_tasks table
        Schema::table('workflow_tasks', function (Blueprint $table) {
            // Add task_number column if it doesn't exist
            if (!Schema::hasColumn('workflow_tasks', 'task_number')) {
                $table->string('task_number')->nullable()->after('id');
            }

            // Add parent_task_id column if it doesn't exist
            if (!Schema::hasColumn('workflow_tasks', 'parent_task_id')) {
                $table->foreignId('parent_task_id')->nullable()->constrained('workflow_tasks')->onDelete('cascade')->after('department_id');
            }

            // Add role_type column if it doesn't exist
            if (!Schema::hasColumn('workflow_tasks', 'role_type')) {
                $table->enum('role_type', ['superior', 'assistant'])->nullable()->after('parent_task_id');
            }
        });

        // Generate task numbers for existing tasks that don't have them
        $tasks = \App\Models\Task::where('task_number', '')->orWhereNull('task_number')->get();
        foreach ($tasks as $task) {
            $date = $task->created_at->format('Ymd');
            $lastTask = \App\Models\Task::where('task_number', 'like', "TSK-{$date}-%")
                           ->orderBy('task_number', 'desc')
                           ->first();

            if ($lastTask) {
                $lastNumber = (int) substr($lastTask->task_number, -3);
                $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
            } else {
                $newNumber = '001';
            }

            $task->update(['task_number' => "TSK-{$date}-{$newNumber}"]);
        }

        // Add unique constraint to task_number if it doesn't exist
        try {
            Schema::table('workflow_tasks', function (Blueprint $table) {
                if (!Schema::hasIndex('workflow_tasks', 'workflow_tasks_task_number_unique')) {
                    $table->unique('task_number');
                }
            });
        } catch (\Exception $e) {
            // Unique constraint might already exist, ignore the error
        }

        // Add index for parent_task_id and role_type if it doesn't exist
        try {
            Schema::table('workflow_tasks', function (Blueprint $table) {
                $table->index(['parent_task_id', 'role_type']);
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore the error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workflow_tasks', function (Blueprint $table) {
            // Drop indexes and constraints first
            try {
                $table->dropIndex(['parent_task_id', 'role_type']);
            } catch (\Exception $e) {
                // Index might not exist
            }

            try {
                $table->dropUnique(['task_number']);
            } catch (\Exception $e) {
                // Unique constraint might not exist
            }

            try {
                $table->dropForeign(['parent_task_id']);
            } catch (\Exception $e) {
                // Foreign key might not exist
            }

            // Drop columns
            if (Schema::hasColumn('workflow_tasks', 'role_type')) {
                $table->dropColumn('role_type');
            }

            if (Schema::hasColumn('workflow_tasks', 'parent_task_id')) {
                $table->dropColumn('parent_task_id');
            }

            if (Schema::hasColumn('workflow_tasks', 'task_number')) {
                $table->dropColumn('task_number');
            }
        });
    }
};
