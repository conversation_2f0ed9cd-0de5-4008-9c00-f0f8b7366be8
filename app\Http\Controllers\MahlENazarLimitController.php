<?php

namespace App\Http\Controllers;

use App\Services\MahlENazarService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class MahlENazarLimitController extends Controller
{
    use AuthorizesRequests;

    protected $mahlENazarService;

    public function __construct(MahlENazarService $mahlENazarService)
    {
        $this->mahlENazarService = $mahlENazarService;
    }

    /**
     * Display the Mahl-e-Nazar limit management interface.
     */
    public function index()
    {
        $this->authorize('manage-users');
        
        return view('mahl-e-nazar-limit.index');
    }

    /**
     * Get Mahl-e-Nazar statistics.
     */
    public function statistics()
    {
        $this->authorize('manage-users');
        
        $stats = $this->mahlENazarService->getStatistics();
        
        return response()->json($stats);
    }

    /**
     * Update all user restrictions based on current counts.
     */
    public function updateRestrictions()
    {
        $this->authorize('manage-users');
        
        $result = $this->mahlENazarService->updateAllUserRestrictions();
        
        return response()->json([
            'message' => "Updated restrictions for {$result['total_checked']} users.",
            'result' => $result,
        ]);
    }

    /**
     * Toggle restriction for a specific user.
     */
    public function toggleUserRestriction(User $user)
    {
        $this->authorize('unlock-users');
        
        $hasRestriction = $user->activeRestrictions()
            ->where('restriction_type', 'fatawa_limit_exceeded')
            ->exists();

        if ($hasRestriction) {
            // Remove restriction
            $user->activeRestrictions()
                ->where('restriction_type', 'fatawa_limit_exceeded')
                ->update([
                    'is_active' => false,
                    'lifted_by' => auth()->id(),
                    'lifted_at' => now(),
                    'lift_reason' => 'Manually lifted by admin',
                ]);
            
            $message = "Restriction removed for {$user->name}.";
        } else {
            // Apply restriction
            $this->mahlENazarService->applyLimitRestriction($user);
            $message = "Restriction applied to {$user->name}.";
        }

        return response()->json(['message' => $message]);
    }

    /**
     * Check if current user can submit fatawa.
     */
    public function checkSubmissionEligibility()
    {
        $user = auth()->user();
        $canSubmit = $this->mahlENazarService->canSubmitFatawa($user);
        $message = $this->mahlENazarService->getRestrictionMessage($user);
        $count = $this->mahlENazarService->getMahlENazarCount($user->name);
        
        $limit = $this->mahlENazarService->getUserLimit($user);
        return response()->json([
            'can_submit' => $canSubmit,
            'message' => $message,
            'count' => $count,
            'limit' => $limit,
            'remaining' => max(0, $limit - $count),
        ]);
    }

    /**
     * Get dashboard widget data for Mahl-e-Nazar.
     */
    public function getDashboardWidget()
    {
        $user = auth()->user();
        
        if ($user->isNazim() || $user->isSuperior()) {
            // Admin/Superior view - show system statistics
            $stats = $this->mahlENazarService->getStatistics();
            return response()->json([
                'type' => 'admin',
                'data' => $stats,
            ]);
        } else {
            // User view - show personal count
            $count = $this->mahlENazarService->getMahlENazarCount($user->name);
            $canSubmit = $this->mahlENazarService->canSubmitFatawa($user);
            $message = $this->mahlENazarService->getRestrictionMessage($user);
            $limit = $this->mahlENazarService->getUserLimit($user);

            return response()->json([
                'type' => 'user',
                'data' => [
                    'count' => $count,
                    'limit' => $limit,
                    'remaining' => max(0, $limit - $count),
                    'can_submit' => $canSubmit,
                    'message' => $message,
                    'percentage' => min(100, ($count / $limit) * 100),
                ],
            ]);
        }
    }
}
