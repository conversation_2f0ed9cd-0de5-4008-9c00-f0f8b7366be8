<?php

namespace App\Http\Controllers;

use App\Models\Issue;
use App\Models\SubIssue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class IssueManagementController extends Controller
{
    /**
     * Display the issue management page
     */
    public function index()
    {
        $issues = Issue::with('subIssues')->orderBy('name')->get();
        return view('pages.issue-management', compact('issues'));
    }

    /**
     * Store a new issue (Muzo)
     */
    public function storeIssue(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:100|unique:issues,name',
        ]);

        Issue::create([
            'name' => $validatedData['name'],
            'parent_id' => 0,
            'type' => 1,
            'is_enable' => 1,
            'created_by' => Auth::id(),
        ]);

        return redirect()->route('issue-management')->with('success', '<PERSON><PERSON> added successfully.');
    }

    /**
     * Update an existing issue (Muzo)
     */
    public function updateIssue(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:100|unique:issues,name,' . $id,
        ]);

        $issue = Issue::findOrFail($id);
        $issue->update([
            'name' => $validatedData['name'],
            'updated_by' => Auth::id(),
        ]);

        return redirect()->route('issue-management')->with('success', 'Muzo updated successfully.');
    }

    /**
     * Delete an issue (Muzo) and its sub-issues
     */
    public function destroyIssue($id)
    {
        $issue = Issue::findOrFail($id);
        
        // Delete all sub-issues first
        SubIssue::where('parent_id', $id)->delete();
        
        // Delete the main issue
        $issue->delete();

        return redirect()->route('issue-management')->with('success', 'Muzo and all its Zeli-Muzo deleted successfully.');
    }

    /**
     * Store a new sub-issue (Zeli Muzo)
     */
    public function storeSubIssue(Request $request)
    {
        $validatedData = $request->validate([
            'parent_id' => 'required|exists:issues,id',
            'name' => 'required|string|max:100',
        ]);

        // Check if sub-issue name already exists for this parent
        $exists = SubIssue::where('parent_id', $validatedData['parent_id'])
                          ->where('name', $validatedData['name'])
                          ->exists();

        if ($exists) {
            return redirect()->route('issue-management')->with('error', 'This Zeli-Muzo already exists for the selected Muzo.');
        }

        SubIssue::create([
            'parent_id' => $validatedData['parent_id'],
            'name' => $validatedData['name'],
            'is_enable' => 1,
            'created_by' => Auth::id(),
        ]);

        return redirect()->route('issue-management')->with('success', 'Zeli-Muzo added successfully.');
    }

    /**
     * Update an existing sub-issue (Zeli Muzo)
     */
    public function updateSubIssue(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:100',
        ]);

        $subIssue = SubIssue::findOrFail($id);
        
        // Check if sub-issue name already exists for this parent (excluding current record)
        $exists = SubIssue::where('parent_id', $subIssue->parent_id)
                          ->where('name', $validatedData['name'])
                          ->where('id', '!=', $id)
                          ->exists();

        if ($exists) {
            return redirect()->route('issue-management')->with('error', 'This Zeli-Muzo already exists for the selected Muzo.');
        }

        $subIssue->update([
            'name' => $validatedData['name'],
            'updated_by' => Auth::id(),
        ]);

        return redirect()->route('issue-management')->with('success', 'Zeli-Muzo updated successfully.');
    }

    /**
     * Delete a sub-issue (Zeli Muzo)
     */
    public function destroySubIssue($id)
    {
        $subIssue = SubIssue::findOrFail($id);
        $subIssue->delete();

        return redirect()->route('issue-management')->with('success', 'Zeli-Muzo deleted successfully.');
    }

    /**
     * Get sub-issues for a specific issue (AJAX)
     */
    public function getSubIssues(Request $request)
    {
        $issueId = $request->issue_id;
        $subIssues = SubIssue::where('parent_id', $issueId)
                            ->where('is_enable', 1)
                            ->orderBy('name')
                            ->get();

        return response()->json($subIssues);
    }
}
