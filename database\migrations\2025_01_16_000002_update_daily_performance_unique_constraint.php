<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_performance', function (Blueprint $table) {
            // Drop the old unique constraint
            $table->dropUnique(['user_id', 'performance_date']);
            
            // Add new unique constraint that includes task_id
            $table->unique(['user_id', 'task_id', 'performance_date'], 'daily_performance_user_task_date_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_performance', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('daily_performance_user_task_date_unique');
            
            // Add back the old unique constraint
            $table->unique(['user_id', 'performance_date']);
        });
    }
};