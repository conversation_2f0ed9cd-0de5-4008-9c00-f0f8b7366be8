<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="unlock-history"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="User Lock/Unlock History"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <div class="container-fluid py-4">
            <!-- User Info Card -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card card-profile">
                        <div class="row">
                            <div class="col-lg-4 col-md-6 col-12 mt-n4 mb-3">
                                <div class="p-3 pe-md-0">
                                    <div class="profile-image-card shadow-lg">
                                        <div class="user-profile-image bg-gradient-primary d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user fa-3x text-white"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-8 col-md-6 col-12 my-auto">
                                <div class="card-body ps-lg-0">
                                    <h5 class="mb-0">{{ $targetUser->name }}</h5>
                                    <p class="text-sm text-muted">{{ $targetUser->email }}</p>
                                    <div class="d-flex">
                                        @foreach($targetUser->roles as $role)
                                            <span class="badge bg-gradient-info me-2">{{ $role->name }}</span>
                                        @endforeach
                                    </div>
                                    <div class="mt-3">
                                        <a href="{{ route('unlock-history.index') }}" class="btn btn-sm bg-gradient-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            Back to List
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Locks</p>
                                        <h5 class="font-weight-bolder mb-0">{{ $stats['total_locks'] }}</h5>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                        <i class="fas fa-lock text-lg opacity-10"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Unlocks</p>
                                        <h5 class="font-weight-bolder mb-0">{{ $stats['total_unlocks'] }}</h5>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                        <i class="fas fa-unlock text-lg opacity-10"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Unlock Requests</p>
                                        <h5 class="font-weight-bolder mb-0">{{ $stats['total_requests'] }}</h5>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                        <i class="fas fa-paper-plane text-lg opacity-10"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-8">
                                    <div class="numbers">
                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Current Status</p>
                                        <h5 class="font-weight-bolder mb-0">
                                            @if($stats['active_locks'] > 0)
                                                <span class="text-danger">Locked</span>
                                            @else
                                                <span class="text-success">Unlocked</span>
                                            @endif
                                        </h5>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="icon icon-shape {{ $stats['active_locks'] > 0 ? 'bg-gradient-danger' : 'bg-gradient-success' }} shadow text-center border-radius-md">
                                        <i class="fas {{ $stats['active_locks'] > 0 ? 'fa-lock' : 'fa-unlock' }} text-lg opacity-10"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6 class="mb-0">
                                <i class="fas fa-history me-2 text-primary"></i>
                                Lock/Unlock Timeline
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Complete history of account locks, unlocks and requests</p>
                        </div>
                        <div class="card-body">
                            <div class="timeline timeline-one-side">
                                @forelse($timeline as $index => $event)
                                    <div class="timeline-block mb-4">
                                        <span class="timeline-step">
                                            @if($event['type'] == 'lock')
                                                <i class="fas fa-lock text-danger"></i>
                                            @elseif($event['type'] == 'unlock')
                                                <i class="fas fa-unlock text-success"></i>
                                            @elseif($event['type'] == 'request')
                                                <i class="fas fa-paper-plane text-info"></i>
                                            @elseif($event['type'] == 'approved')
                                                <i class="fas fa-check-circle text-success"></i>
                                            @elseif($event['type'] == 'dismissed')
                                                <i class="fas fa-times-circle text-warning"></i>
                                            @endif
                                        </span>
                                        <div class="timeline-content">
                                            <h6 class="text-dark text-sm font-weight-bold mb-0">
                                                @if($event['type'] == 'lock')
                                                    Account Locked
                                                @elseif($event['type'] == 'unlock')
                                                    Account Unlocked
                                                @elseif($event['type'] == 'request')
                                                    Unlock Request Submitted
                                                @elseif($event['type'] == 'approved')
                                                    Unlock Request Approved
                                                @elseif($event['type'] == 'dismissed')
                                                    Unlock Request Dismissed
                                                @endif
                                            </h6>
                                            <p class="text-secondary text-xs mt-1 mb-0">
                                                {{ $event['date']->format('M d, Y h:i A') }} ({{ $event['date']->diffForHumans() }})
                                            </p>
                                            
                                            @if($event['type'] == 'lock')
                                                <div class="mt-3 mb-2">
                                                    <span class="badge bg-gradient-{{ $event['restriction_type'] == 'performance_not_submitted' ? 'warning' : 'danger' }} mb-2">
                                                        {{ ucfirst(str_replace('_', ' ', $event['restriction_type'])) }}
                                                    </span>
                                                </div>
                                                <p class="text-sm mb-2">
                                                    <strong>Reason:</strong> {{ $event['reason'] }}
                                                </p>
                                                <p class="text-sm mb-0">
                                                    <strong>Locked by:</strong> {{ $event['by_user'] }}
                                                </p>
                                            @elseif($event['type'] == 'unlock')
                                                <div class="mt-3 mb-2">
                                                    <span class="badge bg-gradient-{{ $event['was_manual'] ? 'info' : 'success' }} mb-2">
                                                        {{ $event['was_manual'] ? 'Manual Unlock' : 'System Unlock' }}
                                                    </span>
                                                </div>
                                                <p class="text-sm mb-2">
                                                    <strong>Reason:</strong> {{ $event['reason'] ?: 'No reason provided' }}
                                                </p>
                                                <p class="text-sm mb-0">
                                                    <strong>Unlocked by:</strong> {{ $event['by_user'] }}
                                                </p>
                                            @elseif($event['type'] == 'request')
                                                <div class="mt-3 mb-2">
                                                    <span class="badge bg-gradient-{{ $event['status'] == 'pending' ? 'warning' : ($event['status'] == 'unlocked' ? 'success' : 'secondary') }} mb-2">
                                                        {{ ucfirst($event['status']) }}
                                                    </span>
                                                </div>
                                                <p class="text-sm mb-0">
                                                    <strong>Message:</strong> {{ $event['message'] ?: 'No message provided' }}
                                                </p>
                                            @elseif($event['type'] == 'approved')
                                                <p class="text-sm mb-2">
                                                    <strong>Reason:</strong> {{ $event['reason'] ?: 'No reason provided' }}
                                                </p>
                                                <p class="text-sm mb-0">
                                                    <strong>Approved by:</strong> {{ $event['by_user'] }}
                                                </p>
                                            @elseif($event['type'] == 'dismissed')
                                                <p class="text-sm mb-0">
                                                    <strong>Dismissed by:</strong> {{ $event['by_user'] }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                @empty
                                    <div class="text-center py-4">
                                        <i class="fas fa-history fa-3x text-secondary mb-3"></i>
                                        <h6 class="text-secondary">No history found</h6>
                                        <p class="text-sm text-secondary">This user has no lock/unlock history yet.</p>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <x-footers.auth></x-footers.auth>

    <style>
        .profile-image-card {
            border-radius: 0.75rem;
            overflow: hidden;
        }
        
        .user-profile-image {
            width: 100%;
            height: 150px;
            border-radius: 0.75rem;
        }
        
        .timeline {
            position: relative;
            margin-top: 1rem;
        }
        
        .timeline-one-side:before {
            left: 1rem;
            border-left-width: 2px;
            border-left-style: solid;
            border-left-color: #dee2e6;
            height: 100%;
            position: absolute;
            content: "";
            top: 0;
        }
        
        .timeline-block {
            position: relative;
            margin-left: 3rem;
        }
        
        .timeline-step {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #dee2e6;
            position: absolute;
            left: -3.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .timeline-step i {
            font-size: 1rem;
        }
    </style>
</x-layout>