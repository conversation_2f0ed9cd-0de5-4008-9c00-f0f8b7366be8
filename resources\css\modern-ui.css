/* General Body and Layout */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f0f2f5;
    color: #333;
}

.main-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

/* Loader Styles */
.loader-overlay {
    background-color: rgba(0, 0, 0, 0.6);
}

.loader {
    border-top: 16px solid #4CAF50; /* Green for loader */
}

/* Buttons */
.btn-modern {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.btn-modern:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    color: white;
}

.btn-modern i {
    margin-right: 8px;
}

/* Navigation Buttons (Dashboard, Received Fatawa, etc.) */
.nav-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
}

.nav-buttons-container .btn-modern {
    background-color: #6c757d; /* Gray for navigation */
}

.nav-buttons-container .btn-modern:hover {
    background-color: #5a6268;
}

.nav-buttons-container .btn-modern.active {
    background-color: #28a745; /* Green for active/current page */
}

/* Table Styles */
.table-modern {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table-modern th,
.table-modern td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.table-modern th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    text-transform: uppercase;
    font-size: 0.9em;
}

.table-modern tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

.table-modern tbody tr:hover {
    background-color: #f0f0f0;
    cursor: pointer;
}

/* Specific table cell styles */
.question-cell {
    background-color: #ffffff;
    max-width: 1000px;
    padding: 10px;
    direction: rtl;
    overflow-wrap: break-word;
    text-align: right;
}

.question-text {
    white-space: normal;
    word-wrap: break-word;
    text-align: right;
    max-width: 100%;
    color: black;
}

/* Filter and Search Section */
.filter-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-section .form-group {
    margin-bottom: 15px;
}

.filter-section label {
    font-weight: 500;
    color: #555;
    margin-bottom: 5px;
    display: block;
}

.filter-section .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-sizing: border-box;
}

.apply-filters-button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.apply-filters-button:hover {
    background-color: #0056b3;
}

.apply-filters-button-active {
    background-color: #28a745;
}

/* Folder and Transfer By Sections */
.folder-entries {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.folder-entry,
.folder-transfer-by {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #e9ecef;
    font-size: 0.9em;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.folder-entry.highlight,
.folder-transfer-by.highlight {
    background-color: #d4edda; /* Light green for highlight */
    border-color: #28a745;
    font-weight: bold;
    color: #155724;
}

.folder-entry:hover,
.folder-transfer-by:hover {
    background-color: #dee2e6;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination-container .btn-modern {
    background-color: #007bff;
    margin: 0 5px;
}

.pagination-container .btn-modern:hover {
    background-color: #0056b3;
}

/* Upload Area (from file-upload.blade.php) */
.upload-area {
    padding: 20px;
    border: 2px dashed #007bff; /* Blue dashed border */
    border-radius: 10px;
    background: linear-gradient(135deg, #e7f3ff, #cce7ff); /* Light blue gradient */
    text-align: center;
    cursor: pointer;
    display: block;
    width: 100%;
    height: 120px; /* Slightly taller */
    line-height: 80px; /* Adjust line height for vertical centering */
    font-size: 18px;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: #0056b3; /* Darker blue text */
    transition: background-color 0.25s ease, border-color 0.25s ease;
}

.upload-area input[type="file"] {
    opacity: 0; /* Make file input completely invisible */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.upload-area:hover {
    background-color: #dbeeff;
    border-color: #0056b3;
}

.upload-area i {
    font-size: 36px; /* Larger icon */
    vertical-align: middle;
    color: #007bff;
    margin-right: 10px;
}

.upload-area span {
    vertical-align: middle;
    font-size: 18px;
    color: #0056b3;
}

.container-bt {
    background-color: #f8f9fa; /* Light background for upload section */
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-list-section h5 {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.file-list-section .list-group-item {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    margin-bottom: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.file-list-section .list-group-item strong {
    color: #555;
}

.file-list-section .list-group-item .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    padding: 5px 10px;
    font-size: 0.85em;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.file-list-section .list-group-item .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.file-list-section .list-group-item .view a {
    color: #28a745; /* Green for view link */
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.file-list-section .list-group-item .view a:hover {
    color: #218838;
    text-decoration: underline;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
    padding: 10px;
    border-radius: 5px;
    margin-top: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .main-content {
        padding: 15px;
    }

    .nav-buttons-container {
        flex-direction: column;
        align-items: stretch;
    }

    .table-modern th,
    .table-modern td {
        padding: 8px 10px;
    }

    .upload-area {
        height: 100px;
        line-height: 60px;
    }

    .upload-area i {
        font-size: 30px;
    }

    .upload-area span {
        font-size: 16px;
    }
}