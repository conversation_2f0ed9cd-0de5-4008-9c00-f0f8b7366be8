<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="user-management"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="User Management"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #7c3aed;
                --primary-hover: #6d28d9;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --info-color: #3b82f6;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 1400px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .page-subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
                margin-top: 0.5rem;
                margin-bottom: 0;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: white;
                padding: 1.5rem;
                border-radius: 1rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                border: 1px solid var(--gray-200);
                text-align: center;
            }

            .stat-number {
                font-size: 2rem;
                font-weight: 700;
                color: var(--primary-color);
                margin-bottom: 0.5rem;
            }

            .stat-label {
                color: var(--gray-600);
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .table-card {
                background: white;
                border-radius: 1rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
            }

            .table-header {
                background: var(--gray-50);
                padding: 1.5rem 2rem;
                border-bottom: 1px solid var(--gray-200);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .table-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .table-count {
                background: var(--primary-color);
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 9999px;
                font-size: 0.875rem;
                font-weight: 600;
            }

            .add-user-btn {
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 0.75rem;
                text-decoration: none;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.2s ease;
                border: none;
                cursor: pointer;
            }

            .add-user-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
                color: white;
                text-decoration: none;
            }

            .modern-table {
                width: 100%;
                border-collapse: collapse;
            }

            .modern-table th {
                background: var(--gray-50);
                padding: 1rem 1.5rem;
                text-align: left;
                font-weight: 600;
                color: var(--gray-700);
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                border-bottom: 1px solid var(--gray-200);
            }

            .modern-table td {
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--gray-100);
                vertical-align: middle;
            }

            .modern-table tbody tr:hover {
                background: var(--gray-50);
            }

            .user-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 600;
                font-size: 1rem;
                margin-right: 1rem;
            }

            .user-info {
                display: flex;
                align-items: center;
            }

            .user-details h6 {
                margin: 0;
                font-weight: 600;
                color: var(--gray-800);
            }

            .user-details p {
                margin: 0;
                color: var(--gray-600);
                font-size: 0.875rem;
            }

            .role-badges {
                display: flex;
                flex-wrap: wrap;
                gap: 0.25rem;
            }

            .role-badge {
                padding: 0.25rem 0.75rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .role-admin {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                color: white;
            }

            .role-mujeeb {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                color: white;
            }

            .role-superior {
                background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
                color: white;
            }

            .role-default {
                background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                color: white;
            }

            .action-buttons {
                display: flex;
                gap: 0.5rem;
                align-items: center;
            }

            .btn-view {
                padding: 0.5rem;
                background: var(--info-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-view:hover {
                background: #2563eb;
                transform: translateY(-1px);
                color: white;
                text-decoration: none;
            }

            .btn-edit {
                padding: 0.5rem;
                background: var(--warning-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-edit:hover {
                background: #d97706;
                transform: translateY(-1px);
                color: white;
                text-decoration: none;
            }

            .btn-delete {
                padding: 0.5rem;
                background: var(--danger-color);
                color: white;
                border: none;
                border-radius: 0.5rem;
                cursor: pointer;
                transition: all 0.2s ease;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-delete:hover {
                background: #dc2626;
                transform: translateY(-1px);
            }

            .delete-form {
                display: inline;
            }

            .empty-state {
                text-align: center;
                padding: 3rem 2rem;
                color: var(--gray-600);
            }

            .empty-icon {
                width: 64px;
                height: 64px;
                margin: 0 auto 1rem;
                color: var(--gray-300);
            }

            .alert {
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                margin-bottom: 1.5rem;
                border: 1px solid;
            }

            .alert-success {
                background: #ecfdf5;
                border-color: #10b981;
                color: #065f46;
            }

            .alert-error {
                background: #fef2f2;
                border-color: #ef4444;
                color: #991b1b;
            }

            @media (max-width: 768px) {
                .modern-container {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                }
                
                .table-header {
                    flex-direction: column;
                    gap: 1rem;
                    align-items: stretch;
                }
                
                .action-buttons {
                    flex-direction: column;
                    align-items: stretch;
                }

                .user-info {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .user-avatar {
                    margin-right: 0;
                    margin-bottom: 0.5rem;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                    </svg>
                    User Management
                </h1>
                <p class="page-subtitle">Manage system users, roles, and permissions efficiently</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success">
                    <strong>Success!</strong> {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    <strong>Error!</strong> {{ session('error') }}
                </div>
            @endif

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ $users->count() }}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $users->filter(function($user) { return $user->roles->contains('name', 'Admin'); })->count() }}</div>
                    <div class="stat-label">Administrators</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $users->filter(function($user) { return $user->roles->contains('name', 'mujeeb'); })->count() }}</div>
                    <div class="stat-label">Mujeeb Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $users->where('created_at', '>=', now()->subDays(30))->count() }}</div>
                    <div class="stat-label">New This Month</div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="table-card">
                <div class="table-header">
                    <h2 class="table-title">
                        <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        System Users
                        <span class="table-count">{{ $users->count() }}</span>
                    </h2>
                    <a href="{{ route('users.create') }}" class="add-user-btn">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add New User
                    </a>
                </div>

                @if($users->count() > 0)
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>User</th>
                                <th>Email</th>
                                <th>Roles</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($users as $user)
                            <tr>
                                <td>
                                    <span class="text-sm font-weight-bold">{{ $loop->iteration }}</span>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            {{ strtoupper(substr($user->name, 0, 1)) }}
                                        </div>
                                        <div class="user-details">
                                            <h6>{{ $user->name }}</h6>
                                            <p>ID: {{ $user->id }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-sm">{{ $user->email }}</span>
                                </td>
                                <td>
                                    <div class="role-badges">
                                        @forelse($user->roles as $role)
                                            <span class="role-badge role-{{ strtolower(str_replace(' ', '-', $role->name)) }}">
                                                {{ $role->name }}
                                            </span>
                                        @empty
                                            <span class="role-badge role-default">No Role</span>
                                        @endforelse
                                    </div>
                                </td>
                                <td>
                                    <span class="text-sm">{{ $user->created_at->format('M d, Y') }}</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('users.show', $user->id) }}" class="btn-view" title="View User">
                                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </a>
                                        <a href="{{ route('users.edit', $user->id) }}" class="btn-edit" title="Edit User">
                                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </a>
                                        <form action="{{ route('users.destroy', $user->id) }}" method="POST" class="delete-form">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn-delete" 
                                                    title="Delete User"
                                                    onclick="return confirm('Are you sure you want to delete {{ $user->name }}? This action cannot be undone.')">
                                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <div class="empty-state">
                        <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <h3>No Users Found</h3>
                        <p>Start by adding your first user to the system.</p>
                    </div>
                @endif
            </div>
        </div>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
