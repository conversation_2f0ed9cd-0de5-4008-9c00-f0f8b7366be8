<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class MahlenazarSendController extends Controller
{
    /**
     * Show the form for sending a Mahl-e-Nazar fatwa for checking.
     */
    public function create($fatwa_id)
    {
        $user = Auth::user();

        // Get the fatwa details
        $fatwa = DB::table('uploaded_files')
            ->where('id', $fatwa_id)
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->first();

        if (!$fatwa) {
            return redirect()->route('mahlenazar-fatawa')
                ->with('error', 'Fatwa not found or not in Mahl-e-Nazar status.');
        }

        // Check if user is authorized to send this fatwa (allow mujeeb users for their own fatawa)
        if (!$user->isNazim() && !$user->isSuperior() && !$user->isMujeeb()) {
            return redirect()->route('mahlenazar-fatawa')
                ->with('error', 'You are not authorized to access this page.');
        }

        // For mujeeb users, ensure they can only send their own fatawa
        if ($user->isMujeeb() && !$user->isNazim() && !$user->isSuperior() && $fatwa->sender !== $user->name) {
            return redirect()->route('mahlenazar-fatawa')
                ->with('error', 'You can only send your own fatawa.');
        }

        // Get all checkers but will pre-select mufti_ali_asghar in the view
        $checkers = DB::table('checker')
            ->select('checker_name', 'folder_id', 'munsab')
            ->get();

        return view('mahlenazar.send-fatwa', compact('fatwa', 'checkers'));
    }
    
    /**
     * Store the sent Mahl-e-Nazar fatwa.
     */
    public function store(Request $request)
    {
        DB::beginTransaction(); // Start a database transaction
        $fileStorageErrors = []; // Initialize an array to track file storage errors

        try {
            // Validate the request
            $request->validate([
                'fatwa_id' => 'required|exists:uploaded_files,id',
                'checker' => 'required|string',
                'transfer_by' => 'required|string',
                'folder_date' => 'required|date',
                'files' => 'required|array|min:1',
                'files.*' => 'required|file|max:10240', // 10MB max file size
            ]);

            $user = Auth::user();
            $fatwa_id = $request->fatwa_id;

            // Get the original fatwa
            $originalFatwa = DB::table('uploaded_files')
                ->where('id', $fatwa_id)
                ->where('checked_folder', 'Mahl-e-Nazar')
                ->first();

            if (!$originalFatwa) {
                DB::rollBack();
                return back()->with('error', 'Fatwa not found or not in Mahl-e-Nazar status.');
            }

            // Check authorization (allow mujeeb users for their own fatawa)
            if (!$user->isNazim() && !$user->isSuperior() && !$user->isMujeeb()) {
                DB::rollBack();
                return back()->with('error', 'You are not authorized to send fatawa.');
            }

            // For mujeeb users, ensure they can only send their own fatawa
            if ($user->isMujeeb() && !$user->isNazim() && !$user->isSuperior() && $originalFatwa->sender !== $user->name) {
                DB::rollBack();
                return back()->with('error', 'You can only send your own fatawa.');
            }

            // Validate uploaded file names match the original fatwa file name
            $files = $request->file('files');
            $originalFileName = $originalFatwa->file_name;

            foreach ($files as $file) {
                $uploadedFileName = $file->getClientOriginalName();
                $modifiedUploadedFileName = str_replace(' ', '_', $uploadedFileName);

                // Check if uploaded file name matches the original (with or without space modifications)
                if ($uploadedFileName !== $originalFileName && $modifiedUploadedFileName !== $originalFileName) {
                    DB::rollBack();
                    return back()->with('error', 'File name "' . $uploadedFileName . '" does not match the original fatwa file name "' . $originalFileName . '". Please upload the correct file.');
                }
            }

            // Check if this fatwa code already exists with selected = 0 (already sent for checking)
            $existingFile = DB::table('uploaded_files')
                ->where('file_code', $originalFatwa->file_code)
                ->where('selected', 0)
                ->where('id', '!=', $fatwa_id) // Exclude the current Mahl-e-Nazar record
                ->first();

            if ($existingFile) {
                DB::rollBack();
                return back()->with('error', 'Fatwa No. ' . $existingFile->file_code . ' already sent for checking to checker '
                    . $existingFile->checker . ' in mail folder date ' . $existingFile->mail_folder_date . ' by ' . $existingFile->sender);
            }

            // Proceed with file handling and data insertion logic
            $checker = $request->checker;
            $transferBy = $request->transfer_by;
            $folderDate = $request->folder_date;

            // Check for downloaded records (following FileController logic)
            $existingRecord = DB::table('uploaded_files')
                ->where('mail_folder_date', $folderDate)
                ->where('darulifta_name', $originalFatwa->darulifta_name)
                ->where('checker', $checker)
                ->where('selected', 0)
                ->whereNotNull('downloaded_by_admin')
                ->exists();

            if ($existingRecord) {
                DB::rollBack();
                return back()->with('error', 'This mail folder has been downloaded by admin, therefore select another mail folder date.');
            }

            // Create folder structure (following FileController logic)
            $folderName = Str::slug($folderDate); // Use Str::slug like FileController
            $transfer = str_replace(' ', '', $transferBy); // Remove spaces from transfer_by like FileController
            $iftaFolderName = $folderName . $originalFatwa->darulifta_name . $checker;
            Storage::disk('public')->makeDirectory($iftaFolderName);
            Storage::disk('public')->makeDirectory("$iftaFolderName/Checked");
            Storage::disk('public')->makeDirectory("$iftaFolderName/Mahl-e-Nazar");

            // Store files
            foreach ($files as $index => $file) {
                $originalFilename = $file->getClientOriginalName();
                $modifiedFilename = str_replace(' ', '_', $originalFilename);

                try {
                    $path = $file->storeAs($iftaFolderName, $modifiedFilename, 'public');
                } catch (\Exception $e) {
                    $fileStorageErrors[] = $e->getMessage();
                }
            }

            if (empty($fileStorageErrors)) {
                // Create new record for the sent fatwa (following FileController logic)
                foreach ($files as $index => $file) {
                    $originalFilename = $file->getClientOriginalName();
                    $modifiedFilename = str_replace(' ', '_', $originalFilename);

                    DB::table('uploaded_files')->insert([
                        'file_name' => $modifiedFilename,
                        'file_code' => $originalFatwa->file_code,
                        'sender' => $originalFatwa->sender,
                        'file_created_date' => $originalFatwa->file_created_date,
                        'ftype' => 'Mahl e Nazar', // Set file type to Mahl e Nazar
                        'mail_folder_date' => $folderDate,
                        'mail_recived_date' => now(),
                        'darulifta_name' => $originalFatwa->darulifta_name,
                        'category' => $originalFatwa->category,
                        'checker' => $checker,
                        'selected' => 0, // Not yet processed (following FileController logic)
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }

                // Update questions table (following FileController logic)
                DB::table('questions')
                    ->where('ifta_code', $originalFatwa->file_code)
                    ->update([
                        'send_to_mufti' => 1,
                    ]);

              

                DB::commit(); // Commit the transaction if files were stored successfully
                return redirect()->route('mahlenazar-fatawa')
                    ->with('success', 'Mahl-e-Nazar fatwa sent for checking successfully.');
            } else {
                DB::rollBack();
                return back()->with('fileErrors', $fileStorageErrors);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('MahlenazarSendController store error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return back()->with('error', 'An error occurred while sending the fatwa: ' . $e->getMessage());
        }
    }
}
