
<x-layout bodyClass="g-sidenav-show bg-gray-200">

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">

        <!-- Navbar -->

        <!-- End Navbar -->

         <!-- Loader -->
         <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- FontAwesome CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ asset('assets/css/modern-ui.css') }}" rel="stylesheet">

         <div id="loader-overlay" class="loader-overlay" style="display: none;">
    <div id="loader" class="loader"></div>
</div>



        <!-- Create Appointment Entry Form -->

        <style>
            /* Ensure the highlight class is not overridden */
/* Highlight styles for folder entries */
.folder-entry.highlight {
    background-color: #f0f8ff; /* Light blue background color */
    font-weight: bold; /* Bold text */
    color: #00008b; /* Dark blue text color */
    border: 2px solid #add8e6; /* Light blue border */
    border-radius: 4px; /* Rounded corners */
}

/* Highlight styles for folder transfer-by entries */
.folder-transfer-by.highlight {
    background-color: #e6f7ff; /* Very light blue background color */
    font-weight: bold; /* Bold text */
    color: #003366; /* Darker blue text color */
    border: 1px solid #b3e0ff; /* Light blue border */
    border-radius: 4px; /* Rounded corners */
}

/* Additional styles for hover effect */
.folder-entry.highlight:hover {
    background-color: #d1e7f0; /* Slightly darker blue on hover */
    color: #000080; /* Dark navy blue text color on hover */
}

.folder-transfer-by.highlight:hover {
    background-color: #cce5ff; /* Slightly darker light blue on hover */
    color: #002966; /* Even darker blue text color on hover */
}

/* Optionally, add a transition for smooth effect */
.folder-entry.highlight, .folder-transfer-by.highlight {
    transition: background-color 0.3s, color 0.3s, border 0.3s;
}

/* Keep other styles unchanged */
.folder-entries {
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
}

.folder-entry {
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 5px;
    border: 1px solid #ccc; /* Border for each entry */
    border-radius: 4px;
    background-color: #f9f9f9; /* Background color for better contrast */
    transition: background-color 0.3s; /* Smooth transition for hover effect */
}

.folder-entry:hover {
    background-color: #e9ecef; /* Highlight color on hover */
}

.folder-date, .folder-status {
    white-space: nowrap;
    margin: 2px 0;
}

.date-link {
    text-decoration: none;
    color: #007bff; /* Link color */
}

.date-link:hover {
    text-decoration: underline; /* Underline on hover */
}


            .table-bordered {
    border-collapse: collapse; /* Ensures borders are not doubled */
}

            .loader-overlay {
        position: fixed; /* Fixed positioning */
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* Darker semi-transparent background */
        z-index: 9998; /* Just below the loader */
        display: flex; /* Center loader */
        justify-content: center;
        align-items: center;
    }

    /* Loader CSS */
    .loader {
        border: 16px solid #f3f3f3; /* Light grey */
        border-top: 16px solid #3498db; /* Blue */
        border-radius: 50%;
        width: 120px;
        height: 120px;
        animation: spin 2s linear infinite;
        z-index: 9999; /* Ensure it's on top of the overlay */
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
.custom-upload-container {
    margin: 20px;
}

.custom-upload-area {
    border: 2px dashed #28a745;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    position: relative;
}

.custom-upload-area i {
    font-size: 48px;
    color: #28a745;
}

#customFileList {
    margin-top: 10px;
}

.custom-file-item {
    padding: 10px;
    border-bottom: 1px solid #ddd;
}

.custom-file-item span {
    margin-right: 10px;
}


            .view a i {
    color: #4CAF50; /* Green color for view icon */
}
 .question-cell {
        background-color: #ffffff;
        max-width: 1000px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }
.question-text {
        white-space: normal; /* Allow text to wrap */
        word-wrap: break-word; /* Ensure long words break onto the next line */
        text-align: right; /* Right-align text */
        max-width: 100%; /* Ensure text doesn't overflow beyond the cell */
        color: black;
    }
    @media (max-width: 768px) {
        .question-text {
            white-space: normal;
            word-wrap: break-word;
        }
    }
             .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
           .btn-custom-blue {
    background-color: #0d6efd; /* Bootstrap primary blue color */
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    margin-right: 10px; /* Add gap between buttons */
}

.btn-custom-blue:last-child {
    margin-right: 0; /* Remove margin from the last button */
}

.btn-custom-blue:hover {
    background-color: #0b5ed7; /* Slightly darker blue for hover effect */
    color: white;
}
            .text-black {
    color: black;
}

                        .custom-textarea {
  width: 100%;

  text-align: right;
}




                        .year {
                background-color: #7971ec; /* Year section color */
            }

            .month {
                background-color: #96c47d; /* Month section color */
            }

            .date {
                background-color: #c8d053; /* Date section color */
            }

            .file {
                background-color: #c6d2f4; /* Date section color */
            }

            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }

            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }

            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }

            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }

                #emptbl th {
                    font-weight: bold;
                }

                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }

                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
            .hidden {
                    display: none;
                }
                .hidden-content {
        display: none;
    }
                .custom-table {
        background-color: #f8f9fa; /* Light gray background color */
        text-align: center; /* Center align table content */
    }

    .custom-table th {
        background-color: #343a40; /* Dark background for table header */
        color: #fff; /* White text for table header */
    }

   /* Color for the first set of three rows */
.custom-table tbody tr:nth-child(3n-2),
.custom-table tbody tr:nth-child(3n-1),
.custom-table tbody tr:nth-child(3n) {
    background-color: #e6e6fa; /* Light gray for the first set of three rows */
}

/* Color for the next set of three rows */
.custom-table tbody tr:nth-child(3n+1),
.custom-table tbody tr:nth-child(3n+2),
.custom-table tbody tr:nth-child(3n+3) {
    background-color: #fafad2; /* White for the next set of three rows */
}

/* Adjusting for the following sets of three rows */
.custom-table tbody tr:nth-child(6n-5),
.custom-table tbody tr:nth-child(6n-4),
.custom-table tbody tr:nth-child(6n-3) {
    background-color: #e6e6fa; /* Light gray for the second set of three rows */
}

.custom-table tbody tr:nth-child(6n-2),
.custom-table tbody tr:nth-child(6n-1),
.custom-table tbody tr:nth-child(6n) {
    background-color: #fafad2; /* White for the second set of three rows */
}


  #table2 {
    display: none;
  }
  table {
    font-family: 'Jameel Noori Nastaleeq';
    font-size: 1vw; /* Adjusts based on viewport width */
    width: 100%; /* Ensure table takes full width of the screen */
    table-layout: auto; /* Allows columns to adjust automatically */
    border-collapse: collapse;
}

th, td {
    padding: 8px;
    text-align: left;
    word-wrap: break-word; /* Ensure long words break into multiple lines */
    white-space: normal; /* Allow text wrapping */
}

@media (max-width: 1200px) {
    table {
        font-size: 1.8vw; /* Adjust font size for medium screens */
    }
}

@media (max-width: 768px) {
    table {
        font-size: 1.6vw; /* Adjust font size for tablets */
    }

    th, td {
        display: block; /* Stack columns vertically on small screens */
        width: 100%; /* Full width for each column */
    }
}

@media (max-width: 576px) {
    table {
        font-size: 1.4vw; /* Adjust font size for small mobile screens */
    }

    th, td {
        display: block;
        width: 100%; /* Full width for each column */
    }
}

          .upload-area {
    padding: 20px;
    border: 2px dashed #6c757d; /* Soft gray border */
    border-radius: 10px;
    background: linear-gradient(135deg, #f0f0f0, #d1d1d1); /* Subtle gray gradient */
    text-align: center;
    cursor: pointer;
    display: block;
    width: 100%;
    height: 100px;
    line-height: 100px;
    font-size: 18px;
    position: relative;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05); /* Light shadow for depth */
    color: #333; /* Dark gray text */
    transition: background-color 0.25s ease, border-color 0.25s ease;
}

.upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 1; /* Keeps the file input invisible */
    cursor: pointer;
}

.upload-area:hover {
    background-color: #e2e2e2; /* Slightly darker gray on hover */
}

.upload-area i {
    font-size: 24px;
    vertical-align: middle;
    color: #6c757d; /* Icon color matches the border */
    margin-right: 8px; /* Spacing between icon and text */
}

.upload-area span {
    vertical-align: middle;
    font-size: 18px;
    color: #333; /* Dark gray text color */
}

.container-bg, .container-bt {
    background-color: #f0f0f0; /* Matches the light gray in the gradient */
    margin-top: 20px;
    padding-top: 20px;
}

</style>
<div id="loader" class="loader" style="display: none;"></div>
        <script>
//         function updatefolderdateOptions() {
//     const selectedDarul = document.getElementById('darulifta_name').value;
//     const folderSelect = document.getElementById('folder_select'); // Use the correct id here

//     // Clear existing options
//     folderSelect.innerHTML = '<option value="0">Select Folder</option>';

//     if (selectedDarul !== '0') {
//         fetch(`/get-folder/${selectedDarul}`)
//             .then(response => response.json())
//             .then(data => {
//                 // Use a Set to store unique mail_folder_date values
//                 const uniqueMailFolderDates = new Set();

//                 // Iterate through the data and add mail_folder_date values to the Set
//                 data.forEach(folder => {
//                     uniqueMailFolderDates.add(folder.mail_folder_date);
//                 });

//                 // Convert the Set back to an array
//                 const uniqueMailFolderDatesArray = [...uniqueMailFolderDates];

//                 // Sort the unique values if needed
//                 uniqueMailFolderDatesArray.sort();

//                 // Populate the dropdown with unique values
//                 uniqueMailFolderDatesArray.forEach(mailFolderDate => {
//                     const option = document.createElement('option');
//                     option.value = mailFolderDate;
//                     option.textContent = mailFolderDate;
//                     folderSelect.appendChild(option);
//                 });
//             });
//     }
// }
const dataLabelFiles = {};

// Add event listeners to file input elements to update the dataLabelFiles object
const fileInputs = document.querySelectorAll('input[type="file"]');
fileInputs.forEach(input => {
    input.addEventListener('change', function () {
        const dataLabel = this.getAttribute('data-label');
        dataLabelFiles[dataLabel] = this.files; // Store files in the object using data-label as the key
    });
});

// Add a submit event listener to the form
// document.querySelector('form').addEventListener('submit', function (e) {
//         e.preventDefault(); // Prevent the form from submitting

//         const formData = new FormData(this); // Create a FormData object from the form

//         fetch(this.action, {
//             method: this.method,
//             body: formData,
//         })
//         .then(response => {
//             if (response.ok) {
//                 // Files uploaded successfully
//                 alert('Files uploaded successfully');
//             } else {
//                 // Handle errors here
//                 alert('An error occurred while uploading files');
//             }
//         })
//         .catch(error => {
//             // Handle network or other errors here
//             alert('An error occurred while uploading files');
//         });
//     });
// Add an event listener to the folder select element


        </script>



        <div class="nav-buttons-container">
    <a href="{{ route('dashboard') }}" class="btn-modern"><i class="material-icons">dashboard</i>Dashboard</a>
    <a href="{{ route('recived-fatawa') }}" class="btn-modern"><i class="material-icons">inbox</i>Received Fatawa</a>
    <a href="{{ route('store.selected.values') }}" class="btn-modern active"><i class="material-icons">send</i>Sending Fatawa</a>
    <a href="{{ route('sent-fatawa') }}" class="btn-modern"><i class="material-icons">check_circle</i>Checked Fatawa</a>
</div>

        <table class="table-modern">
    <tbody>
        @php
            $totalCounts = 0; // Total fatawa count across all Darulifta
            $overallFolderCount = 0; // Total folder count across all Darulifta
        @endphp

        @foreach($daruliftaData as $daruliftaName)
            @if(isset($remainingFatawas[$daruliftaName]))
                @php
                    $daruliftaTotalCounts = 0; // Total fatawa count for the current Darulifta
                    $folderCounts = []; // Folder counts for the current Darulifta
                    $transferByCounts = []; // Transfer_by counts for the current Darulifta
                    // Get the current date
                    $currentDate = \Carbon\Carbon::parse($selectedFolder);

                    // Find the closest back folder
                    $closestBackFolder = $backFolderData->sortBy(function($folder) use ($currentDate) {
                        return $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date));
                    })->first();

                    // Find the closest next folder
                    $closestNextFolder = $nextFolderData->sortBy(function($folder) use ($currentDate) {
                        return $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date));
                    })->first();
                @endphp

                @foreach($folderData as $mailfolderDates)
                    @if(isset($remainingFatawas[$daruliftaName][$mailfolderDates]))
                        @foreach($remainingFatawas[$daruliftaName][$mailfolderDates] as $file)
                            @php
                                $folder = $file->mail_folder_date;
                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;

                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                $transferByCounts[$folder][$transferBy] = isset($transferByCounts[$folder][$transferBy]) ? $transferByCounts[$folder][$transferBy] + 1 : 1;

                                $daruliftaTotalCounts++;
                                $totalCounts++;
                            @endphp
                        @endforeach
                    @endif
                @endforeach

                <tr>
                    <td class="align-middle text-center">
                        <div class="d-flex justify-content-between align-items-center">
                            <!-- Back Button -->
                            <div class="back-buttons">
                                @if ($closestBackFolder)
                                    @php
                                        $transferByName = empty($closestBackFolder->transfer_by) ? 'Mujeeb' : $closestBackFolder->transfer_by;
                                        $formattedDate = \Carbon\Carbon::parse($closestBackFolder->mail_folder_date)->format('d/m/Y');
                                    @endphp
                                    <a href="{{ url("/sending1/{$selectedDarul}/{$closestBackFolder->mail_folder_date}/{$transferByName}") }}" class="btn-custom-blue">
                                        Back
                                    </a>
                                @endif
                            </div>

                            <!-- Folder Entries -->
                            <div class="folder-entries">
                                @php
                                    $foldercount = 0; // Initialize folder count for the current Darulifta
                                    // Get URL parameters
                                    $currentFolder = $selectedFolder; // Extract the 'selectedFolder' parameter from the query string
                                    $currentTransferBy = $transfer_by; // Extract the 'transfer_by' parameter from the query string
                                @endphp

                                @foreach ($folderCounts as $folder => $count)
                                    @php
                                        // Check if the current folder matches the URL parameter
                                        $isActiveFolder = $currentFolder === $folder;
                                    @endphp
                                    <div class="folder-entry {{ $isActiveFolder ? 'highlight' : '' }}">
                                        <div class="folder-date">
                                            <a href="{{ url('sending1/' . $daruliftaName . '/' . urlencode($folder)) }}" class="date-link">
                                                {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                            </a> (T-{{ $count }})
                                        </div>

                                        @if(isset($transferByCounts[$folder]))
                                            @foreach($transferByCounts[$folder] as $transferBy => $transferByCount)
                                                @php
                                                    // Check if both folder and transferBy match the URL parameters
                                                    $isActiveTransferBy = $isActiveFolder && $currentTransferBy === $transferBy;
                                                @endphp
                                                <div class="folder-transfer-by {{ $isActiveTransferBy ? 'highlight' : '' }}">
                                                    <a href="{{ url('sending1/' . $daruliftaName . '/' . urlencode($folder) . '/' . urlencode($transferBy)) }}" class="date-link">
                                                        {{ $transferBy }}
                                                    </a>
                                                    - {{ $transferByCount }}
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>

                                    @php
                                        $foldercount++;
                                        $overallFolderCount++;
                                    @endphp
                                @endforeach
                            </div>

                            <!-- Next Button -->
                            <div class="next-buttons">
                                @if ($closestNextFolder)
                                    @php
                                        $transferByName = empty($closestNextFolder->transfer_by) ? 'Mujeeb' : $closestNextFolder->transfer_by;
                                        $formattedDate = \Carbon\Carbon::parse($closestNextFolder->mail_folder_date)->format('d/m/Y');
                                    @endphp
                                    <a href="{{ url("/sending1/{$selectedDarul}/{$closestNextFolder->mail_folder_date}/{$transferByName}") }}" class="btn-custom-blue">
                                        Next
                                    </a>
                                @endif
                            </div>
                        </div>
                    </td>
                </tr>
            @endif
        @endforeach
    </tbody>
</table>

<h5>Overall Total Fatawa: {{ $totalCounts }} And Folder: {{ $overallFolderCount }}</h5>





        @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @elseif(session('fileErrors'))
        <div class="alert alert-danger">
            @foreach(session('fileErrors') as $error)
                {{ $error }}
            @endforeach
        </div>
    @elseif(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif
    <div style="display: flex; align-items: center;">
    <div>
    @php

        $isAllIftaActive = $selectedDarul === null; // Check if 'darulifta' parameter is not set
    @endphp
    <a href="{{ route('sending-fatawa')}}" class="apply-filters-button {{ $isAllIftaActive ? 'apply-filters-button-active' : '' }}">
        All Ifta
    </a>
</div>

@foreach($daruliftalist as $daruliftalistn)
    @php
        $isActive = $selectedDarul == $daruliftalistn; // Check if the current 'darulifta' parameter matches
    @endphp

    <div>
        <a href="{{ route('sending-fatawa', [
            'darulifta' => $daruliftalistn,])}}" class="apply-filters-button {{ request()->route('selectedDarul') == $daruliftalistn ? 'apply-filters-button-active' : '' }}">
            {{ $daruliftalistn }}
        </a>
    </div>
@endforeach
        </div>

            @if (!empty($fatwaData))
            <h1 style="text-align: center;">
                {{$selectedDarul}} {{ \Carbon\Carbon::parse($selectedFolder)->format('d/m/Y') }}
                @if($transfer_by)
                    by {{ ucwords(str_replace('_', ' ', $transfer_by)) }}
                @endif
            </h1>

    <div id="customFileList"></div>
</div>
<form action="{{ route('upload') }}" method="POST" enctype="multipart/form-data" id="submission-form">
@csrf
@livewire('file-upload', ['selectedDarul' => $selectedDarul, 'selectedFolder' => $selectedFolder, 'checker' => $checker])

        @if (!empty($selectedFolder))
            <input type="hidden" name="folder_select" id="folder_select" value="{{ $selectedFolder }}">
            @if (!empty($selectedDarul))
                <input type="hidden" name="darulifta_select" id="darulifta_select" value="{{ $selectedDarul }}">
                <input type="hidden" name="checker_id" id="checker_id" value="{{ $checker }}">



</div>
</div>




        <!-- Display filtered data -->

        <table id="table1" border="2" class="table table-bordered custom-table">
            <thead>
                <tr>

                    <th>Fatwa No</th>

                    <th>Sender</th>


                    <th style="width: 5%; white-space: normal;">
                        Checked File Name</th>
                    <th style="width: 2%; white-space: normal;">
                        Checked Folder Name</th>
                    <th style="width: 5%; white-space: normal;">
                        Checked Grade</th>
                    <th>Checked Tasurat</th>
                    <th>Send To</th>
                    <th>Viral</th>
                    <th>Checked Instructions</th>
                    <th>Selected</th>

                </tr>
            </thead>
            <tbody>

                @foreach ($fatwaData as $row)

                <tr data-id="{{ $row->id }}">

                    <td class="hidden">{{ $row->id }}</td>
                    <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                        {{ $row->file_code }}</td>
                    <td class="hidden">{{ $row->file_name }}</td>
                    <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                        {{ $row->sender }}</td>
                    <td class="hidden">{{ $row->darulifta_name }}</td>
                    <td class="hidden">{{ $row->mail_folder_date }}</td>
                    <!-- Add other table columns as needed -->
                    <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                        <input type="text" name="checked_file_name[]" data-id="{{ $row->id }}"></td>
                        <td class="align-middle text-center skip-clear" style="width: 2%; white-space: normal;">
                        <input type="text" name="checked_folder[]" data-id="{{ $row->id }}"></td>
                        <td class="align-middle text-center" style="width: 10%; white-space: normal;">
                        <select name="checked_grade[]" data-id="{{ $row->id }}">
                            <option value="">Select Grade</option>
                            <option value="Munasib">Munasib</option>
                            <option value="Bhetar">Bhetar</option>
                            <option value="Mumtaz">Mumtaz</option>
                        </select>
                    </td>

                    <td class="align-middle text-center" style="width: 15%; white-space: normal;">
    <div style="display: flex; align-items: center; justify-content: space-between;">
        <livewire:tasurat-dropdown />
        <i class="fas fa-edit" data-toggle="modal" data-target="#myModal" style="cursor: pointer; margin-left: 10px;"></i> <!-- FontAwesome icon -->
    </div>
</td>





                        @php
                            $isMujeeb = request()->segment(4) === 'Mujeeb'; // Assuming 'Mujeeb' is the 4th segment in the URL
                        @endphp

                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                            <select name="by_mufti[]" class="by_mufti" data-id="{{ $row->id }}">
                                @if($isMujeeb)
                                    <option value="to_mujeeb">Mujeeb</option>
                                @else
                                    <option value="to_mujeeb">Mujeeb</option>
                                    <option value="to_checker">Checker</option>
                                @endif
                            </select>
                        </td>
                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                            <input type="hidden" name="viral[{{ $row->id }}]" value="0"> <!-- Hidden input for unchecked value -->
                            <input type="checkbox" name="viral[{{ $row->id }}]" value="{{ Auth::id() }}" data-id="{{ $row->id }}" {{ $row->viral ? 'checked' : '' }}>
                            Viral
                        </td>
                                                <td class="align-middle text-center" style="width: 10%; white-space: normal;">
                            <textarea class="checked_instructions_textarea" name="checked_Instructions[]" data-id="{{ $row->id }}" style="width: 100%;"></textarea>
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                            <livewire:instruction-dropdown />
                            <i class="fas fa-edit" data-toggle="modal" data-target="#instructionModal" style="cursor: pointer; margin-left: 10px;"></i> <!-- FontAwesome icon for Instruction Editor -->
                            </div>


                        </td>
                        <!-- Add the hidden input for checker if it's null or empty -->
                        @if(is_null($row->checker) || $row->checker === '')
                            <input type="hidden" name="checker[]" value="mufti_ali_asghar" data-id="{{ $row->id }}">
                        @else
                            <input type="hidden" name="checker[]" value="{{ $row->checker }}" data-id="{{ $row->id }}">
                        @endif





                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                            <input type="checkbox" name="selected[]" data-id="{{ $row->id }}" onchange="updateCheckboxValue(this)">
                            <input type="hidden" name="row_id[]" value="{{ $row->id }}">
                        </td>

                    <tr>
                    <td colspan="12" class="question-cell align-right text-center">
                            <div class="custom-textarea" id="textAreaExample4"  readonly>
                              @foreach ($quest as $que)
                                @if ($que->ifta_code == $row->file_code)
                                <div class="question-text">سوال: {{ $que->question }}</div>
                                @endif
                              @endforeach
                            </div>
                    </td>
                        </tr>
                    <tr>
                        <td class="align-middle text-center" colspan="2">
                             <span class="font-weight-bold" style="color: green; width: 5%; white-space: normal;">موضوع:{{ $row->category }}</span>
                        </td>
                        <td class="align-middle text-center">
                        <span class="font-weight-bold"  style="color: blue; width: 5%; white-space: normal;">Recived Date & Time</span>
                        <br>
                            <span class="font-weight-bold"  style="color: green; width: 5%; white-space: normal;">{{ $row->mail_recived_date }}</span>
                         </td>
                         <td class="align-middle text-center ">
                                                            @foreach ($que_day_r as $day)
                                                            @if (Str::lower($row->file_code) == Str::lower($day->ifta_code))
                                                                @php
                                                                    $recDate = new \DateTime($day->rec_date);
                                                                    $currentDate = now();
                                                                    $daysDifference = $currentDate->diff($recDate)->days;
                                                                @endphp
                                                                <span class=" font-weight-bold" style="color: blue;">
                                                            Reciption Date:{{$day->rec_date}}
                                                            </span>
                                                            <br>
                                                            <span class=" font-weight-bold" style="color: green;">
                                                            Total Day :{{ $daysDifference }} days
                                                            </span>
                                                            @endif
                                                        @endforeach

                                                        </td>
                                                        <td class="align-middle text-center ">
                                                        <span class="font-weight-bold"  style="color: blue; width: 5%; white-space: normal;">Mahl-e-Nazar Folder</span>
                                                        <br>
                                                            @foreach ($mahlenazar_null as $mahle)
                                                            {{-- <span class=" font-weight-bold"> --}}

                                                                @if ($row->file_code == $mahle->file_code && $mahle->mail_folder_date < $row->mail_folder_date)
                                                                {{ $mahle->mail_folder_date }}
                                                                <br>



                                                                {{-- </span> --}}
                                                                @endif

                                                        @endforeach

                                                        </td>
                                                        <td>
                                                        <span class="view">
                                                                <a href="{{ route('fatwa-detail',
                                                                    ['fatwa' => $row->file_code]) }}" target="_blank">
                                                                    <span class="btn btn-outline-primary">
                                                                        <i class="fas fa-eye"></i> View Details
        </span>




                                                                </a>
                                                            </span>
                                                        </td>
                                                        <td colspan="2">
                                                        @if(is_null($row->transfer_by) || empty($row->transfer_by))
    <span class="view">
        <a href="{{ route('viewRemain', [
                'date' => $row->mail_folder_date . $row->darulifta_name . $row->checker,
                'filename' => $row->file_name
            ]) }}" target="_blank">
            <i class="fas fa-eye"></i>View |
        </a>
    </span>
    <span class="download">
        <a href="{{ route('downloadFile', [
                'date' => $row->mail_folder_date . $row->darulifta_name . $row->checker,
                'filename' => $row->file_name,
                'id' => $row->id
            ]) }}">
            <i class="fas fa-download"></i>Download |
        </a>
    </span>
@else
    <span class="view">
        <a href="{{ route('viewRemain', [
                'date' => $row->mail_folder_date . $row->darulifta_name . $row->checker . '_by_' . $row->transfer_by,
                'filename' => $row->file_name
            ]) }}" target="_blank">
            <i class="fas fa-eye"></i>View |
        </a>
    </span>
    <span class="download">
        <a href="{{ route('downloadFile', [
                'date' => $row->mail_folder_date . $row->darulifta_name . $row->checker . '_by_' . $row->transfer_by,
                'filename' => $row->file_name,
                'id' => $row->id
            ]) }}">
            <i class="fas fa-download"></i>Download |
        </a>
    </span>
@endif
@php
    $deleteFileUrl = route('deleteFile', [
        'mailfolderDates' => $row->mail_folder_date,
        'daruliftaName' => $row->darulifta_name,
        'checker' => $row->checker ?? '',
        'transferby' => $row->transfer_by ?? ''
    ]);

    $downloadfileByadmin = $row->downloaded_by_admin;
    $canDeletefile = is_null($downloadfileByadmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
@endphp

<span class="delete">
    @if ($canDeletefile)
        <a href="#" onclick="deleteFile('{{ $row->id }}', '{{ $row->file_name }}', '{{ $row->file_code }}', '{{ $deleteFileUrl }}')" class="text-danger">
            <i class="fas fa-trash"></i>Delete |
        </a>
    @else
        <span class="text-danger" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Cannot delete, downloaded by admin on {{ $downloadfileByadmin }}">
            <i class="fas fa-trash mx-1"></i>Delete |
        </span>
    @endif
</span>
<td colspan="2">
                <button type="button" onclick="clearRow({{ $row->id }})" class="btn btn-warning">Clear</button>
            </td>

                                                        </td>




                    </tr>

                </tr>
                @endforeach
                @else
                    <tr>
                        <td colspan="11">
                            @if(isset($message))
                            <p>{{ $message }}</p>
                        @endif
                        </td>
                    </tr>
                @endif
                @endif
            </tbody>
        </table>



        <!-- Single "Update" button -->

        {{-- <button type="submit">Update All</button> --}}
        <button type="submit" class="btn btn-primary">Upload</button>
        @endif
    </form>

@php
    // Get the current date
    $currentDate = \Carbon\Carbon::parse($selectedFolder);

    // Find the closest back folder
    $closestBackFolder = $backFolderData->sortBy(function($folder) use ($currentDate) {
        return $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date));
    })->first();

    // Find the closest next folder
    $closestNextFolder = $nextFolderData->sortBy(function($folder) use ($currentDate) {
        return $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date));
    })->first();
@endphp

<div class="d-flex justify-content-between">
    <div class="back-buttons">
        @if ($closestBackFolder)
            @php
                $transferByName = empty($closestBackFolder->transfer_by) ? 'Mujeeb' : $closestBackFolder->transfer_by;
                $formattedDate = \Carbon\Carbon::parse($closestBackFolder->mail_folder_date)->format('d/m/Y');
            @endphp
            <a href="{{ url("/sending1/{$selectedDarul}/{$closestBackFolder->mail_folder_date}/{$transferByName}") }}" class="btn-custom-blue">
                Back
            </a>
        @endif
    </div>

    <div class="next-buttons">
        @if ($closestNextFolder)
            @php
                $transferByName = empty($closestNextFolder->transfer_by) ? 'Mujeeb' : $closestNextFolder->transfer_by;
                $formattedDate = \Carbon\Carbon::parse($closestNextFolder->mail_folder_date)->format('d/m/Y');
            @endphp
            <a href="{{ url("/sending1/{$selectedDarul}/{$closestNextFolder->mail_folder_date}/{$transferByName}") }}" class="btn-custom-blue">
                Next
            </a>
        @endif
    </div>
</div>
<br>

<div style="display: flex; align-items: center;">
    <div>
    @php

        $isAllIftaActive = $selectedDarul === null; // Check if 'darulifta' parameter is not set
    @endphp
    <a href="{{ route('sending-fatawa')}}" class="apply-filters-button {{ $isAllIftaActive ? 'apply-filters-button-active' : '' }}">
        All Ifta
    </a>
</div>

@foreach($daruliftalist as $daruliftalistn)
    @php
        $isActive = $selectedDarul == $daruliftalistn; // Check if the current 'darulifta' parameter matches
    @endphp

    <div>
        <a href="{{ route('sending-fatawa', [
            'darulifta' => $daruliftalistn,])}}" class="apply-filters-button {{ request()->route('selectedDarul') == $daruliftalistn ? 'apply-filters-button-active' : '' }}">
            {{ $daruliftalistn }}
        </a>
    </div>
@endforeach
        </div>
</div>

        <!-- Add a new table to display file details -->
        <table id="table2" border="1">
            <thead>
                <tr>

                    <th>Checked File Name</th>
                    <th>Checked Folder Name</th>
                    <th>Selected</th>
                    <th>ID</th>
                </tr>
            </thead>
            <tbody id="file-details">
                <!-- File details will be added here dynamically -->
            </tbody>
        </table>


<div class="container">
  <!-- The Modal -->
  <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="max-width: 800px;">
      <div class="modal-content">

        <!-- Modal Header -->
        <div class="modal-header">
          <h4 class="modal-title">Tasurat Editor</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

        <!-- Modal body -->
        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
    <!-- Livewire Component -->
    <livewire:tasurat-editor/>
</div>

        <!-- Modal footer -->
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>

      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- The Modal -->
  <div class="modal fade" id="instructionModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="max-width: 800px;">
      <div class="modal-content">

        <!-- Modal Header -->
        <div class="modal-header">
          <h4 class="modal-title">Instruction Editor</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

        <!-- Modal body -->
        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
          <!-- Livewire Component -->
          <livewire:instruction-editor/>
        </div>

        <!-- Modal footer -->
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- File input elements -->



        <!-- JavaScript for filtering data without a button click -->

        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>


        <script>
    // Get references to all input fields and the select elements within the table rows
    // Get references to all input fields and the select elements within the table rows
var selectedDarul = @json($selectedDarul);
var selectedFolder = @json($selectedFolder);

// Listen for changes in the dropdowns and trigger the data fetch
var daruliftaName = selectedDarul;
var mailFolderDate = selectedFolder;

document.addEventListener('DOMContentLoaded', function () {
    // Function to add file details to the table
    function addFileDetails(file, label) {
        var tr = document.createElement('tr');

        // Display the file name in the "Checked File Name" column
        var tdCheckedFileName = document.createElement('td');
        tdCheckedFileName.textContent = file.name;
        tr.appendChild(tdCheckedFileName);

        // Display the label in the "Checked Folder Name" column
        var tdCheckedFolderName = document.createElement('td');
        tdCheckedFolderName.textContent = label;
        tr.appendChild(tdCheckedFolderName);

        // Find the corresponding ID from the first table
        var tdID = document.createElement('td');
        var correspondingID = findCorrespondingID(file.name);
        tdID.textContent = correspondingID;
        tr.appendChild(tdID);

        // Append the row to the table
        document.getElementById('file-details').appendChild(tr);

        // Now that files are in table2, trigger the function to populate table1
        populateTable1FromTable2();
    }

    // Function to find the corresponding ID in the first table
    function findCorrespondingID(fileName) {
        var firstTableRows = document.querySelectorAll('#table1 tbody tr');
        for (var i = 0; i < firstTableRows.length; i++) {
            var firstTableFileName = firstTableRows[i].querySelector('td:nth-child(3)');
            var firstTableID = firstTableRows[i].querySelector('td:nth-child(1)');

            // Add a check to ensure these elements are not null
            if (firstTableFileName && firstTableID) {
                if (firstTableFileName.textContent === fileName) {
                    return firstTableID.textContent;
                }
            }
        }
        return 'Not Found';
    }

    // Get all file input elements
    var fileInputs = document.querySelectorAll('input[type="file"]');

    // Listen for changes in the file inputs
    fileInputs.forEach(function (input) {
        input.addEventListener('change', function (e) {
            var files = e.target.files;
            var label = e.target.getAttribute('data-label');  // Get the label from the data-label attribute

            // Loop through selected files and add them to table2
            for (var i = 0; i < files.length; i++) {
                addFileDetails(files[i], label);  // Pass the label to the addFileDetails function
            }
        });
    });

    // Function to populate table1 from table2
    function populateTable1FromTable2() {
        // Get rows from both tables
        let table1Rows = document.querySelectorAll('#table1 tbody tr');
        let table2Rows = document.querySelectorAll('#table2 tbody tr');

        // Loop through table1 and table2 rows to populate data
        table1Rows.forEach(function (table1Row) {
            let table1FileNameElement = table1Row.querySelector('td:nth-child(3)');
            if (!table1FileNameElement) return; // Skip if the element doesn't exist
            let table1FileName = table1FileNameElement.textContent;

            table2Rows.forEach(function (table2Row) {
                let table2FileNameElement = table2Row.querySelector('td:nth-child(1)');
                if (!table2FileNameElement) return; // Skip if the element doesn't exist
                let table2FileName = table2FileNameElement.textContent;

                if (table1FileName === table2FileName) {
                    // Populate the corresponding columns in table1
                    let checkedFileNameInput = table1Row.querySelector('input[name="checked_file_name[]"]');
                    let checkedFolderInput = table1Row.querySelector('input[name="checked_folder[]"]');
                    let checkbox = table1Row.querySelector('input[name="selected[]"]');

                    if (checkedFileNameInput && checkedFolderInput && checkbox) {
                        checkedFileNameInput.value = table2Row.querySelector('td:nth-child(1)').textContent;
                        checkedFolderInput.value = table2Row.querySelector('td:nth-child(2)').textContent;

                        // Mark the checkbox as checked
                        checkbox.checked = true;
                        checkbox.value = "1";
                    }
                }
            });
        });
    }
});

function updateCheckboxValue(checkbox) {
    const hiddenInput = checkbox.previousElementSibling; // Assuming the hidden input is immediately before the checkbox
    hiddenInput.value = checkbox.checked ? "1" : "0";
}


function clearRow(rowId) {
    // Clear select inputs with name="checked_grade[]"
    document.querySelectorAll(`select[name="checked_grade[]"][data-id="${rowId}"]`).forEach(select => {
        select.selectedIndex = 0; // Reset to first option
    });

    // Clear select inputs with name="checked_tasurat[]"
    document.querySelectorAll(`select[name="checked_tasurat[]"][data-id="${rowId}"]`).forEach(select => {
        select.selectedIndex = 0; // Reset to first option
    });

    // Clear select inputs with name="by_mufti[]"
    document.querySelectorAll(`select[name="by_mufti[]"][data-id="${rowId}"]`).forEach(select => {
        select.selectedIndex = 0; // Reset to first option
    });

    // Clear checkboxes with name="viral[]"
    document.querySelectorAll(`input[type="checkbox"][name="viral[]"][data-id="${rowId}"]`).forEach(checkbox => {
        checkbox.checked = false; // Uncheck the checkbox
    });

    // Clear textareas with name="checked_Instructions[]"
    document.querySelectorAll(`textarea[name="checked_Instructions[]"][data-id="${rowId}"]`).forEach(textarea => {
        textarea.value = ''; // Clear the textarea value
    });
    Livewire.dispatch('resetTasuratDropdown');
    Livewire.dispatch('resetInstructionDropdown');


}
document.addEventListener('DOMContentLoaded', () => {
    const modalContent = document.getElementById('modalContent');

    document.querySelectorAll('[data-toggle="modal"]').forEach(icon => {
        icon.addEventListener('click', function() {
            const url = this.getAttribute('data-url'); // URL to fetch content if needed

            if (url) {
                fetch(url)
                    .then(response => response.text())
                    .then(data => {
                        modalContent.innerHTML = data;
                    })
                    .catch(error => console.error('Error loading content:', error));
            }
        });
    });
});
document.addEventListener('DOMContentLoaded', function() {
        // Select all dropdowns
        const dropdowns = document.querySelectorAll('.tasurat_dropdown');

        // Add event listeners to all dropdowns
        dropdowns.forEach((dropdown) => {
            dropdown.addEventListener('change', function() {
                // Find the corresponding textarea using data-id
                const dataId = dropdown.closest('td').querySelector('.checked_instructions_textarea').dataset.id;
                const textarea = document.querySelector(`.checked_instructions_textarea[data-id="${dataId}"]`);
                if (textarea) {
                    // Get the selected option text
                    const selectedText = dropdown.options[dropdown.selectedIndex].text;

                    // Append the selected text to the textarea with a line break
                    textarea.value += (textarea.value ? '\n' : '') + selectedText;
                }
            });
        });
    });
        </script>



        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
