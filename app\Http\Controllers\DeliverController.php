<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DeliverController extends Controller
{
    private $roleInfo = [];

    /**
     * Initialize role information for the authenticated user
     */
    private function initializeRoleInfo()
    {
        $user = Auth::user();
        $this->roleInfo = [
            'isMujeeb' => false,
            'userName' => $user->name,
            'mujeebName' => null,
            'darulName' => null
        ];

        // Check if user is in mujeebs table
        $mujeeb = DB::table('mujeebs')
            ->where('mujeeb_name', $user->name)
            ->first();

        if ($mujeeb) {
            $this->roleInfo['isMujeeb'] = true;
            $this->roleInfo['mujeebName'] = $user->name;
            $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
        }
    }

    /**
     * Apply role-based filtering to the query
     */
    private function applyRoleBasedFiltering($query)
    {
        if ($this->roleInfo['isMujeeb']) {
            $query->where('sender', $this->roleInfo['userName']);
        }
    }

    /**
     * Get darulifta names based on user role
     */
    private function getDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);

        if ($this->roleInfo['isMujeeb']) {
            // If user is mujeeb, only show their darul's data
            return [$this->roleInfo['darulName']];
        } else if (count($user->roles) > 1) {
            return DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                ->select('uploaded_files.darulifta_name')
                ->distinct()
                ->orderBy('daruliftas.id')
                ->pluck('uploaded_files.darulifta_name')
                ->toArray();
        } else {
            return DB::table('uploaded_files')
                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name', $firstRoleName)
                ->pluck('darulifta_name')
                ->toArray();
        }
    }

    public function index()
    {
        // Initialize role information
        $this->initializeRoleInfo();

        // Get darulifta names based on user role
        $daruliftaNames = $this->getDaruliftaNames();

        $mailfolderDates = DB::table('uploaded_files')
                ->select('mail_folder_date')
                ->distinct()
                ->orderBy('mail_folder_date', 'desc')
                ->pluck('mail_folder_date');

        $okFatawa = [];

        foreach ($mailfolderDates as $mailfolderDate) {
            foreach ($daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('checked_folder', 'ok')
                    ->where('deliver', 0);

                // Apply role-based filtering (Mujeeb users only see their own data)
                $this->applyRoleBasedFiltering($query);

                $data = $query->get();

                if ($data->isNotEmpty()) {
                    $okFatawa[$daruliftaName][$mailfolderDate] = $data;
                }
            }
        }

        return view('deliver.not_deliver', compact('okFatawa', 'daruliftaNames', 'mailfolderDates'));
    }
    public function indexDel()
    {
        // Initialize role information
        $this->initializeRoleInfo();

        // Get darulifta names based on user role
        $daruliftaNames = $this->getDaruliftaNames();

        $mailfolderDates = DB::table('uploaded_files')
                ->select('mail_folder_date')
                ->distinct()
                ->orderBy('mail_folder_date', 'desc')
                ->pluck('mail_folder_date');

        $okFatawa = [];

        foreach ($mailfolderDates as $mailfolderDate) {
            foreach ($daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('checked_folder', 'ok')
                    ->where('deliver', 1);

                // Apply role-based filtering (Mujeeb users only see their own data)
                $this->applyRoleBasedFiltering($query);

                $data = $query->get();

                if ($data->isNotEmpty()) {
                    $okFatawa[$daruliftaName][$mailfolderDate] = $data;
                }
            }
        }

        return view('deliver.deliver', compact('okFatawa', 'daruliftaNames', 'mailfolderDates'));
    }
    public function updateDeliver(Request $request, $id)
{
    $isChecked = $request->input('isChecked') ? 1 : 0;
    
    DB::table('uploaded_files')
        ->where('id', $id)
        ->update([
            'deliver' => $isChecked,
            'deliver_date' => $isChecked ? Carbon::now() : null,
        ]);

    return response()->json(['success' => true]);
}
    public function create()
    {
        // Your code for the create action
        return view('deliver.create');
    }

    public function store(Request $request)
    {
        // Your code for storing data
        // This method typically handles form submissions
    }
    public function updateDeliverDate(Request $request, $id)
{
    $newDeliverDate = $request->input('newDeliverDate');
    
    DB::table('uploaded_files')
        ->where('id', $id)
        ->update(['deliver_date' => $newDeliverDate]);

    return response()->json(['success' => true]);
}
public function updateNotDeliverReason(Request $request, $id)
{
    $newNotDeliverReason = $request->input('newNotDeliverReason');

    try {
        DB::table('uploaded_files')
            ->where('id', $id)
            ->update(['not_deliver_reason' => $newNotDeliverReason]);

        return response()->json(['success' => true]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'error' => $e->getMessage()]);
    }
}
}
