<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="user-management"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="View User"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #7c3aed;
                --primary-hover: #6d28d9;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --info-color: #3b82f6;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 1000px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                color: white !important;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
            }

            .page-header * {
                color: white !important;
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                color: white !important;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .page-title svg {
                color: white !important;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            }

            .page-subtitle {
                font-size: 1.1rem;
                color: white !important;
                opacity: 0.95;
                margin-top: 0.5rem;
                margin-bottom: 0;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            }

            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1rem;
                font-size: 0.875rem;
            }

            .breadcrumb a {
                color: white !important;
                text-decoration: none;
                opacity: 1;
                transition: all 0.2s;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.1);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .breadcrumb a:hover {
                opacity: 1;
                text-decoration: none;
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
            }

            .breadcrumb span {
                color: white !important;
                opacity: 1;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.25);
            }

            .breadcrumb svg {
                color: white !important;
                opacity: 0.8;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            }

            .content-grid {
                display: grid;
                grid-template-columns: 1fr 2fr;
                gap: 2rem;
                align-items: start;
            }

            .user-profile-card {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
                text-align: center;
                position: sticky;
                top: 2rem;
            }

            .user-avatar-large {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                font-size: 3rem;
                margin: 0 auto 1.5rem;
                box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
            }

            .user-name {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--gray-800);
                margin-bottom: 0.5rem;
            }

            .user-email {
                color: var(--gray-600);
                margin-bottom: 1.5rem;
                font-size: 1rem;
            }

            .user-stats {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .stat-item {
                text-align: center;
                padding: 1rem;
                background: var(--gray-50);
                border-radius: 0.75rem;
                border: 1px solid var(--gray-200);
            }

            .stat-number {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--primary-color);
                margin-bottom: 0.25rem;
            }

            .stat-label {
                font-size: 0.75rem;
                color: var(--gray-600);
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .action-buttons {
                display: flex;
                gap: 0.75rem;
                justify-content: center;
            }

            .btn-edit {
                padding: 0.75rem 1.5rem;
                background: var(--warning-color);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .btn-edit:hover {
                background: #d97706;
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
                box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
            }

            .btn-back {
                padding: 0.75rem 1.5rem;
                background: var(--info-color);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .btn-back:hover {
                background: #2563eb;
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
                box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
            }

            .details-card {
                background: white;
                border-radius: 1rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
            }

            .details-header {
                background: var(--gray-50);
                padding: 1.5rem 2rem;
                border-bottom: 1px solid var(--gray-200);
            }

            .details-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .details-icon {
                width: 24px;
                height: 24px;
                color: var(--primary-color);
            }

            .details-content {
                padding: 2rem;
            }

            .detail-section {
                margin-bottom: 2rem;
            }

            .detail-section:last-child {
                margin-bottom: 0;
            }

            .section-title {
                font-size: 1.125rem;
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .section-icon {
                width: 20px;
                height: 20px;
                color: var(--primary-color);
            }

            .detail-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 0;
                border-bottom: 1px solid var(--gray-100);
            }

            .detail-row:last-child {
                border-bottom: none;
            }

            .detail-label {
                font-weight: 600;
                color: var(--gray-700);
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .detail-value {
                color: var(--gray-800);
                font-weight: 500;
            }

            .role-badges {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .role-badge {
                padding: 0.5rem 1rem;
                border-radius: 9999px;
                font-size: 0.875rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .role-admin {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                color: white;
            }

            .role-mujeeb {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                color: white;
            }

            .role-superior {
                background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
                color: white;
            }

            .role-default {
                background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
                color: white;
            }

            .no-roles {
                color: var(--gray-500);
                font-style: italic;
            }

            .back-link {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                color: white !important;
                text-decoration: none;
                font-weight: 600;
                margin-bottom: 2rem;
                padding: 0.875rem 1.75rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
                border-radius: 0.75rem;
                box-shadow: 0 4px 12px rgba(124, 58, 237, 0.25);
                transition: all 0.2s ease;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }

            .back-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
                color: white !important;
                text-decoration: none;
                background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .back-link svg {
                color: white !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
            }

            @media (max-width: 768px) {
                .modern-container {
                    padding: 1rem;
                }
                
                .content-grid {
                    grid-template-columns: 1fr;
                    gap: 1.5rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }
                
                .user-profile-card {
                    position: static;
                }

                .user-stats {
                    grid-template-columns: 1fr;
                }

                .action-buttons {
                    flex-direction: column;
                }

                .detail-row {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Back Link -->
            <a href="{{ route('users.index') }}" class="back-link">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Users List
            </a>

            <!-- Page Header -->
            <div class="page-header">
                <div class="breadcrumb">
                    <a href="{{ route('users.index') }}">User Management</a>
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                    <span>User Profile</span>
                </div>
                <h1 class="page-title">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    User Profile
                </h1>
                <p class="page-subtitle">View detailed information for "{{ $user->name }}"</p>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- User Profile Card -->
                <div class="user-profile-card">
                    <div class="user-avatar-large">
                        {{ strtoupper(substr($user->name, 0, 1)) }}
                    </div>
                    
                    <h2 class="user-name">{{ $user->name }}</h2>
                    <p class="user-email">{{ $user->email }}</p>
                    
                    <div class="user-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ $user->roles->count() }}</div>
                            <div class="stat-label">Roles</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ $user->created_at->diffInDays(now()) }}</div>
                            <div class="stat-label">Days Active</div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{{ route('users.edit', $user->id) }}" class="btn-edit">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            Edit User
                        </a>
                        <a href="{{ route('users.index') }}" class="btn-back">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                            All Users
                        </a>
                    </div>
                </div>

                <!-- User Details -->
                <div class="details-card">
                    <div class="details-header">
                        <h3 class="details-title">
                            <svg class="details-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            User Information
                        </h3>
                    </div>
                    
                    <div class="details-content">
                        <!-- Basic Information -->
                        <div class="detail-section">
                            <h4 class="section-title">
                                <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                Basic Information
                            </h4>
                            
                            <div class="detail-row">
                                <span class="detail-label">User ID</span>
                                <span class="detail-value">#{{ $user->id }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Full Name</span>
                                <span class="detail-value">{{ $user->name }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Email Address</span>
                                <span class="detail-value">{{ $user->email }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Email Verified</span>
                                <span class="detail-value">
                                    @if($user->email_verified_at)
                                        <span style="color: var(--success-color); font-weight: 600;">✓ Verified</span>
                                    @else
                                        <span style="color: var(--warning-color); font-weight: 600;">⚠ Not Verified</span>
                                    @endif
                                </span>
                            </div>
                        </div>

                        <!-- Role Information -->
                        <div class="detail-section">
                            <h4 class="section-title">
                                <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                </svg>
                                Roles & Permissions
                            </h4>
                            
                            <div class="detail-row">
                                <span class="detail-label">Assigned Roles</span>
                                <div class="role-badges">
                                    @forelse($user->roles as $role)
                                        <span class="role-badge role-{{ strtolower(str_replace(' ', '-', $role->name)) }}">
                                            {{ $role->name }}
                                        </span>
                                    @empty
                                        <span class="no-roles">No roles assigned</span>
                                    @endforelse
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="detail-section">
                            <h4 class="section-title">
                                <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Account Timeline
                            </h4>
                            
                            <div class="detail-row">
                                <span class="detail-label">Account Created</span>
                                <span class="detail-value">{{ $user->created_at->format('F d, Y \a\t g:i A') }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Last Updated</span>
                                <span class="detail-value">{{ $user->updated_at->format('F d, Y \a\t g:i A') }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Member Since</span>
                                <span class="detail-value">{{ $user->created_at->diffForHumans() }}</span>
                            </div>
                            
                            @if($user->email_verified_at)
                            <div class="detail-row">
                                <span class="detail-label">Email Verified On</span>
                                <span class="detail-value">{{ $user->email_verified_at->format('F d, Y \a\t g:i A') }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>