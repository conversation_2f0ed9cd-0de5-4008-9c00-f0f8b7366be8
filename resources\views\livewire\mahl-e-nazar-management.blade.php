<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Users</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['total_users'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="ni ni-single-02 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">At Limit</p>
                                <h5 class="font-weight-bolder mb-0 text-warning">{{ $statistics['users_at_limit'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="ni ni-notification-70 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Over Limit</p>
                                <h5 class="font-weight-bolder mb-0 text-danger">{{ $statistics['users_over_limit'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                <i class="ni ni-fat-remove text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Near Limit</p>
                                <h5 class="font-weight-bolder mb-0 text-info">{{ $statistics['users_near_limit'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                <i class="ni ni-time-alarm text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Avg Per User</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['average_per_user'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md">
                                <i class="ni ni-chart-bar-32 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Restricted</p>
                                <h5 class="font-weight-bolder mb-0 text-danger">{{ $statistics['users_with_restrictions'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                <i class="ni ni-lock-circle-open text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Management Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Mahl-e-Nazar Limit Management</h5>
                            <p class="text-sm mb-0">Monitor and manage Mahl-e-Nazar forwarding limits</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <button wire:click="refreshData" class="btn btn-outline-info btn-sm mb-0 me-2">
                                    <i class="fas fa-sync"></i>&nbsp;&nbsp;Refresh
                                </button>
                                <button wire:click="updateAllRestrictions" class="btn btn-outline-warning btn-sm mb-0 me-2">
                                    <i class="fas fa-shield-alt"></i>&nbsp;&nbsp;Update Restrictions
                                </button>
                                <button wire:click="initializeDefaultRestrictions" class="btn btn-outline-danger btn-sm mb-0 me-2">
                                    <i class="fas fa-user-lock"></i>&nbsp;&nbsp;Default Restrict All
                                </button>
                                <button wire:click="sendLimitWarnings" class="btn btn-outline-secondary btn-sm mb-0 me-2">
                                    <i class="fas fa-bell"></i>&nbsp;&nbsp;Send Warnings
                                </button>
                                <button wire:click="openStatsModal" class="btn btn-outline-primary btn-sm mb-0 me-2">
                                    <i class="fas fa-chart-pie"></i>&nbsp;&nbsp;Statistics
                                </button>
                                <button wire:click="openBreakdownModal" class="btn btn-outline-success btn-sm mb-0 me-2">
                                    <i class="fas fa-list-alt"></i>&nbsp;&nbsp;Breakdown
                                </button>
                                <button wire:click="exportReport" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-download"></i>&nbsp;&nbsp;Export Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search users...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select wire:model.live="filterStatus" class="form-select">
                                    <option value="all">All Status</option>
                                    <option value="normal">Normal</option>
                                    <option value="near_limit">Near Limit</option>
                                    <option value="at_limit">At Limit</option>
                                    <option value="restricted">Restricted</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="text-sm text-secondary">
                                    Total Users: {{ $totalUsers }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Count</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Progress</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Remaining</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($userReport as $item)
                                <tr class="{{ $item['is_at_limit'] ? 'table-warning' : '' }}">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $item['user']->name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $item['user']->email }}</p>
                                                @if($item['user']->departments->isNotEmpty())
                                                    <span class="badge badge-sm bg-gradient-info">{{ $item['user']->departments->first()->name }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-sm font-weight-bold">
                                            {{ $item['mahl_e_nazar_count'] }}/{{ $item['limit'] ?? 5 }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="progress-wrapper w-75 mx-auto">
                                            <div class="progress-info">
                                                <div class="progress-percentage">
                                                    <span class="text-xs font-weight-bold">{{ round($this->getProgressPercentage($item['mahl_e_nazar_count'], $item['limit'] ?? 5)) }}%</span>
                                                </div>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar bg-gradient-{{ $this->getProgressColor($item['mahl_e_nazar_count'], $item['limit'] ?? 5) }}" 
                                                     role="progressbar" 
                                                     style="width: {{ $this->getProgressPercentage($item['mahl_e_nazar_count'], $item['limit'] ?? 5) }}%">
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $item['remaining_capacity'] }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $this->getStatusColor($item['status']) }}">
                                            {{ $this->getStatusText($item['status']) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button wire:click="openUserModal({{ $item['user']->id }})"
                                                    class="btn btn-sm btn-outline-info mb-0" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            @php
                                                $isManuallyAllowed = app(\App\Services\MahlENazarService::class)->isManuallyAllowed($item['user']);
                                            @endphp

                                            @if($isManuallyAllowed)
                                                <button wire:click="restrictUser({{ $item['user']->id }})"
                                                        class="btn btn-sm btn-outline-danger mb-0" title="Restrict User">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            @else
                                                <button wire:click="allowUser({{ $item['user']->id }})"
                                                        class="btn btn-sm btn-outline-success mb-0" title="Allow User">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            @endif

                                            @if($item['has_restriction'])
                                                <button wire:click="toggleUserRestriction({{ $item['user']->id }})"
                                                        class="btn btn-sm btn-outline-warning mb-0" title="Remove Limit Restriction">
                                                    <i class="fas fa-unlock"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <p class="text-secondary mb-0">No users found.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Statistics Modal -->
    @if($showStatsModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mahl-e-Nazar Statistics</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-gradient-primary">
                                <div class="card-body text-white">
                                    <h3 class="text-white">{{ $detailedBreakdown['totalCounts'] ?? $statistics['total_mahl_e_nazar'] ?? 0 }}</h3>
                                    <p class="mb-0">Total Mahl-e-Nazar Fatawa</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-gradient-info">
                                <div class="card-body text-white">
                                    <h3 class="text-white">{{ $statistics['average_per_user'] ?? 0 }}</h3>
                                    <p class="mb-0">Average Per User</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="card bg-gradient-success">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white">{{ $statistics['total_users'] - $statistics['users_at_limit'] - $statistics['users_over_limit'] - $statistics['users_near_limit'] }}</h4>
                                    <p class="mb-0 text-sm">Normal</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-gradient-info">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white">{{ $statistics['users_near_limit'] ?? 0 }}</h4>
                                    <p class="mb-0 text-sm">Near Limit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-gradient-warning">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white">{{ $statistics['users_at_limit'] ?? 0 }}</h4>
                                    <p class="mb-0 text-sm">At Limit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-gradient-danger">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white">{{ $statistics['users_over_limit'] ?? 0 }}</h4>
                                    <p class="mb-0 text-sm">Over Limit</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>System Health</h6>
                        <div class="progress">
                            @php
                                $healthPercentage = $statistics['total_users'] > 0
                                    ? (($statistics['total_users'] - $statistics['users_over_limit'] - $statistics['users_at_limit']) / $statistics['total_users']) * 100
                                    : 100;
                            @endphp
                            <div class="progress-bar bg-gradient-{{ $healthPercentage >= 80 ? 'success' : ($healthPercentage >= 60 ? 'warning' : 'danger') }}"
                                 role="progressbar" style="width: {{ $healthPercentage }}%">
                                {{ round($healthPercentage) }}% Healthy
                            </div>
                        </div>
                        <small class="text-muted">Users not at or over limit</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- User Details Modal -->
    @if($showUserModal && $selectedUser)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details - {{ $selectedUser->name }}</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">User Information</small>
                                <p class="mb-1"><strong>Name:</strong> {{ $selectedUser->name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $selectedUser->email }}</p>
                                <p class="mb-1"><strong>Roles:</strong>
                                    @foreach($selectedUser->roles as $role)
                                        <span class="badge bg-gradient-info">{{ $role->name }}</span>
                                    @endforeach
                                </p>
                                @if($selectedUser->departments->isNotEmpty())
                                    <p class="mb-1"><strong>Departments:</strong>
                                        @foreach($selectedUser->departments as $department)
                                            <span class="badge bg-gradient-secondary">{{ $department->name }}</span>
                                        @endforeach
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Mahl-e-Nazar Count</small>
                                <h4 class="text-primary">{{ $this->getUserMahlENazarCount($selectedUser->name) }}/{{ $selectedUser->mahl_e_nazar_limit ?? 5 }}</h4>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Remaining Capacity</small>
                                <h4 class="text-success">{{ max(0, ($selectedUser->mahl_e_nazar_limit ?? 5) - $this->getUserMahlENazarCount($selectedUser->name)) }}</h4>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Mahl-e-Nazar Limit</small>
                                <div class="input-group">
                                    <input type="number" min="1" class="form-control" wire:model.defer="selectedUser.mahl_e_nazar_limit" placeholder="Default: 5">
                                    <button class="btn btn-primary" wire:click="saveUserLimit">Save Limit</button>
                                </div>
                                <small class="text-muted">Set a custom limit for this user. Leave blank for system default (5).</small>
                            </div>
                        </div>
                    </div>

                    @if($selectedUser->activeRestrictions->isNotEmpty())
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Active Restrictions</h6>
                        @foreach($selectedUser->activeRestrictions as $restriction)
                            <p class="mb-1"><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', $restriction->restriction_type)) }}</p>
                            <p class="mb-1"><strong>Reason:</strong> {{ $restriction->reason }}</p>
                            <p class="mb-0"><strong>Since:</strong> {{ $restriction->restricted_at->format('M d, Y h:i A') }}</p>
                        @endforeach
                    </div>
                    @endif

                    @php
                        $count = $this->getUserMahlENazarCount($selectedUser->name);
                        $percentage = min(100, ($count / 12) * 100);
                    @endphp

                    <div class="progress-wrapper">
                        <div class="progress-info">
                            <div class="progress-percentage">
                                <span class="text-sm font-weight-bold">{{ round($this->getProgressPercentage($count, $selectedUser->mahl_e_nazar_limit ?? 5)) }}% of limit used</span>
                            </div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-gradient-{{ $this->getProgressColor($count, $selectedUser->mahl_e_nazar_limit ?? 5) }}"
                                 role="progressbar" style="width: {{ $this->getProgressPercentage($count, $selectedUser->mahl_e_nazar_limit ?? 5) }}%">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                    @if($selectedUser->activeRestrictions->isNotEmpty())
                        <button type="button" class="btn btn-success" wire:click="toggleUserRestriction({{ $selectedUser->id }})">
                            <i class="fas fa-unlock"></i> Remove Restriction
                        </button>
                    @else
                        <button type="button" class="btn btn-warning" wire:click="toggleUserRestriction({{ $selectedUser->id }})">
                            <i class="fas fa-lock"></i> Apply Restriction
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Detailed Breakdown Modal -->
    @if($showBreakdownModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mahl-e-Nazar Detailed Breakdown</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    @if(isset($detailedBreakdown['summaryData']) && count($detailedBreakdown['summaryData']) > 0)
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Summary</h6>
                            <p class="mb-0">Total Mahl-e-Nazar Fatawa: <strong>{{ $detailedBreakdown['totalCounts'] ?? 0 }}</strong></p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-striped">
                                <thead class="table-dark text-center">
                                    <tr>
                                        <th style="width: 20%;">Darulifta</th>
                                        <th style="width: 60%;">Mujeeb Mahl-e-Nazar Details</th>
                                        <th style="width: 20%;">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($detailedBreakdown['summaryData'] as $daruliftaName => $data)
                                        <tr>
                                            <td class="fw-bold text-center align-middle">
                                                {{ $daruliftaName }}
                                            </td>
                                            <td class="text-wrap">
                                                @foreach($data['senders'] as $sender => $count)
                                                    <span class="badge bg-light text-primary me-1 mb-1">
                                                        {{ $sender }}: {{ $count }}
                                                    </span>
                                                    @if (!$loop->last)
                                                        <span class="text-muted">|</span>
                                                    @endif
                                                @endforeach
                                            </td>
                                            <td class="text-center align-middle">
                                                <span class="badge bg-success fs-6">{{ $data['total'] }}</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-3 text-end">
                            <h5 class="text-success fw-semibold">
                                Overall Total: {{ $detailedBreakdown['totalCounts'] ?? 0 }}
                            </h5>
                        </div>
                    @else
                        <div class="alert alert-warning text-center">
                            <h6>No Mahl-e-Nazar Fatawa Found</h6>
                            <p class="mb-0">There are currently no fatawa in Mahl-e-Nazar status.</p>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                    <button type="button" class="btn btn-primary" onclick="window.open('/mahlenazar-fatawa', '_blank')">
                        <i class="fas fa-external-link-alt"></i> View Full Page
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
