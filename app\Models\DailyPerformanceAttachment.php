<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class DailyPerformanceAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'daily_performance_id',
        'original_name',
        'file_name',
        'file_path',
        'file_type',
        'mime_type',
        'file_size',
        'uploaded_by',
    ];

    protected $casts = [
        'file_size' => 'integer',
    ];

    /**
     * Get the daily performance record that owns this attachment.
     */
    public function dailyPerformance()
    {
        return $this->belongsTo(DailyPerformance::class);
    }

    /**
     * Get the user who uploaded this attachment.
     */
    public function uploadedBy()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFileSizeHumanAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the file extension.
     */
    public function getFileExtensionAttribute()
    {
        return pathinfo($this->original_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if the file is an image.
     */
    public function isImage()
    {
        return in_array(strtolower($this->file_extension), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
    }

    /**
     * Check if the file is a PDF.
     */
    public function isPdf()
    {
        return strtolower($this->file_extension) === 'pdf';
    }

    /**
     * Check if the file is a Word document.
     */
    public function isWordDocument()
    {
        return in_array(strtolower($this->file_extension), ['doc', 'docx']);
    }

    /**
     * Get the file icon class based on file type.
     */
    public function getFileIconAttribute()
    {
        if ($this->isImage()) {
            return 'fas fa-image text-success';
        } elseif ($this->isPdf()) {
            return 'fas fa-file-pdf text-danger';
        } elseif ($this->isWordDocument()) {
            return 'fas fa-file-word text-primary';
        } elseif (in_array(strtolower($this->file_extension), ['xls', 'xlsx'])) {
            return 'fas fa-file-excel text-success';
        } elseif (in_array(strtolower($this->file_extension), ['ppt', 'pptx'])) {
            return 'fas fa-file-powerpoint text-warning';
        } elseif (in_array(strtolower($this->file_extension), ['txt'])) {
            return 'fas fa-file-alt text-secondary';
        } else {
            return 'fas fa-file text-secondary';
        }
    }

    /**
     * Get the download URL for this attachment.
     */
    public function getDownloadUrlAttribute()
    {
        return route('daily-performance.attachment.download', $this->id);
    }

    /**
     * Delete the file from storage when the model is deleted.
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($attachment) {
            if (Storage::disk('public')->exists($attachment->file_path)) {
                Storage::disk('public')->delete($attachment->file_path);
            }
        });
    }
}