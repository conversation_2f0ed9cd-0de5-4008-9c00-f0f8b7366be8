<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="issue-management"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Issue Management"></x-navbars.navs.auth>
        
        <!-- Modern Styles -->
        <style>
            .modern-card {
                background: #ffffff;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
            }
            
            .modern-card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1.5rem 2rem;
                border: none;
            }
            
            .modern-card-body {
                padding: 2rem;
            }
            
            .btn-modern {
                border-radius: 8px;
                padding: 0.5rem 1.5rem;
                font-weight: 500;
                border: none;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }
            
            .btn-modern-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            
            .btn-modern-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                color: white;
            }
            
            .btn-modern-success {
                background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
                color: white;
            }
            
            .btn-modern-success:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
                color: white;
            }
            
            .btn-modern-warning {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
            }
            
            .btn-modern-warning:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
                color: white;
            }
            
            .btn-modern-danger {
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                color: white;
            }
            
            .btn-modern-danger:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
                color: white;
            }
            
            .table-modern {
                border-collapse: separate;
                border-spacing: 0;
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                font-family: 'Jameel Noori Nastaleeq', serif;
            }
            
            .table-modern thead th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: 600;
                padding: 1rem;
                border: none;
                text-align: center;
            }
            
            .table-modern tbody td {
                padding: 1rem;
                border-bottom: 1px solid #e9ecef;
                vertical-align: middle;
                text-align: center;
            }
            
            .table-modern tbody tr:hover {
                background-color: #f8f9ff;
                transform: scale(1.01);
                transition: all 0.3s ease;
            }
            
            .modern-input {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
            }
            
            .modern-input:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }
            
            .modern-select {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
            }
            
            .modern-select:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }
            
            .badge-muzo {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 0.4rem 0.8rem;
                border-radius: 20px;
                font-weight: 600;
                font-size: 0.85rem;
            }
            
            .badge-zeli {
                background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
                color: white;
                padding: 0.3rem 0.6rem;
                border-radius: 15px;
                font-weight: 500;
                font-size: 0.8rem;
                margin: 0.2rem;
                display: inline-block;
            }
            
            .sub-issues-container {
                max-height: 200px;
                overflow-y: auto;
                padding: 0.5rem;
                background: #f8f9ff;
                border-radius: 8px;
                margin-top: 0.5rem;
            }
            
            .alert-modern {
                border-radius: 8px;
                border: none;
                padding: 1rem 1.5rem;
                margin-bottom: 1rem;
            }
            
            .alert-success {
                background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
                color: white;
            }
            
            .alert-danger {
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                color: white;
            }
        </style>
        
        <div class="container-fluid py-4">
            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-modern">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                </div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-danger alert-modern">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                </div>
            @endif
            
            <!-- Header Section -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0 text-white">
                                <i class="fas fa-tags me-2"></i>
                                Muzo & Zeli-Muzo Management
                            </h4>
                            <p class="mb-0 opacity-75">Manage categories and subcategories for questions</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn-modern btn-modern-primary" data-bs-toggle="modal" data-bs-target="#addMuzoModal">
                                <i class="fas fa-plus"></i>
                                Add Muzo
                            </button>
                            <button class="btn-modern btn-modern-success" data-bs-toggle="modal" data-bs-target="#addZeliMuzoModal">
                                <i class="fas fa-plus"></i>
                                Add Zeli-Muzo
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="modern-card-body">
                    <div class="table-responsive">
                        <table class="table-modern w-100">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>S.No</th>
                                    <th><i class="fas fa-tag me-1"></i>Muzo Name</th>
                                    <th><i class="fas fa-tags me-1"></i>Zeli-Muzo Count</th>
                                    <th><i class="fas fa-list me-1"></i>Zeli-Muzo List</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($issues as $index => $issue)
                                <tr>
                                    <td>
                                        <span class="badge-muzo">{{ $index + 1 }}</span>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-primary">{{ $issue->name }}</div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-white px-3 py-1 rounded-pill">
                                            {{ $issue->subIssues->count() }} Zeli-Muzo
                                        </span>
                                    </td>
                                    <td>
                                        <div class="sub-issues-container">
                                            @forelse($issue->subIssues as $subIssue)
                                                <span class="badge-zeli">
                                                    {{ $subIssue->name }}
                                                    <button class="btn btn-sm text-white ms-1" 
                                                            onclick="editSubIssue({{ $subIssue->id }}, '{{ $subIssue->name }}')"
                                                            title="Edit">
                                                        <i class="fas fa-edit" style="font-size: 0.7rem;"></i>
                                                    </button>
                                                    <button class="btn btn-sm text-white ms-1" 
                                                            onclick="deleteSubIssue({{ $subIssue->id }})"
                                                            title="Delete">
                                                        <i class="fas fa-trash" style="font-size: 0.7rem;"></i>
                                                    </button>
                                                </span>
                                            @empty
                                                <span class="text-muted">No Zeli-Muzo added yet</span>
                                            @endforelse
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2 justify-content-center">
                                            <button class="btn-modern btn-modern-warning btn-sm" 
                                                    onclick="editIssue({{ $issue->id }}, '{{ $issue->name }}')"
                                                    title="Edit Muzo">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-modern btn-modern-danger btn-sm" 
                                                    onclick="deleteIssue({{ $issue->id }})"
                                                    title="Delete Muzo">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Muzo Modal -->
        <div class="modal fade" id="addMuzoModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <h5 class="modal-title">
                            <i class="fas fa-plus me-2"></i>Add New Muzo
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="{{ route('issue-management.store-issue') }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="muzo_name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Muzo Name
                                </label>
                                <input type="text" class="modern-input w-100" id="muzo_name" name="name" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn-modern btn-modern-primary">
                                <i class="fas fa-save"></i>Save Muzo
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Add Zeli-Muzo Modal -->
        <div class="modal fade" id="addZeliMuzoModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); color: white;">
                        <h5 class="modal-title">
                            <i class="fas fa-plus me-2"></i>Add New Zeli-Muzo
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="{{ route('issue-management.store-sub-issue') }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="parent_muzo" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Select Parent Muzo
                                </label>
                                <select class="modern-select w-100" id="parent_muzo" name="parent_id" required>
                                    <option value="">Select Muzo</option>
                                    @foreach($issues as $issue)
                                        <option value="{{ $issue->id }}">{{ $issue->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="zeli_muzo_name" class="form-label">
                                    <i class="fas fa-tags me-1"></i>Zeli-Muzo Name
                                </label>
                                <input type="text" class="modern-input w-100" id="zeli_muzo_name" name="name" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn-modern btn-modern-success">
                                <i class="fas fa-save"></i>Save Zeli-Muzo
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit Muzo Modal -->
        <div class="modal fade" id="editMuzoModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <h5 class="modal-title">
                            <i class="fas fa-edit me-2"></i>Edit Muzo
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="editMuzoForm" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="edit_muzo_name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Muzo Name
                                </label>
                                <input type="text" class="modern-input w-100" id="edit_muzo_name" name="name" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn-modern btn-modern-warning">
                                <i class="fas fa-save"></i>Update Muzo
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit Zeli-Muzo Modal -->
        <div class="modal fade" id="editZeliMuzoModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <h5 class="modal-title">
                            <i class="fas fa-edit me-2"></i>Edit Zeli-Muzo
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="editZeliMuzoForm" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="edit_zeli_muzo_name" class="form-label">
                                    <i class="fas fa-tags me-1"></i>Zeli-Muzo Name
                                </label>
                                <input type="text" class="modern-input w-100" id="edit_zeli_muzo_name" name="name" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn-modern btn-modern-warning">
                                <i class="fas fa-save"></i>Update Zeli-Muzo
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script>
            function editIssue(id, name) {
                document.getElementById('edit_muzo_name').value = name;
                document.getElementById('editMuzoForm').action = `/issue-management/issue/${id}`;
                new bootstrap.Modal(document.getElementById('editMuzoModal')).show();
            }

            function editSubIssue(id, name) {
                document.getElementById('edit_zeli_muzo_name').value = name;
                document.getElementById('editZeliMuzoForm').action = `/issue-management/sub-issue/${id}`;
                new bootstrap.Modal(document.getElementById('editZeliMuzoModal')).show();
            }

            function deleteIssue(id) {
                if (confirm('Are you sure you want to delete this Muzo? This will also delete all its Zeli-Muzo.')) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/issue-management/issue/${id}`;

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';

                    const methodField = document.createElement('input');
                    methodField.type = 'hidden';
                    methodField.name = '_method';
                    methodField.value = 'DELETE';

                    form.appendChild(csrfToken);
                    form.appendChild(methodField);
                    document.body.appendChild(form);
                    form.submit();
                }
            }

            function deleteSubIssue(id) {
                if (confirm('Are you sure you want to delete this Zeli-Muzo?')) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/issue-management/sub-issue/${id}`;

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';

                    const methodField = document.createElement('input');
                    methodField.type = 'hidden';
                    methodField.name = '_method';
                    methodField.value = 'DELETE';

                    form.appendChild(csrfToken);
                    form.appendChild(methodField);
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        </script>

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
