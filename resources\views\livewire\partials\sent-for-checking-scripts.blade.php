{{-- Sent For Checking Scripts Partial --}}

<!-- External Libraries -->
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script src="https://rawgit.com/eKoopmans/html2pdf/master/dist/html2pdf.bundle.js"></script>
<script src="//unpkg.com/alpinejs" defer></script>

<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Initialize popovers
    $('[data-bs-toggle="popover"]').popover();
    
    // Auto-refresh functionality
    let autoRefreshInterval;
    
    function startAutoRefresh() {
        autoRefreshInterval = setInterval(function() {
            if (document.visibilityState === 'visible') {
                updateStatistics();
            }
        }, 300000); // Refresh every 5 minutes
    }
    
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }
    
    // Start auto-refresh
    startAutoRefresh();
    
    // Stop auto-refresh when page is hidden
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'hidden') {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });
});

// Update statistics without full page reload
function updateStatistics() {
    // This would typically make an AJAX call to get updated statistics
    // For now, we'll just update the timestamp
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    
    // Add a small indicator that data was refreshed
    const refreshIndicator = document.createElement('small');
    refreshIndicator.className = 'text-muted ms-2';
    refreshIndicator.innerHTML = `<i class="fas fa-sync-alt"></i> Updated: ${timeString}`;
    
    // Remove existing refresh indicators
    document.querySelectorAll('.refresh-indicator').forEach(el => el.remove());
    
    // Add new refresh indicator to stats section
    const statsSection = document.querySelector('.stats-grid');
    if (statsSection) {
        refreshIndicator.className += ' refresh-indicator';
        statsSection.parentNode.appendChild(refreshIndicator);
        
        // Remove after 3 seconds
        setTimeout(() => {
            refreshIndicator.remove();
        }, 3000);
    }
}

// Export functionality
function exportTableData() {
    const table = document.querySelector('.table-responsive table');
    if (!table) {
        alert('No table found to export');
        return;
    }
    
    // Create a new workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.table_to_sheet(table);
    
    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Sent Fatawa');
    
    // Generate filename with current date
    const now = new Date();
    const filename = `sent-fatawa-${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')}.xlsx`;
    
    // Save the file
    XLSX.writeFile(wb, filename);
}

// Print functionality
function printTable() {
    const printContent = document.querySelector('.card-modern').cloneNode(true);
    
    // Remove unnecessary elements for printing
    printContent.querySelectorAll('.btn-modern, .form-check, .quick-actions').forEach(el => el.remove());
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Sent Fatawa Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
                    .stat-card { border: 1px solid #ddd; padding: 1rem; text-align: center; }
                    .stat-value { font-size: 1.5rem; font-weight: bold; color: #007bff; }
                    .stat-label { font-size: 0.875rem; color: #6c757d; }
                    @media print {
                        .no-print { display: none; }
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                <h1>Sent Fatawa Report</h1>
                <p>Generated on: ${new Date().toLocaleString()}</p>
                ${printContent.innerHTML}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Fullscreen functionality
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.log('Error attempting to enable fullscreen:', err);
        });
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

// Refresh data functionality
function refreshData() {
    // Show loading indicator
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    if (refreshBtn) {
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
        refreshBtn.disabled = true;
        
        // Simulate refresh delay
        setTimeout(() => {
            location.reload();
        }, 1000);
    } else {
        location.reload();
    }
}

// Filter management
function clearAllFilters() {
    const url = new URL(window.location);
    url.searchParams.delete('selectedmujeeb');
    url.searchParams.delete('selectedmufti');
    url.searchParams.delete('selectedTimeFrame');
    url.searchParams.delete('selectedexclude');
    url.searchParams.delete('startDate');
    url.searchParams.delete('endDate');
    url.searchParams.delete('selectedMonths');
    
    window.location.href = url.toString();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + R for refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshData();
    }
    
    // Ctrl/Cmd + P for print
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        printTable();
    }
    
    // F11 for fullscreen
    if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
    }
    
    // Escape to clear filters
    if (e.key === 'Escape') {
        const hasFilters = new URLSearchParams(window.location.search).has('selectedmujeeb') ||
                          new URLSearchParams(window.location.search).has('selectedmufti') ||
                          new URLSearchParams(window.location.search).has('selectedTimeFrame');
        
        if (hasFilters && confirm('Clear all filters?')) {
            clearAllFilters();
        }
    }
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Auto-save form data to localStorage
function saveFormData() {
    const form = document.querySelector('form');
    if (form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        localStorage.setItem('sentFatawaFilters', JSON.stringify(data));
    }
}

// Load saved form data
function loadFormData() {
    const savedData = localStorage.getItem('sentFatawaFilters');
    if (savedData) {
        const data = JSON.parse(savedData);
        Object.keys(data).forEach(key => {
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = data[key];
            }
        });
    }
}

// Initialize form data management
document.addEventListener('DOMContentLoaded', function() {
    loadFormData();
    
    // Save form data on change
    document.querySelectorAll('form input, form select').forEach(input => {
        input.addEventListener('change', saveFormData);
    });
});

// Performance monitoring
let pageLoadTime = performance.now();
window.addEventListener('load', function() {
    pageLoadTime = performance.now() - pageLoadTime;
    console.log(`Page loaded in ${pageLoadTime.toFixed(2)}ms`);
});
</script>

<!-- Add XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
