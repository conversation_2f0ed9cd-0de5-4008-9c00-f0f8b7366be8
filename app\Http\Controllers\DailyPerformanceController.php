<?php

namespace App\Http\Controllers;

use App\Models\DailyPerformance;
use App\Models\DailyPerformanceAttachment;
use App\Models\User;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class DailyPerformanceController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display the daily performance form.
     */
    public function create()
    {
        $this->authorize('submit-performance');
        
        return view('daily-performance.create');
    }

    /**
     * Store a newly created performance report.
     */
    public function store(Request $request)
    {
        $this->authorize('submit-performance');
        
        $validated = $request->validate([
            'performance_date' => 'required|date',
            'task_id' => 'required|exists:workflow_tasks,id',
            'tasks_completed' => 'required|string',
            'challenges_faced' => 'nullable|string',
            'next_day_plan' => 'nullable|string',
            'hours_worked' => 'required|numeric|min:0|max:24',
            'additional_notes' => 'nullable|string',
        ]);

        // Get task and department information
        $task = Task::findOrFail($validated['task_id']);

        $validated['user_id'] = auth()->id();
        $validated['department_id'] = $task->department_id;
        $validated['overall_rating'] = null;
        $validated['is_submitted'] = true;
        $validated['submitted_at'] = Carbon::now();

        DailyPerformance::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'task_id' => $validated['task_id'],
                'performance_date' => $validated['performance_date'],
            ],
            $validated
        );

        return redirect()->route('dashboard')
            ->with('success', 'Daily performance report submitted successfully!');
    }

    /**
     * Display performance management for supervisors/admins.
     */
    public function index()
    {
        $this->authorize('view-all-performance');
        
        return view('daily-performance.index');
    }

    /**
     * Display the specified performance report.
     */
    public function show(DailyPerformance $dailyPerformance)
    {
        $this->authorize('viewPerformance', $dailyPerformance->user);
        
        return view('daily-performance.show', compact('dailyPerformance'));
    }

    /**
     * Show the form for editing the performance report.
     */
    public function edit(DailyPerformance $dailyPerformance)
    {
        $this->authorize('viewPerformance', $dailyPerformance->user);
        
        // Only allow editing if not submitted or user is admin
        if ($dailyPerformance->is_submitted && !auth()->user()->isNazim()) {
            return redirect()->route('daily-performance.show', $dailyPerformance)
                ->with('error', 'Cannot edit submitted performance report.');
        }
        
        return view('daily-performance.edit', compact('dailyPerformance'));
    }

    /**
     * Update the specified performance report.
     */
    public function update(Request $request, DailyPerformance $dailyPerformance)
    {
        $this->authorize('viewPerformance', $dailyPerformance->user);
        
        $validated = $request->validate([
            'tasks_completed' => 'required|string',
            'challenges_faced' => 'nullable|string',
            'next_day_plan' => 'nullable|string',
            'hours_worked' => 'required|numeric|min:0|max:24',
            'additional_notes' => 'nullable|string',
            'superior_rating' => 'nullable|in:poor,fair,good,excellent',
            'superior_comments' => 'nullable|string|max:1000',
        ]);

        // Handle superior rating fields
        if (isset($validated['superior_rating']) && $validated['superior_rating']) {
            $validated['rated_by'] = auth()->id();
            $validated['rated_at'] = now();
        } elseif (isset($validated['superior_rating']) && !$validated['superior_rating']) {
            // If superior rating is being removed
            $validated['rated_by'] = null;
            $validated['rated_at'] = null;
        }

        // Handle submission status
        if ($request->input('action') === 'submit' && !$dailyPerformance->is_submitted) {
            $validated['is_submitted'] = true;
            $validated['submitted_at'] = now();
        }

        $dailyPerformance->update($validated);

        $message = $request->input('action') === 'submit'
            ? 'Performance report submitted successfully!'
            : 'Performance report updated successfully.';

        return redirect()->route('daily-performance.show', $dailyPerformance)
            ->with('success', $message);
    }

    /**
     * Get performance statistics.
     */
    public function statistics()
    {
        $this->authorize('view-all-performance');
        
        $user = auth()->user();
        $query = DailyPerformance::query();
        
        // Apply user restrictions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->departments->flatMap->users->pluck('id');
                $userIds = $assistantIds->merge($departmentUserIds)->unique();
                $query->whereIn('user_id', $userIds);
            }
        }

        $stats = [
            'total_reports' => $query->count(),
            'submitted_today' => $query->whereDate('performance_date', Carbon::today())->where('is_submitted', true)->count(),
            'pending_today' => $query->whereDate('performance_date', Carbon::today())->where('is_submitted', false)->count(),
            'avg_hours_worked' => round($query->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
            'excellent_ratings' => $query->where('superior_rating', 'excellent')->where('is_submitted', true)->count(),
            'superior_rated' => $query->whereNotNull('superior_rating')->where('is_submitted', true)->count(),
            'this_week_submitted' => $query->whereBetween('performance_date', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->where('is_submitted', true)->count(),
            'this_month_submitted' => $query->whereMonth('performance_date', Carbon::now()->month)
                ->whereYear('performance_date', Carbon::now()->year)
                ->where('is_submitted', true)->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get performance summary with filtering options.
     */
    public function getSummary(Request $request)
    {
        // Allow all authenticated users to view summary, but filter data based on permissions
        
        $viewType = $request->get('view_type', 'overall'); // overall, department, superior
        $month = $request->get('month', 'current'); // current, previous, custom
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $status = $request->get('status', 'all');
        $department = $request->get('department');
        
        // Set date range based on month filter
        if ($month === 'current') {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        } elseif ($month === 'previous') {
            $startDate = Carbon::now()->subMonth()->startOfMonth();
            $endDate = Carbon::now()->subMonth()->endOfMonth();
        } elseif ($month === 'custom' && $startDate && $endDate) {
            $startDate = Carbon::parse($startDate);
            $endDate = Carbon::parse($endDate);
        } else {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        }

        $user = auth()->user();
        $query = DailyPerformance::query()->with(['user', 'task', 'department']);
        
        // Apply date filter
        $query->whereBetween('performance_date', [$startDate, $endDate]);
        
        // Apply user permissions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->departments->flatMap->users->pluck('id');
                $userIds = $assistantIds->merge($departmentUserIds)->unique();
                $query->whereIn('user_id', $userIds);
            }
        }
        
        // Apply status filter
        if ($status !== 'all') {
            if ($status === 'submitted') {
                $query->where('is_submitted', true);
            } elseif ($status === 'pending') {
                $query->where('is_submitted', false);
            } elseif ($status === 'overdue') {
                $query->whereDate('performance_date', '<', Carbon::today())
                      ->where('is_submitted', false);
            }
        }
        
        // Apply department filter
        if ($department) {
            $query->whereHas('user.departments', function ($q) use ($department) {
                $q->where('departments.id', $department);
            });
        }

        switch ($viewType) {
            case 'department':
                return $this->getDepartmentWisePerformanceSummary($query, $startDate, $endDate);
            case 'superior':
                return $this->getSuperiorWisePerformanceSummary($query, $startDate, $endDate);
            default:
                return $this->getOverallPerformanceSummary($query, $startDate, $endDate);
        }
    }

    /**
     * Get overall performance summary statistics.
     */
    private function getOverallPerformanceSummary($query, $startDate, $endDate)
    {
        $performances = $query->get();
        
        // Get users who should have submitted performance (have active tasks)
        $usersWithTasks = User::whereHas('assignedTasks', function ($q) {
            $q->whereNotIn('status', ['completed', 'cancelled']);
        })->count();
        
        $submittedCount = $performances->where('is_submitted', true)->count();
        $pendingCount = $performances->where('is_submitted', false)->count();
        $overdueCount = $performances->filter(function($perf) {
            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
        })->count();
        
        return [
            'view_type' => 'overall',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'summary' => [
                'total_users' => $usersWithTasks,
                'submitted' => $submittedCount,
                'pending' => $pendingCount,
                'overdue' => $overdueCount,
                'submission_rate' => $usersWithTasks > 0 ? round(($submittedCount / $usersWithTasks) * 100, 1) : 0,
                'avg_hours' => round($performances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                'excellent_ratings' => $performances->where('superior_rating', 'excellent')->count(),
                'poor_ratings' => $performances->where('superior_rating', 'poor')->count()
            ]
        ];
    }

    /**
     * Get department-wise performance summary statistics.
     */
    private function getDepartmentWisePerformanceSummary($query, $startDate, $endDate)
    {
        $performances = $query->get();
        $departments = Department::active()->get();
        
        $departmentSummary = [];
        
        foreach ($departments as $department) {
            // Get users in this department who have tasks
            $deptUserIds = $department->users()->whereHas('assignedTasks', function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            })->pluck('users.id');
            
            $deptPerformances = $performances->whereIn('user_id', $deptUserIds);
            
            if ($deptUserIds->count() > 0) {
                $submittedCount = $deptPerformances->where('is_submitted', true)->count();
                
                $departmentSummary[] = [
                    'department' => [
                        'id' => $department->id,
                        'name' => $department->name
                    ],
                    'summary' => [
                        'total_users' => $deptUserIds->count(),
                        'submitted' => $submittedCount,
                        'pending' => $deptPerformances->where('is_submitted', false)->count(),
                        'overdue' => $deptPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count(),
                        'submission_rate' => round(($submittedCount / $deptUserIds->count()) * 100, 1),
                        'avg_hours' => round($deptPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                        'excellent_ratings' => $deptPerformances->where('superior_rating', 'excellent')->count()
                    ],
                    'users' => $this->getDepartmentUsersPerformanceSummary($deptPerformances, $department, $deptUserIds)
                ];
            }
        }
        
        return [
            'view_type' => 'department',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'departments' => $departmentSummary
        ];
    }

    /**
     * Get superior-wise performance summary statistics.
     */
    private function getSuperiorWisePerformanceSummary($query, $startDate, $endDate)
    {
        $performances = $query->get();
        $superiors = User::whereHas('roles', function($q) {
            $q->where('name', 'Superior');
        })->with('assistants')->get();
        
        $superiorSummary = [];
        
        foreach ($superiors as $superior) {
            $superiorPerformances = $performances->where('user_id', $superior->id);
            
            $assistantsSummary = [];
            foreach ($superior->assistants as $assistant) {
                $assistantPerformances = $performances->where('user_id', $assistant->id);
                
                if ($assistantPerformances->count() > 0) {
                    $submittedCount = $assistantPerformances->where('is_submitted', true)->count();
                    
                    $assistantsSummary[] = [
                        'assistant' => [
                            'id' => $assistant->id,
                            'name' => $assistant->name,
                            'email' => $assistant->email
                        ],
                        'summary' => [
                            'total_reports' => $assistantPerformances->count(),
                            'submitted' => $submittedCount,
                            'pending' => $assistantPerformances->where('is_submitted', false)->count(),
                            'overdue' => $assistantPerformances->filter(function($perf) {
                                return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                            })->count(),
                            'avg_hours' => round($assistantPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                            'avg_rating' => $this->getAverageRating($assistantPerformances->where('is_submitted', true))
                        ]
                    ];
                }
            }
            
            $totalTeamReports = $superiorPerformances->count() + collect($assistantsSummary)->sum('summary.total_reports');
            
            if ($totalTeamReports > 0) {
                $superiorSubmitted = $superiorPerformances->where('is_submitted', true)->count();
                
                $superiorSummary[] = [
                    'superior' => [
                        'id' => $superior->id,
                        'name' => $superior->name,
                        'email' => $superior->email
                    ],
                    'superior_performance' => [
                        'total_reports' => $superiorPerformances->count(),
                        'submitted' => $superiorSubmitted,
                        'pending' => $superiorPerformances->where('is_submitted', false)->count(),
                        'overdue' => $superiorPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count(),
                        'avg_hours' => round($superiorPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                        'avg_rating' => $this->getAverageRating($superiorPerformances->where('is_submitted', true))
                    ],
                    'team_summary' => [
                        'total_reports' => $totalTeamReports,
                        'submitted' => $superiorSubmitted + collect($assistantsSummary)->sum('summary.submitted'),
                        'pending' => $superiorPerformances->where('is_submitted', false)->count() + collect($assistantsSummary)->sum('summary.pending'),
                        'overdue' => $superiorPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count() + collect($assistantsSummary)->sum('summary.overdue'),
                        'avg_hours' => round(($superiorPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0 + collect($assistantsSummary)->avg('summary.avg_hours')) / 2, 1)
                    ],
                    'assistants' => $assistantsSummary
                ];
            }
        }
        
        return [
            'view_type' => 'superior',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'superiors' => $superiorSummary
        ];
    }

    /**
     * Get department users performance summary.
     */
    private function getDepartmentUsersPerformanceSummary($performances, $department, $userIds)
    {
        $usersSummary = [];
        
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            if ($user) {
                $userPerformances = $performances->where('user_id', $userId);
                $submittedCount = $userPerformances->where('is_submitted', true)->count();
                
                $usersSummary[] = [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->isSuperior() ? 'Superior' : ($user->isMujeeb() ? 'Assistant' : 'Other')
                    ],
                    'summary' => [
                        'total_reports' => $userPerformances->count(),
                        'submitted' => $submittedCount,
                        'pending' => $userPerformances->where('is_submitted', false)->count(),
                        'overdue' => $userPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count(),
                        'avg_hours' => round($userPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                        'avg_rating' => $this->getAverageRating($userPerformances->where('is_submitted', true))
                    ]
                ];
            }
        }
        
        return $usersSummary;
    }

    /**
     * Calculate average rating from performance records.
     */
    private function getAverageRating($performances)
    {
        $ratingValues = [
            'poor' => 1,
            'fair' => 2,
            'good' => 3,
            'excellent' => 4
        ];
        
        $ratings = $performances->whereNotNull('superior_rating')->pluck('superior_rating');
        
        if ($ratings->isEmpty()) {
            return 'N/A';
        }
        
        $avgValue = $ratings->map(function($rating) use ($ratingValues) {
            return $ratingValues[$rating] ?? 0;
        })->avg();
        
        $ratingLabels = array_flip($ratingValues);
        return $ratingLabels[round($avgValue)] ?? 'N/A';
    }

    /**
     * Get user's performance history.
     */
    public function userHistory(User $user)
    {
        $this->authorize('viewPerformance', $user);
        
        $performances = DailyPerformance::where('user_id', $user->id)
            ->orderBy('performance_date', 'desc')
            ->paginate(15);

        return view('daily-performance.user-history', compact('user', 'performances'));
    }

    /**
     * Send reminder to users who haven't submitted today's performance.
     */
    public function sendReminders()
    {
        $this->authorize('view-all-performance');
        
        $today = Carbon::today();
        $submittedUserIds = DailyPerformance::where('performance_date', $today)
            ->where('is_submitted', true)
            ->pluck('user_id');

        $usersToRemind = User::whereNotIn('id', $submittedUserIds)
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
            })
            ->whereHas('assignedTasks', function ($query) {
                $query->whereNotIn('status', ['completed', 'cancelled']);
            })
            ->get();

        // Here you would implement the actual reminder logic (email, notification, etc.)
        $reminderCount = $usersToRemind->count();

        return response()->json([
            'message' => "Reminders sent to {$reminderCount} users.",
            'users_reminded' => $reminderCount,
        ]);
    }

    /**
     * Check if user has submitted today's performance.
     */
    public function checkTodaysSubmission()
    {
        $hasSubmitted = DailyPerformance::isSubmittedForToday(auth()->id());
        
        return response()->json([
            'has_submitted' => $hasSubmitted,
            'date' => Carbon::today()->format('Y-m-d'),
        ]);
    }

    /**
     * Download a performance attachment.
     */
    public function downloadAttachment(DailyPerformanceAttachment $attachment)
    {
        // Check if user can access this attachment
        $user = auth()->user();
        $performanceUser = $attachment->dailyPerformance->user;
        
        // Allow access if:
        // 1. User owns the performance record
        // 2. User is admin/nazim
        // 3. User is superior of the performance record owner
        if ($user->id !== $performanceUser->id && 
            !$user->isNazim() && 
            !$user->isSuperior()) {
            abort(403, 'Unauthorized access to attachment.');
        }
        
        // Additional check for superiors - they should have access to their assistants' performance
        if ($user->isSuperior() && $user->id !== $performanceUser->id) {
            $hasAccess = $user->assistants->contains('id', $performanceUser->id) ||
                        $user->departments->flatMap->users->contains('id', $performanceUser->id);
            
            if (!$hasAccess) {
                abort(403, 'Unauthorized access to attachment.');
            }
        }

        if (!Storage::disk('public')->exists($attachment->file_path)) {
            abort(404, 'File not found.');
        }

        return Storage::disk('public')->download($attachment->file_path, $attachment->original_name);
    }

    /**
     * View a performance attachment in browser.
     */
    public function viewAttachment(DailyPerformanceAttachment $attachment)
    {
        // Check if user can access this attachment (same logic as download)
        $user = auth()->user();
        $performanceUser = $attachment->dailyPerformance->user;
        
        // Allow access if:
        // 1. User owns the performance record
        // 2. User is admin/nazim
        // 3. User is superior of the performance record owner
        if ($user->id !== $performanceUser->id && 
            !$user->isNazim() && 
            !$user->isSuperior()) {
            abort(403, 'Unauthorized access to attachment.');
        }
        
        // Additional check for superiors
        if ($user->isSuperior() && $user->id !== $performanceUser->id) {
            $hasAccess = $user->assistants->contains('id', $performanceUser->id) ||
                        $user->departments->flatMap->users->contains('id', $performanceUser->id);
            
            if (!$hasAccess) {
                abort(403, 'Unauthorized access to attachment.');
            }
        }

        if (!Storage::disk('public')->exists($attachment->file_path)) {
            abort(404, 'File not found.');
        }

        // Get file content and MIME type
        $filePath = Storage::disk('public')->path($attachment->file_path);
        $mimeType = $attachment->mime_type;
        
        // For viewable file types, return the file content with appropriate headers
        if ($this->isViewableInBrowser($attachment)) {
            return response()->file($filePath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $attachment->original_name . '"'
            ]);
        }
        
        // For non-viewable files, redirect to download
        return $this->downloadAttachment($attachment);
    }

    /**
     * Check if file can be viewed in browser.
     */
    private function isViewableInBrowser(DailyPerformanceAttachment $attachment)
    {
        $viewableTypes = [
            'application/pdf',
            'text/plain',
            'text/html',
            'text/css',
            'text/javascript',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml'
        ];
        
        return in_array($attachment->mime_type, $viewableTypes);
    }

    /**
     * Get my performance reports.
     */
    public function myReports()
    {
        $performances = DailyPerformance::where('user_id', auth()->id())
            ->orderBy('performance_date', 'desc')
            ->paginate(15);

        return view('daily-performance.my-reports', compact('performances'));
    }
}
