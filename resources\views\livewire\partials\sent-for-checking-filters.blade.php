<!-- Filter Section -->
<div class="filter-section">
    <h5 class="mb-3">
        <i class="fas fa-filter me-2"></i>
        Filters & Search
    </h5>

    <form method="GET" action="{{ route('sent-for-checking', [
        'darulifta' => request()->route('darulifta'),
        'mailfolder' => request()->route('mailfolder')
    ]) }}">
        <!-- Hidden inputs to preserve display options -->
        <input type="hidden" name="showDetail" value="{{ request('showDetail', '0') }}">
        <input type="hidden" name="showQue" value="{{ request('showQue', '0') }}">
        <input type="hidden" name="showChat" value="{{ request('showChat', '0') }}">

        <div class="filter-grid">
            <!-- Mujeeb Filter -->
            <div class="form-group-modern">
                <label for="mujeebframe">
                    <i class="fas fa-user me-1"></i>
                    Select Mujeeb
                </label>
                <select class="form-control-modern" id="mujeebframe" name="selectedmujeeb">
                    <option value="all" {{ request('selectedmujeeb') === 'all' ? 'selected' : '' }}>All Mujeeb</option>
                    @foreach ($mujeebs as $sender)
                        <option value="{{ $sender }}" {{ request('selectedmujeeb') === $sender ? 'selected' : '' }}>
                            {{ $sender }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Mufti Filter -->
            <div class="form-group-modern">
                <label for="muftiframe">
                    <i class="fas fa-user-tie me-1"></i>
                    Select Mufti
                </label>
                <select class="form-control-modern" id="muftiframe" name="selectedmufti">
                    @if ($munsab == 'Musaddiq' || is_null($munsab))
                        <option value="all" {{ request('selectedmufti') === 'all' || !request('selectedmufti') ? 'selected' : '' }}>All</option>
                        @foreach ($checkerlist as $item)
                            <option value="{{ $item->folder_id }}" {{ request('selectedmufti') == $item->folder_id ? 'selected' : '' }}>
                                {{ $item->checker_name }}
                            </option>
                        @endforeach
                    @else
                        @foreach ($checkerlist as $item)
                            @if ($item->checker_name == $userName)
                                <option value="{{ $item->folder_id }}" {{ request('selectedmufti') == $item->folder_id ? 'selected' : '' }}>
                                    {{ $item->checker_name }}
                                </option>
                                <option value="transfer_checked" {{ request('selectedmufti') === 'transfer_checked' ? 'selected' : '' }}>
                                    Transfer Checked
                                </option>
                            @endif
                        @endforeach
                    @endif
                </select>
            </div>

            <!-- Time Frame Filter -->
            <div class="form-group-modern">
                <label for="timeframe">
                    <i class="fas fa-calendar me-1"></i>
                    Time Frame
                </label>
                <select class="form-control-modern" id="timeframe" name="selectedTimeFrame">
                    <option value="this_month" {{ request('selectedTimeFrame') === 'this_month' ? 'selected' : '' }}>This Month</option>
                    <option value="last_month" {{ request('selectedTimeFrame') === 'last_month' ? 'selected' : '' }}>Last Month</option>
                    <option value="other" {{ request('selectedTimeFrame') === 'other' ? 'selected' : '' }}>Other</option>
                    @if (request('selectedTimeFrame') === 'custom')
                        <option value="custom" selected>Selected Date</option>
                    @endif
                </select>
            </div>

            <!-- Exclude Filter -->
            <div class="form-group-modern">
                <label for="exclude">
                    <i class="fas fa-filter me-1"></i>
                    Exclude
                </label>
                <select class="form-control-modern" id="exclude" name="selectedexclude">
                    <option value="exclude_checked" {{ request('selectedexclude') === 'exclude_checked' ? 'selected' : '' }}>Exclude Checked</option>
                    <option value="all_rec" {{ request('selectedexclude') === 'all_rec' ? 'selected' : '' }}>All Sent</option>
                </select>
            </div>

            <!-- Start Date -->
            <div class="form-group-modern">
                <label for="start_date">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Start Date
                </label>
                <input type="date" class="form-control-modern" id="start_date" name="startDate" value="{{ request('startDate') }}">
            </div>

            <!-- End Date -->
            <div class="form-group-modern">
                <label for="end_date">
                    <i class="fas fa-calendar-check me-1"></i>
                    End Date
                </label>
                <input type="date" class="form-control-modern" id="end_date" name="endDate" value="{{ request('endDate') }}">
            </div>

            <!-- Apply Button -->
            <div class="form-group-modern">
                <button type="submit" class="btn-modern btn-primary-modern w-100">
                    <i class="fas fa-search me-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>

        <!-- Month Selector for "Other" timeframe -->
        @if (request('selectedTimeFrame') === 'other')
            <div class="mt-4">
                <h6 class="mb-3">Select Months:</h6>
                <div class="row">
                    @foreach ($datefilter as $data)
                        @php
                            $monthName = DateTime::createFromFormat('!m', $data->month)->format('F');
                        @endphp
                        <div class="col-md-3 col-sm-6 mb-2">
                            <div class="form-check">
                                <input type="checkbox" name="selectedMonths[]" id="month-{{ $data->year }}-{{ $data->month }}"
                                       value="{{ $data->year }}-{{ $data->month }}" class="form-check-input"
                                       @php
                                           $selectedMonths = request('selectedMonths', []);
                                           if (is_string($selectedMonths)) {
                                               $selectedMonths = explode(',', $selectedMonths);
                                           }
                                           $isChecked = in_array("$data->year-$data->month", $selectedMonths);
                                       @endphp
                                       {{ $isChecked ? 'checked' : '' }}>
                                <label for="month-{{ $data->year }}-{{ $data->month }}" class="form-check-label">
                                    {{ $monthName }} {{ $data->year }}
                                </label>
                            </div>
                        </div>
                    @endforeach
                </div>
                <!-- Hidden inputs for month filter to preserve display options -->
                <input type="hidden" name="showDetail" value="{{ request('showDetail', '0') }}">
                <input type="hidden" name="showQue" value="{{ request('showQue', '0') }}">
                <input type="hidden" name="showChat" value="{{ request('showChat', '0') }}">
                <input type="hidden" name="selectedmujeeb" value="{{ request('selectedmujeeb', 'all') }}">
                <input type="hidden" name="selectedmufti" value="{{ request('selectedmufti', 'all') }}">
                <input type="hidden" name="selectedTimeFrame" value="{{ request('selectedTimeFrame', 'other') }}">
                <input type="hidden" name="startDate" value="{{ request('startDate') }}">
                <input type="hidden" name="endDate" value="{{ request('endDate') }}">

                <button type="submit" class="btn-modern btn-primary-modern mt-3">
                    <i class="fas fa-filter me-2"></i>
                    Filter By Date
                </button>
            </div>
        @endif
    </form>
</div>
