<!-- Summary Table -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Darulifta Summary
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="table-responsive">
            <table class="table-modern">
                <thead>
                    <tr>
                        <th>
                            <i class="fas fa-building me-2"></i>
                            Darulifta
                        </th>
                        @if ($selectedmufti == 'all')
                            <th>
                                <i class="fas fa-user-check me-2"></i>
                                Checker
                            </th>
                        @endif
                        <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Remaining Fatawa Dates
                        </th>
                        <th>
                            <i class="fas fa-chart-bar me-2"></i>
                            Total
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if ($selectedmufti == 'all')
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($sendingFatawa[$daruliftaName]))
                                @foreach($sendingFatawa[$daruliftaName] as $checked => $dates)
                                    @php
                                        $daruliftaTotalCounts = 0;
                                        $folderEntries = [];
                                    @endphp

                                    @foreach($dates as $mailfolderDates => $files)
                                        @php
                                            $filesCollection = is_array($files) ? collect($files) : $files;
                                        @endphp
                                        @foreach($filesCollection as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';

                                                $folderEntries[$folder] = $folderEntries[$folder] ?? ['count' => 0, 'transfer_by' => []];
                                                $folderEntries[$folder]['count']++;
                                                $folderEntries[$folder]['transfer_by'][$transferBy] = ($folderEntries[$folder]['transfer_by'][$transferBy] ?? 0) + 1;

                                                $daruliftaTotalCounts++;
                                            @endphp
                                        @endforeach
                                    @endforeach

                                    <tr>
                                        <td>
                                            @if ($loop->first || $loop->parent->first)
                                                @php
                                                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                                @endphp
                                                <a href="{{ route('sending-fatawa', [
                                                    'darulifta' => $daruliftaName,
                                                    'selectedmujeeb' => $this->selectedmujeeb,
                                                    'selectedmufti' => $this->selectedmufti,
                                                    'selectedTimeFrame' => $this->selectedTimeFrame,
                                                    'startDate' => $this->startDate,
                                                    'endDate' => $this->endDate,
                                                ]) }}&{{ $selectedMonthsQuery }}"
                                                   class="text-decoration-none fw-bold text-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i>
                                                    {{ $daruliftaName }}
                                                </a>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $checked }}</span>
                                        </td>
                                        <td>
                                            <div class="folder-entries-modern">
                                                @php $foldercount = 0; @endphp
                                                @foreach ($folderEntries as $folder => $data)
                                                    <div class="folder-entry-modern">
                                                        <div class="folder-date-modern">
                                                            <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder]) }}"
                                                               class="text-decoration-none">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                            </a>
                                                            <span class="badge bg-info ms-2">{{ $data['count'] }}</span>
                                                        </div>

                                                        @if(isset($data['transfer_by']))
                                                            @foreach($data['transfer_by'] as $transferBy => $transferByCount)
                                                                <div class="small text-muted">
                                                                    <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder, 'transfer_by' => $transferBy]) }}"
                                                                       class="text-decoration-none">
                                                                        <i class="fas fa-user me-1"></i>
                                                                        {{ $transferBy }}
                                                                    </a>
                                                                    <span class="badge bg-secondary ms-1">{{ $transferByCount }}</span>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                    @php $foldercount++; @endphp
                                                @endforeach
                                            </div>
                                        </td>
                                        <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold text-primary">
                                                <i class="fas fa-file-alt me-1"></i>
                                                Regular: {{ $daruliftaTotalCounts }}
                                            </span>
                                            @if(isset($talaqFatawaByDarulifta[$daruliftaName]))
                                                <span class="fw-bold text-danger">
                                                    <i class="fas fa-heart-broken me-1"></i>
                                                    Talaq: {{ $talaqFatawaByDarulifta[$daruliftaName] }}
                                                </span>
                                            @endif
                                            <span class="fw-bold text-success">
                                                <i class="fas fa-calculator me-1"></i>
                                                Total: {{ $daruliftaTotalCounts + ($talaqFatawaByDarulifta[$daruliftaName] ?? 0) }}
                                            </span>
                                            <span class="text-muted small">
                                                <i class="fas fa-folder me-1"></i>
                                                Folders: {{ $foldercount }}
                                            </span>
                                        </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        @endforeach
                    @else
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($remainingFatawa[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0;
                                    $folderCounts = [];
                                    $transferByCounts = [];
                                @endphp

                                @foreach($mailfolderDate as $mailfolderDates)
                                    @if(isset($remainingFatawa[$daruliftaName][$mailfolderDates]))
                                        @php
                                            $remainingFatawaCollection = is_array($remainingFatawa[$daruliftaName][$mailfolderDates])
                                                ? collect($remainingFatawa[$daruliftaName][$mailfolderDates])
                                                : $remainingFatawa[$daruliftaName][$mailfolderDates];
                                        @endphp
                                        @foreach($remainingFatawaCollection as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy] = isset($transferByCounts[$folder][$transferBy]) ? $transferByCounts[$folder][$transferBy] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach

                                <tr>
                                    <td>
                                        @php
                                            $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                        @endphp
                                        <a href="{{ route('sending-fatawa', [
                                            'darulifta' => $daruliftaName,
                                            'selectedmujeeb' => $this->selectedmujeeb,
                                            'selectedmufti' => $this->selectedmufti,
                                            'selectedTimeFrame' => $this->selectedTimeFrame,
                                            'startDate' => $tempStartDate,
                                            'endDate' => $tempEndDate,
                                        ]) }}&{{ $selectedMonthsQuery }}"
                                           class="text-decoration-none fw-bold text-primary">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            {{ $daruliftaName }}
                                        </a>
                                    </td>
                                    <td>
                                        <div class="folder-entries-modern">
                                            @php $foldercount = 0; @endphp
                                            @foreach ($folderCounts as $folder => $count)
                                                <div class="folder-entry-modern">
                                                    <div class="folder-date-modern">
                                                        <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder]) }}"
                                                           class="text-decoration-none">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                        </a>
                                                        <span class="badge bg-info ms-2">{{ $count }}</span>
                                                    </div>

                                                    @if(isset($transferByCounts[$folder]))
                                                        @foreach($transferByCounts[$folder] as $transferBy => $transferByCount)
                                                            <div class="small text-muted">
                                                                <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder, 'transfer_by' => $transferBy]) }}"
                                                                   class="text-decoration-none">
                                                                    <i class="fas fa-user me-1"></i>
                                                                    {{ $transferBy }}
                                                                </a>
                                                                <span class="badge bg-secondary ms-1">{{ $transferByCount }}</span>
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                                @php $foldercount++; @endphp
                                            @endforeach
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold text-primary">
                                                <i class="fas fa-file-alt me-1"></i>
                                                Regular: {{ $daruliftaTotalCounts }}
                                            </span>
                                            @if(isset($talaqFatawaByDarulifta[$daruliftaName]))
                                                <span class="fw-bold text-danger">
                                                    <i class="fas fa-heart-broken me-1"></i>
                                                    Talaq: {{ $talaqFatawaByDarulifta[$daruliftaName] }}
                                                </span>
                                            @endif
                                            <span class="fw-bold text-success">
                                                <i class="fas fa-calculator me-1"></i>
                                                Total: {{ $daruliftaTotalCounts + ($talaqFatawaByDarulifta[$daruliftaName] ?? 0) }}
                                            </span>
                                            <span class="text-muted small">
                                                <i class="fas fa-folder me-1"></i>
                                                Folders: {{ $foldercount }}
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Overall Summary -->
        <div class="mt-4 p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-md-6">
                    <h5 class="text-primary mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        <span id="finalTotalFatawa">{{ $totalCounts ?? 0 }}</span>
                    </h5>
                    <small class="text-muted">Total Fatawa</small>
                </div>
                <div class="col-md-6">
                    <h5 class="text-success mb-0">
                        <i class="fas fa-folder me-2"></i>
                        <span id="finalTotalFolders">{{ $overallFolderCount ?? 0 }}</span>
                    </h5>
                    <small class="text-muted">Total Folders</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Talaq Remaining Fatawa Summary -->
@if(count($talaqFatawaList) > 0)
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-heart-broken me-2"></i>
            Talaq Remaining Fatawa Summary
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-3">
            @foreach($talaqFatawaList as $index => $talaqFatwa)
            <div class="col-md-6 col-lg-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-3">
                        <!-- Fatwa Code - Clickable -->
                        <div class="mb-2">
                            <a href="{{ route('talaq-fatwa-edit', $talaqFatwa->talaq_fatawa_id) }}" 
                               class="text-decoration-none fw-bold fs-5 text-primary" 
                               target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>
                                {{ $talaqFatwa->file_code }}
                            </a>
                        </div>
                        
                        <!-- Mujeeb Name -->
                        <div class="mb-1">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                {{ $talaqFatwa->sender }}
                            </small>
                        </div>
                        
                        <!-- Date -->
                        <div class="mb-2">
                            <small class="text-success">
                                <i class="fas fa-calendar me-1"></i>
                                {{ \Carbon\Carbon::parse($talaqFatwa->mail_recived_date)->format('d-m-Y') }}
                            </small>
                        </div>
                        
                        <!-- Darulifta Badge -->
                        <div>
                            <span class="badge bg-info small">{{ $talaqFatwa->darulifta_name }}</span>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- Show More Link -->
        <div class="text-center mt-3">
            <a href="{{ route('talaq-remaining') }}" class="btn btn-outline-primary">
                <i class="fas fa-eye me-2"></i>
                View All Talaq Fatawa ({{ $talaqFatawaCount ?? 0 }})
            </a>
        </div>
    </div>
</div>
@endif
