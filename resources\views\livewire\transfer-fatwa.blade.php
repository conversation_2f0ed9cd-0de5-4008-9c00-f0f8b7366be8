<div class="main-content">
<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="mahlenazar-fatawa"></x-navbars.sidebar>

    <!-- Modern Styles -->
    <style>
        /* Fix for sidebar overlap */
        .main-content {
            margin-left: 250px; /* Adjust this value based on your sidebar width */
            transition: margin-left 0.3s ease;
        }

        @media (max-width: 1199.98px) {
            .main-content {
                margin-left: 0;
            }
        }

        .modern-card {
            background: #ffffff;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .modern-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            border: none;
        }

        .modern-card-body {
            padding: 2rem;
        }

        .fatwa-details-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 140px;
        }

        .detail-value {
            color: #212529;
            font-weight: 500;
        }

        .transfer-form-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 1.5rem;
            border: 2px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .modern-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        .modern-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        .btn-modern {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border: none;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-modern-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-modern-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-modern-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-modern-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }

        .btn-modern-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-modern-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .alert-modern {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
        }

        .alert-modern.alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .alert-modern.alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .mujeeb-option {
            padding: 0.5rem 0;
        }

        .form-label-modern {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            display: block;
        }

        /* Additional responsive fixes */
        .position-relative.max-height-vh-100.h-100.border-radius-lg {
            margin-left: 0 !important;
            width: 100% !important;
            padding-left: 0 !important;
        }
    </style>

    <main class="position-relative max-height-vh-100 h-100 border-radius-lg">
        @livewire('navbar', ['titlePage' => 'Transfer Fatwa'])

        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-0 text-white">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        Transfer Fatwa
                                    </h4>
                                    <p class="mb-0 opacity-75">Transfer fatwa to another Mujeeb</p>
                                </div>
                            </div>
                        </div>
                        <div class="modern-card-body">
                            @if($fatwa)
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="fatwa-details-card">
                                            <h5 class="mb-3" style="color: #495057; font-weight: 600;">
                                                <i class="fas fa-file-alt me-2"></i>
                                                Fatwa Details
                                            </h5>

                                            <div class="detail-row">
                                                <span class="detail-label">
                                                    <i class="fas fa-barcode me-1"></i>
                                                    File Code:
                                                </span>
                                                <span class="detail-value">{{ $fatwa->file_code }}</span>
                                            </div>

                                            <div class="detail-row">
                                                <span class="detail-label">
                                                    <i class="fas fa-user me-1"></i>
                                                    Current Sender:
                                                </span>
                                                <span class="detail-value">{{ $fatwa->sender }}</span>
                                            </div>

                                            <div class="detail-row">
                                                <span class="detail-label">
                                                    <i class="fas fa-university me-1"></i>
                                                    Darulifta:
                                                </span>
                                                <span class="detail-value">{{ $fatwa->darulifta_name }}</span>
                                            </div>

                                            <div class="detail-row">
                                                <span class="detail-label">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Mail Folder Date:
                                                </span>
                                                <span class="detail-value">{{ $fatwa->mail_folder_date }}</span>
                                            </div>

                                            <div class="detail-row">
                                                <span class="detail-label">
                                                    <i class="fas fa-tags me-1"></i>
                                                    Category:
                                                </span>
                                                <span class="detail-value">{{ $fatwa->category }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="transfer-form-card">
                                            <h5 class="mb-3" style="color: #495057; font-weight: 600;">
                                                <i class="fas fa-paper-plane me-2"></i>
                                                Transfer to New Sender
                                            </h5>

                                            <form wire:submit.prevent="transferFatwa">
                                                @if($successMessage)
                                                    <div class="alert-modern alert-success">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        {{ $successMessage }}
                                                    </div>
                                                @endif

                                                @if($errorMessage)
                                                    <div class="alert-modern alert-danger">
                                                        <i class="fas fa-exclamation-circle me-2"></i>
                                                        {{ $errorMessage }}
                                                    </div>
                                                @endif

                                                <div class="form-group mb-4">
                                                    <label for="newSender" class="form-label-modern">
                                                        <i class="fas fa-user-plus me-1"></i>
                                                        Select New Sender
                                                    </label>
                                                    <select class="modern-select w-100 @error('newSender') is-invalid @enderror"
                                                            id="newSender"
                                                            wire:model="newSender">
                                                        <option value="">-- Select Mujeeb --</option>
                                                        @foreach($mujeebs as $mujeeb)
                                                            <option value="{{ $mujeeb->mujeeb_name }}" class="mujeeb-option">
                                                                {{ $mujeeb->mujeeb_name }}
                                                                ({{ $mujeeb->darul_name }})
                                                                @if($mujeeb->munsab) - {{ $mujeeb->munsab }} @endif
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('newSender')
                                                        <div class="invalid-feedback d-block mt-2" style="color: #dc3545;">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                                            {{ $message }}
                                                        </div>
                                                    @enderror
                                                </div>

                                                <div class="d-flex justify-content-between gap-3">
                                                    <a href="{{ route('mahlenazar-fatawa') }}" class="btn-modern btn-modern-secondary">
                                                        <i class="fas fa-arrow-left me-1"></i>
                                                        Cancel
                                                    </a>
                                                    <button type="submit" class="btn-modern btn-modern-success">
                                                        <i class="fas fa-exchange-alt me-1"></i>
                                                        Transfer Fatwa
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="alert-modern alert-danger text-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    {{ $errorMessage ?? 'Fatwa not found.' }}
                                </div>
                                <div class="text-center mt-4">
                                    <a href="{{ route('mahlenazar-fatawa') }}" class="btn-modern btn-modern-primary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Back to Fatwa List
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
</div>