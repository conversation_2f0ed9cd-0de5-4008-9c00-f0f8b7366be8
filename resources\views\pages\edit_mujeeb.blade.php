<x-layout bodyClass="g-sidenav-show bg-gray-100">
    <x-navbars.sidebar activePage="mujeeb"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Edit Mujeeb"></x-navbars.navs.auth>
        <!-- End Navbar -->
        
        <style>
            :root {
                --primary-color: #059669;
                --primary-hover: #047857;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --danger-hover: #dc2626;
                --gray-50: #f9fafb;
                --gray-100: #f3f4f6;
                --gray-200: #e5e7eb;
                --gray-300: #d1d5db;
                --gray-600: #4b5563;
                --gray-700: #374151;
                --gray-800: #1f2937;
                --gray-900: #111827;
            }

            .modern-container {
                padding: 2rem;
                max-width: 800px;
                margin: 0 auto;
            }

            .page-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, #10b981 100%);
                color: white !important;
                padding: 2rem;
                border-radius: 1rem;
                margin-bottom: 2rem;
                box-shadow: 0 10px 25px rgba(5, 150, 105, 0.2);
            }

            .page-header * {
                color: white !important;
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                color: white !important;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .page-title svg {
                color: white !important;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            }

            .page-subtitle {
                font-size: 1.1rem;
                color: white !important;
                opacity: 0.95;
                margin-top: 0.5rem;
                margin-bottom: 0;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            }

            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1rem;
                font-size: 0.875rem;
            }

            .breadcrumb a {
                color: white !important;
                text-decoration: none;
                opacity: 1;
                transition: all 0.2s;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.1);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .breadcrumb a:hover {
                opacity: 1;
                text-decoration: none;
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
            }

            .breadcrumb span {
                color: white !important;
                opacity: 1;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                font-weight: 600;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.25rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid rgba(255, 255, 255, 0.25);
            }

            .breadcrumb svg {
                color: white !important;
                opacity: 0.8;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            }

            .form-card {
                background: white;
                border-radius: 1rem;
                padding: 2.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 1px solid var(--gray-200);
                margin-bottom: 2rem;
            }

            .form-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-icon {
                width: 24px;
                height: 24px;
                color: var(--primary-color);
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                font-weight: 600;
                color: var(--gray-700);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .form-input, .form-select {
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.75rem;
                font-size: 1rem;
                transition: all 0.2s ease;
                background: var(--gray-50);
            }

            .form-input:focus, .form-select:focus {
                outline: none;
                border-color: var(--primary-color);
                background: white;
                box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
            }

            .current-value {
                background: #ecfdf5;
                border: 2px solid var(--primary-color);
                color: var(--gray-800);
            }

            .form-select {
                cursor: pointer;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 0.5rem center;
                background-repeat: no-repeat;
                background-size: 1.5em 1.5em;
                padding-right: 2.5rem;
            }

            .btn-primary {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #10b981 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
            }

            .danger-zone {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
                border: 2px solid #fecaca;
            }

            .danger-title {
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--danger-color);
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .danger-description {
                color: var(--gray-600);
                margin-bottom: 1.5rem;
                line-height: 1.6;
            }

            .btn-danger {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .btn-danger:hover {
                background: linear-gradient(135deg, var(--danger-hover) 0%, var(--danger-color) 100%);
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
            }

            .back-link {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                color: white !important;
                text-decoration: none;
                font-weight: 600;
                margin-bottom: 2rem;
                padding: 0.875rem 1.75rem;
                background: linear-gradient(135deg, var(--primary-color) 0%, #10b981 100%);
                border-radius: 0.75rem;
                box-shadow: 0 4px 12px rgba(5, 150, 105, 0.25);
                transition: all 0.2s ease;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }

            .back-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
                color: white !important;
                text-decoration: none;
                background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .back-link svg {
                color: white !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
            }

            .current-info {
                background: #ecfdf5;
                border: 1px solid #10b981;
                border-radius: 0.75rem;
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .current-info-title {
                font-weight: 600;
                color: var(--primary-color);
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .current-info-content {
                color: var(--gray-700);
                font-size: 0.875rem;
            }

            .alert {
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                margin-bottom: 1.5rem;
                border: 1px solid;
            }

            .alert-success {
                background: #ecfdf5;
                border-color: #10b981;
                color: #065f46;
            }

            .alert-error {
                background: #fef2f2;
                border-color: #ef4444;
                color: #991b1b;
            }

            @media (max-width: 768px) {
                .modern-container {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .page-title {
                    font-size: 2rem;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 0.5rem;
                }
                
                .form-card, .danger-zone {
                    padding: 1.5rem;
                }
            }
        </style>

        <div class="modern-container">
            <!-- Back Link -->
            <a href="{{ route('mujeeb') }}" class="back-link">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Mujeeb List
            </a>

            <!-- Page Header -->
            <div class="page-header">
                <div class="breadcrumb">
                    <a href="{{ route('mujeeb') }}">Mujeeb Management</a>
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                    <span>Edit Scholar</span>
                </div>
                <h1 class="page-title">
                    <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Edit Mujeeb
                </h1>
                <p class="page-subtitle">Update information for "{{ $upmujeeb->mujeeb_name }}"</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success">
                    <strong>Success!</strong> {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error">
                    <strong>Error!</strong> {{ session('error') }}
                </div>
            @endif

            <!-- Current Information Display -->
            <div class="current-info">
                <div class="current-info-title">Current Information</div>
                <div class="current-info-content">
                    <strong>Name:</strong> {{ $upmujeeb->mujeeb_name }} | 
                    <strong>Institution:</strong> {{ $upmujeeb->darul_name }} | 
                    <strong>Position:</strong> {{ $upmujeeb->munsab }}
                </div>
            </div>

            <!-- Edit Form -->
            <div class="form-card">
                <h2 class="form-title">
                    <svg class="form-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Update Scholar Information
                </h2>
                
                <form action="{{ route('mujeeb.update', ['id' => $upmujeeb->id]) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="form-group">
                        <label for="mujeeb_name" class="form-label">Scholar Name</label>
                        <input type="text" 
                               class="form-input current-value" 
                               id="mujeeb_name" 
                               name="mujeeb_name" 
                               value="{{ $upmujeeb->mujeeb_name }}"
                               placeholder="Enter scholar's full name"
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label for="darul_name" class="form-label">Darulifta Institution</label>
                        <select class="form-select current-value" id="darul_name" name="darul_name" required>
                            <option value="">Select Institution</option>
                            @foreach($daruliftas as $darulifta)
                                <option value="{{ $darulifta->darul_name }}" 
                                        @if($darulifta->darul_name == $upmujeeb->darul_name) selected @endif>
                                    {{ $darulifta->darul_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="munsab" class="form-label">Position (Munsab)</label>
                        <select class="form-select current-value" id="munsab" name="munsab" required>
                            <option value="">Select Position</option>
                            <option value="Mufti" @if($upmujeeb->munsab == 'Mufti') selected @endif>Mufti</option>
                            <option value="Mutakhassis" @if($upmujeeb->munsab == 'Mutakhassis') selected @endif>Mutakhassis</option>
                            <option value="Muawin" @if($upmujeeb->munsab == 'Muawin') selected @endif>Muawin</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Update Mujeeb
                    </button>
                </form>
            </div>

            <!-- Danger Zone -->
            <div class="danger-zone">
                <h3 class="danger-title">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    Danger Zone
                </h3>
                <p class="danger-description">
                    Once you delete this Mujeeb, there is no going back. This action will permanently remove 
                    "{{ $upmujeeb->mujeeb_name }}" from {{ $upmujeeb->darul_name }} and all associated data. 
                    Please be certain before proceeding.
                </p>
                
                <form action="{{ route('mujeeb.delete', ['id' => $upmujeeb->id]) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="btn-danger" 
                            onclick="return confirm('⚠️ FINAL WARNING ⚠️\n\nYou are about to permanently delete:\n\nName: {{ $upmujeeb->mujeeb_name }}\nInstitution: {{ $upmujeeb->darul_name }}\nPosition: {{ $upmujeeb->munsab }}\n\nThis action CANNOT be undone!\n\nAre you absolutely sure?')">
                        <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                        Delete Mujeeb Permanently
                    </button>
                </form>
            </div>
        </div>
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
