<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\DailyPerformance;
use App\Models\User;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class SuperiorPerformanceReview extends Component
{
    use WithPagination, AuthorizesRequests;

    public $selectedDate;
    public $selectedUser = 'all';
    public $selectedStatus = 'all';
    
    public $showRatingModal = false;
    public $selectedPerformance = null;
    public $superiorRating = '';
    public $superiorComments = '';
    
    public $subordinateUsers = [];

    public function mount()
    {
        $this->authorize('view-all-performance');
        $this->selectedDate = Carbon::today()->format('Y-m-d');
        $this->loadSubordinateUsers();
    }

    public function render()
    {
        $performances = $this->getPerformances();
        
        return view('livewire.superior-performance-review', [
            'performances' => $performances,
        ]);
    }

    public function loadSubordinateUsers()
    {
        $user = auth()->user();
        
        if ($user->isSuperior()) {
            // Get assistants and department members
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->departments->flatMap->users->pluck('id');
            $userIds = $assistantIds->merge($departmentUserIds)->unique();
            
            $this->subordinateUsers = User::whereIn('id', $userIds)
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();
        } elseif ($user->isNazim()) {
            // Nazim can see all users
            $this->subordinateUsers = User::select('id', 'name', 'email')
                ->orderBy('name')
                ->get();
        }
    }

    public function getPerformances()
    {
        $query = DailyPerformance::with(['user', 'task', 'department', 'ratedBy'])
            ->where('is_submitted', true);

        // Apply date filter
        if ($this->selectedDate) {
            $query->whereDate('performance_date', $this->selectedDate);
        }

        // Apply user filter
        if ($this->selectedUser !== 'all') {
            $query->where('user_id', $this->selectedUser);
        } else {
            // Only show subordinates' performance
            $userIds = $this->subordinateUsers->pluck('id');
            $query->whereIn('user_id', $userIds);
        }

        // Apply status filter
        if ($this->selectedStatus === 'rated') {
            $query->whereNotNull('superior_rating');
        } elseif ($this->selectedStatus === 'unrated') {
            $query->whereNull('superior_rating');
        }

        return $query->orderBy('performance_date', 'desc')
                    ->orderBy('submitted_at', 'desc')
                    ->paginate(15);
    }

    public function openRatingModal($performanceId)
    {
        $this->selectedPerformance = DailyPerformance::with(['user', 'task', 'department'])
            ->findOrFail($performanceId);
        
        // Load existing rating if any
        $this->superiorRating = $this->selectedPerformance->superior_rating ?? '';
        $this->superiorComments = $this->selectedPerformance->superior_comments ?? '';
        
        $this->showRatingModal = true;
    }

    public function closeRatingModal()
    {
        $this->showRatingModal = false;
        $this->selectedPerformance = null;
        $this->superiorRating = '';
        $this->superiorComments = '';
    }

    public function submitRating()
    {
        $this->validate([
            'superiorRating' => 'required|in:poor,fair,good,excellent',
            'superiorComments' => 'nullable|string|max:1000',
        ]);

        $this->selectedPerformance->update([
            'superior_rating' => $this->superiorRating,
            'superior_comments' => $this->superiorComments,
            'rated_by' => auth()->id(),
            'rated_at' => now(),
        ]);

        session()->flash('message', 'Performance rating submitted successfully.');
        $this->closeRatingModal();
    }

    public function removeRating($performanceId)
    {
        $performance = DailyPerformance::findOrFail($performanceId);
        
        $performance->update([
            'superior_rating' => null,
            'superior_comments' => null,
            'rated_by' => null,
            'rated_at' => null,
        ]);

        session()->flash('message', 'Performance rating removed successfully.');
    }

    public function getRatingColor($rating)
    {
        return match($rating) {
            'excellent' => 'success',
            'good' => 'info',
            'fair' => 'warning',
            'poor' => 'danger',
            default => 'secondary'
        };
    }

    public function getStatusColor($hasRating)
    {
        return $hasRating ? 'success' : 'warning';
    }
}
