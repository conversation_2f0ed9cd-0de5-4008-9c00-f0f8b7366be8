{{-- Sent For Checking Display Options Partial --}}

<div class="card-modern">
    <div class="card-header-modern">
        <h5 class="mb-0">
            <i class="fas fa-cog me-2"></i>
            Display Options
        </h5>
    </div>
    <div class="card-body-modern">
        <form method="GET" action="{{ route('sent-for-checking', [
            'darulifta' => request()->route('darulifta'),
            'mailfolder' => request()->route('mailfolder')
        ]) }}">
            <!-- Preserve filter parameters -->
            <input type="hidden" name="selectedmujeeb" value="{{ request('selectedmujeeb', 'all') }}">
            <input type="hidden" name="selectedmufti" value="{{ request('selectedmufti', 'all') }}">
            <input type="hidden" name="selectedexclude" value="{{ request('selectedexclude', 'exclude_checked') }}">
            <input type="hidden" name="selectedTimeFrame" value="{{ request('selectedTimeFrame', 'this_month') }}">
            <input type="hidden" name="startDate" value="{{ request('startDate') }}">
            <input type="hidden" name="endDate" value="{{ request('endDate') }}">
            @if(request('selectedMonths'))
                @foreach((array)request('selectedMonths') as $month)
                    <input type="hidden" name="selectedMonths[]" value="{{ $month }}">
                @endforeach
            @endif

            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="showDetail" name="showDetail" 
                               value="1" {{ request('showDetail') == '1' ? 'checked' : '' }}
                               onchange="this.form.submit()">
                        <label class="form-check-label" for="showDetail">
                            <i class="fas fa-list-ul me-1"></i>
                            Show Detailed View
                        </label>
                    </div>
                    <small class="text-muted">Display detailed fatawa information in expanded format</small>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="showQue" name="showQue" 
                               value="1" {{ request('showQue') == '1' ? 'checked' : '' }}
                               onchange="this.form.submit()">
                        <label class="form-check-label" for="showQue">
                            <i class="fas fa-question-circle me-1"></i>
                            Show Questions
                        </label>
                    </div>
                    <small class="text-muted">Display related questions and queries</small>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="showChat" name="showChat" 
                               value="1" {{ request('showChat') == '1' ? 'checked' : '' }}
                               onchange="this.form.submit()">
                        <label class="form-check-label" for="showChat">
                            <i class="fas fa-comments me-1"></i>
                            Show Chat
                        </label>
                    </div>
                    <small class="text-muted">Enable chat functionality for fatawa discussions</small>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <button type="submit" class="btn-modern btn-primary-modern">
                            <i class="fas fa-sync-alt me-2"></i>
                            Apply Options
                        </button>
                        
                        <a href="{{ route('sent-for-checking', [
                            'darulifta' => request()->route('darulifta'),
                            'mailfolder' => request()->route('mailfolder')
                        ]) }}" class="btn-modern btn-secondary-modern">
                            <i class="fas fa-undo me-2"></i>
                            Reset Options
                        </a>
                        
                        <button type="button" class="btn-modern btn-info-modern" onclick="toggleFullscreen()">
                            <i class="fas fa-expand me-2"></i>
                            Fullscreen
                        </button>
                        
                        <button type="button" class="btn-modern btn-success-modern" onclick="exportTableData()">
                            <i class="fas fa-file-excel me-2"></i>
                            Export Excel
                        </button>
                        
                        <button type="button" class="btn-modern btn-danger-modern" onclick="printTable()">
                            <i class="fas fa-print me-2"></i>
                            Print
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Display Status -->
@if(request('showDetail') == '1' || request('showQue') == '1' || request('showChat') == '1')
    <div class="alert alert-info d-flex align-items-center" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <div>
            <strong>Active Display Options:</strong>
            @if(request('showDetail') == '1')
                <span class="badge bg-primary ms-1">Detailed View</span>
            @endif
            @if(request('showQue') == '1')
                <span class="badge bg-success ms-1">Questions</span>
            @endif
            @if(request('showChat') == '1')
                <span class="badge bg-warning ms-1">Chat</span>
            @endif
        </div>
    </div>
@endif

<script>
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

function exportTableData() {
    // Implementation for Excel export
    alert('Excel export functionality will be implemented');
}

function printTable() {
    window.print();
}

function refreshData() {
    location.reload();
}
</script>
