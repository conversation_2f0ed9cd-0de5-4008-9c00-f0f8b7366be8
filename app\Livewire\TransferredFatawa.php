<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class TransferredFatawa extends Component
{
    use WithPagination;

    public $search = '';
    public $editFatwaId = null;
    public $newSender = '';
    public $mujeebs = [];
    public $confirmingDelete = false;
    public $deleteFatwaId = null;
    public $successMessage = '';
    public $errorMessage = '';

    protected $listeners = ['refresh' => '$refresh'];

    protected $queryString = [
        'search' => ['except' => '']
    ];

    public function mount()
    {
        // Check if user is admin, mujeeb, or has Darulifta role
        $userRoles = Auth::user()->roles->pluck('name')->toArray();
        $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
        $hasDaruliftaRole = !empty(array_intersect($userRoles, $daruliftaRoles));
        
        if (!Auth::check() || (!in_array('Admin', $userRoles) && !Auth::user()->isMujeeb() && !$hasDaruliftaRole)) {
            return redirect()->route('dashboard');
        }

        $this->loadMujeebs();
    }

    private function loadMujeebs()
    {
        // Get all distinct senders from uploaded_files table
        $this->mujeebs = DB::table('uploaded_files')
            ->select('sender')
            ->distinct()
            ->whereNotNull('sender')
            ->where('sender', '!=', '')
            ->orderBy('sender')
            ->pluck('sender')
            ->toArray();
    }

    public function startEdit($fatwaId)
    {
        $this->editFatwaId = $fatwaId;
        $fatwa = DB::table('uploaded_files')->where('id', $fatwaId)->first();
        if ($fatwa) {
            $this->newSender = $fatwa->sender;
        }
    }

    public function cancelEdit()
    {
        $this->editFatwaId = null;
        $this->newSender = '';
        $this->resetValidation();
    }

    public function updateFatwa()
    {
        $this->validate([
            'newSender' => 'required|string',
        ], [
            'newSender.required' => 'Please select a sender.'
        ]);

        try {
            // Get the fatwa record first
            $fatwa = DB::table('uploaded_files')->where('id', $this->editFatwaId)->first();
            
            if (!$fatwa) {
                $this->errorMessage = 'Fatwa not found.';
                return;
            }
            
            // Make sure file_created_date is set if not already available
            $fileCreatedDate = $fatwa->file_created_date ?? now()->toDateString();
            
            DB::table('uploaded_files')
                ->where('id', $this->editFatwaId)
                ->update([
                    'sender' => $this->newSender,
                    'file_created_date' => $fileCreatedDate,
                    'updated_at' => now()
                ]);

            $this->successMessage = 'Fatwa updated successfully.';
            $this->editFatwaId = null;
            $this->newSender = '';
        } catch (\Exception $e) {
            $this->errorMessage = 'Error updating fatwa: ' . $e->getMessage();
        }
    }

    public function confirmDelete($fatwaId)
    {
        $this->confirmingDelete = true;
        $this->deleteFatwaId = $fatwaId;
    }

    public function cancelDelete()
    {
        $this->confirmingDelete = false;
        $this->deleteFatwaId = null;
    }

    public function deleteFatwa()
    {
        try {
            DB::table('uploaded_files')
                ->where('id', $this->deleteFatwaId)
                ->delete();

            $this->successMessage = 'Fatwa deleted successfully.';
            $this->confirmingDelete = false;
            $this->deleteFatwaId = null;
        } catch (\Exception $e) {
            $this->errorMessage = 'Error deleting fatwa: ' . $e->getMessage();
        }
    }

    public function render()
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();
        $isAdmin = in_array('Admin', $userRoles);
        
        // Check if user has a Darulifta role
        $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
        $userDaruliftaRole = array_intersect($userRoles, $daruliftaRoles);
        $hasDaruliftaRole = !empty($userDaruliftaRole);
        
        $query = DB::table('uploaded_files')
            ->where('checked_folder', 'Assigned');
            
        // Apply role-based filtering
        if (!$isAdmin) {
            if ($hasDaruliftaRole) {
                // User has Darulifta role - show fatawa where sender or transfer_by mujeeb belongs to their Darulifta
                $userDarulifta = reset($userDaruliftaRole); // Get the first (should be only one) Darulifta role
                
                // Get all mujeebs that belong to this Darulifta
                $daruliftaMujeebs = DB::table('mujeebs')
                    ->where('darul_name', $userDarulifta)
                    ->pluck('mujeeb_name')
                    ->toArray();
                
                if (!empty($daruliftaMujeebs)) {
                    $query->where(function ($q) use ($daruliftaMujeebs) {
                        $q->whereIn('sender', $daruliftaMujeebs)
                          ->orWhereIn('transfer_by', $daruliftaMujeebs);
                    });
                } else {
                    // No mujeebs found for this Darulifta, show no results
                    $query->where('id', -1);
                }
            } elseif ($user->isMujeeb()) {
                // User is mujeeb - show only their transferred fatawa
                $query->where(function ($q) use ($user) {
                    $q->where('sender', $user->name)
                      ->orWhere('transfer_by', $user->name);
                });
            } else {
                // User has no relevant role, show no results
                $query->where('id', -1);
            }
        }
        
        $query->when($this->search, function ($query) {
                return $query->where(function ($query) {
                    $query->where('file_code', 'like', '%' . $this->search . '%')
                        ->orWhere('sender', 'like', '%' . $this->search . '%')
                        ->orWhere('darulifta_name', 'like', '%' . $this->search . '%')
                        ->orWhere('category', 'like', '%' . $this->search . '%');
                });
            })
            ->select('uploaded_files.*')
            ->orderBy('updated_at', 'desc');

        $transferredFatawa = $query->paginate(20);
        
        // Get mahlenazar_null data for Mail Folder Dates trail
        $mahlenazar_null = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select('file_code', 'mail_folder_date', 'sender', 'file_name', 'checker', 'by_mufti', 'darulifta_name')
            ->get();

        return view('livewire.transferred-fatawa', [
            'transferredFatawa' => $transferredFatawa,
            'mahlenazar_null' => $mahlenazar_null
        ])->layout('layouts.app');
    }
}
