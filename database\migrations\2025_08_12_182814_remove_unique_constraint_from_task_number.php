<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('workflow_tasks', function (Blueprint $table) {
            // Remove unique constraint from task_number to allow grouped tasks
            $table->dropUnique(['task_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workflow_tasks', function (Blueprint $table) {
            // Add back unique constraint
            $table->unique('task_number');
        });
    }
};
