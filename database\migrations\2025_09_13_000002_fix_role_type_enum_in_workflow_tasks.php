<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the role_type enum to include 'direct' for operational tasks
        DB::statement("ALTER TABLE workflow_tasks MODIFY COLUMN role_type ENUM('superior', 'assistant', 'direct') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'direct' from the role_type enum
        DB::statement("ALTER TABLE workflow_tasks MODIFY COLUMN role_type ENUM('superior', 'assistant') NULL");
    }
};
