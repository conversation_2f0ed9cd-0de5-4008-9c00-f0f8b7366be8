<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="unlock-history"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Lock/Unlock System Statistics"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <div class="container-fluid py-4">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header pb-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                                        System-wide Lock/Unlock Statistics
                                    </h6>
                                    <p class="text-sm mb-0 text-secondary">Overview of all account locks and unlocks</p>
                                </div>
                                <a href="{{ route('unlock-history.index') }}" class="btn btn-sm bg-gradient-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to History
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Locks</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['total_locks'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                                        <i class="fas fa-lock text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Active Locks</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['active_locks'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                                        <i class="fas fa-user-lock text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 mb-xl-0 mb-4">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Unlocks</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['total_unlocks'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                                        <i class="fas fa-unlock text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Unlock Requests</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['total_requests'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                                        <i class="fas fa-paper-plane text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xl-4 col-md-6 mb-xl-0 mb-4">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Manual Unlocks</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['manual_unlocks'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                                        <i class="fas fa-user-shield text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-4 col-md-6 mb-xl-0 mb-4">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">System Unlocks</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['system_unlocks'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                                        <i class="fas fa-robot text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-4 col-md-6">
                                    <div class="card card-stats mb-4">
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div class="numbers">
                                                        <p class="text-sm mb-0 text-capitalize font-weight-bold">Pending Requests</p>
                                                        <h5 class="font-weight-bolder mb-0">{{ $stats['pending_requests'] }}</h5>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-end">
                                                    <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                                        <i class="fas fa-clock text-lg opacity-10"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row mb-4">
                <div class="col-lg-7 mb-lg-0 mb-4">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line me-2 text-primary"></i>
                                Monthly Lock Statistics
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Account locks by month</p>
                        </div>
                        <div class="card-body p-3">
                            <div class="chart">
                                <canvas id="monthly-chart" class="chart-canvas" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2 text-primary"></i>
                                Restriction Types
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Distribution of lock reasons</p>
                        </div>
                        <div class="card-body p-3">
                            <div class="chart">
                                <canvas id="restriction-types-chart" class="chart-canvas" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Request Statistics -->
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header pb-0">
                            <h6 class="mb-0">
                                <i class="fas fa-paper-plane me-2 text-primary"></i>
                                Unlock Request Statistics
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Overview of unlock requests and their outcomes</p>
                        </div>
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-md-4 mb-md-0 mb-4">
                                    <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
                                        <div class="icon icon-shape icon-sm bg-gradient-success shadow text-center me-3">
                                            <i class="fas fa-check-circle opacity-10"></i>
                                        </div>
                                        <div class="d-flex flex-column">
                                            <h6 class="mb-1 text-dark text-sm">Approved Requests</h6>
                                            <span class="text-xs">{{ $stats['approved_requests'] }} requests ({{ $stats['total_requests'] > 0 ? round(($stats['approved_requests'] / $stats['total_requests']) * 100) : 0 }}%)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-md-0 mb-4">
                                    <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
                                        <div class="icon icon-shape icon-sm bg-gradient-warning shadow text-center me-3">
                                            <i class="fas fa-times-circle opacity-10"></i>
                                        </div>
                                        <div class="d-flex flex-column">
                                            <h6 class="mb-1 text-dark text-sm">Dismissed Requests</h6>
                                            <span class="text-xs">{{ $stats['dismissed_requests'] }} requests ({{ $stats['total_requests'] > 0 ? round(($stats['dismissed_requests'] / $stats['total_requests']) * 100) : 0 }}%)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
                                        <div class="icon icon-shape icon-sm bg-gradient-info shadow text-center me-3">
                                            <i class="fas fa-clock opacity-10"></i>
                                        </div>
                                        <div class="d-flex flex-column">
                                            <h6 class="mb-1 text-dark text-sm">Pending Requests</h6>
                                            <span class="text-xs">{{ $stats['pending_requests'] }} requests ({{ $stats['total_requests'] > 0 ? round(($stats['pending_requests'] / $stats['total_requests']) * 100) : 0 }}%)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <x-footers.auth></x-footers.auth>

    @push('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Monthly Chart
            var ctx1 = document.getElementById("monthly-chart").getContext("2d");
            
            var monthlyData = @json($monthlyStats);
            var labels = [];
            var data = [];
            
            monthlyData.forEach(function(item) {
                var monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                labels.push(monthNames[item.month - 1] + " " + item.year);
                data.push(item.count);
            });
            
            new Chart(ctx1, {
                type: "bar",
                data: {
                    labels: labels,
                    datasets: [{
                        label: "Account Locks",
                        tension: 0.4,
                        borderWidth: 0,
                        borderRadius: 4,
                        borderSkipped: false,
                        backgroundColor: "rgba(94, 114, 228, 0.8)",
                        data: data,
                        maxBarThickness: 30
                    }],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false,
                        }
                    },
                    scales: {
                        y: {
                            grid: {
                                drawBorder: false,
                                display: true,
                                drawOnChartArea: true,
                                drawTicks: false,
                                borderDash: [5, 5]
                            },
                            ticks: {
                                padding: 10,
                                font: {
                                    size: 11,
                                    family: "Open Sans",
                                    style: 'normal',
                                    lineHeight: 2
                                },
                                color: "#9ca2b7"
                            }
                        },
                        x: {
                            grid: {
                                drawBorder: false,
                                display: false,
                                drawOnChartArea: false,
                                drawTicks: false,
                                borderDash: [5, 5]
                            },
                            ticks: {
                                display: true,
                                color: '#9ca2b7',
                                padding: 10,
                                font: {
                                    size: 11,
                                    family: "Open Sans",
                                    style: 'normal',
                                    lineHeight: 2
                                },
                            }
                        },
                    },
                },
            });

            // Restriction Types Chart
            var ctx2 = document.getElementById("restriction-types-chart").getContext("2d");
            
            var restrictionData = @json($restrictionTypes);
            var typeLabels = [];
            var typeData = [];
            var backgroundColors = [
                'rgba(245, 54, 92, 0.8)',  // Red
                'rgba(251, 99, 64, 0.8)',   // Orange
                'rgba(45, 206, 137, 0.8)',  // Green
                'rgba(17, 205, 239, 0.8)',  // Blue
                'rgba(94, 114, 228, 0.8)',  // Purple
            ];
            
            restrictionData.forEach(function(item, index) {
                typeLabels.push(item.restriction_type.replace(/_/g, ' '));
                typeData.push(item.count);
            });
            
            new Chart(ctx2, {
                type: "pie",
                data: {
                    labels: typeLabels,
                    datasets: [{
                        label: "Restriction Types",
                        weight: 9,
                        cutout: 0,
                        tension: 0.9,
                        pointRadius: 2,
                        borderWidth: 2,
                        backgroundColor: backgroundColors,
                        data: typeData,
                        fill: false
                    }],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index',
                    },
                },
            });
        });
    </script>
    @endpush
</x-layout>