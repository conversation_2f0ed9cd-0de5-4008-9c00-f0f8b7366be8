{{-- Sent For Checking Navigation Partial --}}

@if(Auth::user()->hasRole('Admin') || Auth::user()->hasRole('SuperAdmin'))
    <div class="card-modern">
        <div class="card-header-modern">
            <h5 class="mb-0">
                <i class="fas fa-compass me-2"></i>
                Quick Navigation
            </h5>
        </div>
        <div class="card-body-modern">
            <div class="row">
                <!-- Darulifta Navigation -->
                <div class="col-md-6 mb-3">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-building me-1"></i>
                        Daruliftaas
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        @foreach($daruliftalist as $darul)
                            <a href="{{ route('sent-for-checking', ['darulifta' => $darul]) }}" 
                               class="btn-modern btn-outline-modern btn-sm {{ request()->route('darulifta') == $darul ? 'btn-primary-modern' : '' }}">
                                {{ $darul }}
                            </a>
                        @endforeach
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-md-6 mb-3">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-bolt me-1"></i>
                        Quick Actions
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{{ route('create') }}" class="btn-modern btn-success-modern btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            Send New Fatwa
                        </a>
                        <a href="{{ route('recived-fatawa') }}" class="btn-modern btn-info-modern btn-sm">
                            <i class="fas fa-inbox me-1"></i>
                            Received Fatawa
                        </a>
                        <a href="{{ route('remaining-fatawa') }}" class="btn-modern btn-warning-modern btn-sm">
                            <i class="fas fa-clock me-1"></i>
                            Remaining Fatawa
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn-modern btn-secondary-modern btn-sm">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Checker Navigation -->
            <div class="row">
                <div class="col-12">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-user-check me-1"></i>
                        Muftis/Checkers
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{{ request()->fullUrlWithQuery(['selectedmufti' => 'all']) }}" 
                           class="btn-modern btn-outline-modern btn-sm {{ request('selectedmufti') == 'all' ? 'btn-primary-modern' : '' }}">
                            All Muftis
                        </a>
                        @foreach($checkerlist as $checker)
                            <a href="{{ request()->fullUrlWithQuery(['selectedmufti' => $checker->folder_id]) }}" 
                               class="btn-modern btn-outline-modern btn-sm {{ request('selectedmufti') == $checker->folder_id ? 'btn-primary-modern' : '' }}">
                                {{ $checker->checker_name }}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Time Frame Navigation -->
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-calendar me-1"></i>
                        Time Periods
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{{ request()->fullUrlWithQuery(['selectedTimeFrame' => 'this_month']) }}" 
                           class="btn-modern btn-outline-modern btn-sm {{ request('selectedTimeFrame') == 'this_month' ? 'btn-primary-modern' : '' }}">
                            This Month
                        </a>
                        <a href="{{ request()->fullUrlWithQuery(['selectedTimeFrame' => 'last_month']) }}" 
                           class="btn-modern btn-outline-modern btn-sm {{ request('selectedTimeFrame') == 'last_month' ? 'btn-primary-modern' : '' }}">
                            Last Month
                        </a>
                        <a href="{{ request()->fullUrlWithQuery(['selectedTimeFrame' => 'other']) }}" 
                           class="btn-modern btn-outline-modern btn-sm {{ request('selectedTimeFrame') == 'other' ? 'btn-primary-modern' : '' }}">
                            Custom Months
                        </a>
                    </div>
                </div>
            </div>

            <!-- Breadcrumb Navigation -->
            <div class="row mt-3">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('dashboard') }}">
                                    <i class="fas fa-home me-1"></i>
                                    Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('sent-for-checking') }}">Sent For Checking</a>
                            </li>
                            @if(request()->route('darulifta'))
                                <li class="breadcrumb-item">
                                    <a href="{{ route('sent-for-checking', ['darulifta' => request()->route('darulifta')]) }}">
                                        {{ request()->route('darulifta') }}
                                    </a>
                                </li>
                            @endif
                            @if(request()->route('mailfolder'))
                                <li class="breadcrumb-item active" aria-current="page">
                                    {{ request()->route('mailfolder') }}
                                </li>
                            @endif
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
@endif

<!-- Current Filter Display -->
<div class="card-modern">
    <div class="card-body-modern py-2">
        <div class="d-flex align-items-center justify-content-between">
            <div class="current-filters">
                <small class="text-muted">Current View:</small>
                <span class="badge bg-primary ms-1">
                    @if(request()->route('darulifta'))
                        {{ request()->route('darulifta') }}
                    @else
                        All Daruliftaas
                    @endif
                </span>
                @if(request('selectedmufti') && request('selectedmufti') != 'all')
                    <span class="badge bg-info ms-1">
                        @foreach($checkerlist as $checker)
                            @if($checker->folder_id == request('selectedmufti'))
                                {{ $checker->checker_name }}
                                @break
                            @endif
                        @endforeach
                    </span>
                @endif
                <span class="badge bg-success ms-1">
                    {{ ucfirst(str_replace('_', ' ', request('selectedTimeFrame', 'this_month'))) }}
                </span>
            </div>
            <div class="view-count">
                <small class="text-muted">
                    <i class="fas fa-eye me-1"></i>
                    Viewing {{ count($daruliftaNames) }} Darulifta(s)
                </small>
            </div>
        </div>
    </div>
</div>
