{{-- Sent For Checking Summary Table Partial --}}

<!-- Summary Table -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Darulifta Summary
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="table-responsive">
            <table class="table-modern">
                <thead>
                    <tr>
                        <th>
                            <i class="fas fa-building me-2"></i>
                            Darulifta
                        </th>
                        @if ($selectedmufti == 'all')
                            <th>
                                <i class="fas fa-user-check me-2"></i>
                                Checker
                            </th>
                        @endif
                        <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Sent Fatawa Mail Folder
                        </th>
                        <th>
                            <i class="fas fa-chart-bar me-2"></i>
                            Total
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $totalCounts = 0;
                        $overallFolderCount = 0;
                    @endphp

                    @if ($selectedmufti == 'all')
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($sendingFatawa[$daruliftaName]))
                                @foreach($sendingFatawa[$daruliftaName] as $checked => $dates)
                                    @php
                                        $daruliftaTotalCounts = 0;
                                        $folderCounts = [];
                                        $transferByCounts = [];
                                        $folderEntries = [];
                                    @endphp

                                    @foreach($dates as $mailfolderDates => $files)
                                        @foreach($files as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $checkedFolder = $file->checked_folder ?? "";
                                                $byMufti = $file->by_mufti ?? "";
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] = isset($transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti]) ? $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] + 1 : 1;

                                                $daruliftaTotalCounts++;
                                                $totalCounts++;

                                                $folderEntries[$folder] = $folderEntries[$folder] ?? ['count' => 0, 'transfer_by' => []];
                                                $folderEntries[$folder]['count']++;
                                                $folderEntries[$folder]['transfer_by'][$transferBy] = ($folderEntries[$folder]['transfer_by'][$transferBy] ?? 0) + 1;
                                            @endphp
                                        @endforeach
                                    @endforeach

                                    <tr>
                                        <td>
                                            @if ($loop->first || $loop->parent->first)
                                                @php
                                                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                                @endphp
                                                <a href="{{ route('sending-fatawa', [
                                                    'darulifta' => $daruliftaName,
                                                    'selectedmujeeb' => $this->selectedmujeeb,
                                                    'selectedmufti' => $this->selectedmufti,
                                                    'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                    'startDate' => $this->startDate,
                                                    'endDate' => $this->endDate,
                                                ]) }}&{{ $selectedMonthsQuery }}" class="table-link">
                                                    {{ $daruliftaName }}
                                                </a>
                                            @endif
                                        </td>
                                        <td>{{ $checked }}</td>
                                        <td class="align-middle text-center" style="max-width: 1000px;">
                                            @php $foldercount = 0; @endphp
                                            <div class="folder-entries-modern">
                                                @foreach ($folderEntries as $folder => $data)
                                                    <div class="folder-entry-modern">
                                                        <div class="folder-date-modern">
                                                            <a href="{{ route('sending-fatawa', ['darulifta' => $daruliftaName, 'mailfolder' => $folder, 'selectedmujeeb' => $this->selectedmujeeb, 'selectedmufti' => $this->selectedmufti, 'selectedTimeFrame' => $this->tempSelectedTimeFrame, 'startDate' => $this->startDate, 'endDate' => $this->endDate]) }}"
                                                               class="text-decoration-none">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                            </a>
                                                            <span class="badge bg-info ms-2">{{ $data['count'] }}</span>
                                                        </div>
                                                        @if(isset($transferByCounts[$folder]))
                                                            <div class="folder-transfer-by-modern">
                                                                @foreach($transferByCounts[$folder] as $transferBy => $checkedFolders)
                                                                    @foreach($checkedFolders as $checkedFolder => $byMuftis)
                                                                        @foreach($byMuftis as $byMufti => $transferByCount)
                                                                            <a href="{{ route('sending-fatawa', ['selectedDarul' => $daruliftaName, 'mailfolder' => $folder, 'transfer_by' => $transferBy, 'selectedmujeeb' => $this->selectedmujeeb, 'selectedmufti' => $this->selectedmufti, 'selectedTimeFrame' => $this->tempSelectedTimeFrame, 'startDate' => $this->startDate, 'endDate' => $this->endDate]) }}"
                                                                               class="badge bg-secondary text-decoration-none me-1 mb-1">
                                                                                <i class="fas fa-user me-1"></i>
                                                                                {{ $transferBy }}: {{ $transferByCount }}
                                                                            </a>
                                                                        @endforeach
                                                                    @endforeach
                                                                @endforeach
                                                            </div>
                                                        @endif
                                                    </div>
                                                    @php
                                                        $foldercount++;
                                                        $overallFolderCount++;
                                                    @endphp
                                                @endforeach
                                            </div>
                                        </td>
                                        <td>
                                            <div class="summary-stats-modern">
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-file-alt me-1"></i>
                                                    Fatawa: {{ $daruliftaTotalCounts }}
                                                </span>
                                                <span class="badge bg-success ms-1">
                                                    <i class="fas fa-folder me-1"></i>
                                                    Folders: {{ $foldercount }}
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        @endforeach
                    @else
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($sendingFatawa[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0;
                                    $folderCounts = [];
                                    $transferByCounts = [];
                                @endphp

                                @foreach($mailfolderDate as $mailfolderDates)
                                    @if(isset($sendingFatawa[$daruliftaName][$mailfolderDates]))
                                        @foreach($sendingFatawa[$daruliftaName][$mailfolderDates] as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $checkedFolder = $file->checked_folder ?? "";
                                                $byMufti = $file->by_mufti ?? "";
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] = isset($transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti]) ? $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach

                                <tr>
                                    <td>
                                        @php
                                            $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                        @endphp
                                        <a href="{{ route('sending-fatawa', [
                                            'darulifta' => $daruliftaName,
                                            'selectedmujeeb' => $this->selectedmujeeb,
                                            'selectedmufti' => $this->selectedmufti,
                                            'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                            'selectedexclude' => $this->selectedexclude,
                                            'startDate' => $tempStartDate,
                                            'endDate' => $tempEndDate,
                                        ]) }}&{{ $selectedMonthsQuery }}" class="table-link">
                                            {{ $daruliftaName }}
                                        </a>
                                    </td>
                                    <td class="align-middle text-center" style="max-width: 1000px;">
                                        <div class="folder-entries-modern">
                                            @php
                                                $foldercount = 0;
                                                $currentRoute = 'sent-for-checking';
                                            @endphp
                                            @foreach ($folderCounts as $folder => $count)
                                                <div class="folder-entry-modern">
                                                    <div class="folder-date-modern">
                                                        <a href="{{ route('sending-fatawa', [
                                                            'darulifta' => $daruliftaName,
                                                            'mailfolder' => $folder,
                                                            'selectedmujeeb' => $this->selectedmujeeb,
                                                            'selectedmufti' => $this->selectedmufti,
                                                            'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                            'startDate' => $this->startDate,
                                                            'endDate' => $this->endDate,
                                                        ]) }}&{{ $selectedMonthsQuery }}"
                                                           class="text-decoration-none">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                        </a>
                                                        <span class="badge bg-info ms-2">{{ $count }}</span>
                                                    </div>
                                                    @if(isset($transferByCounts[$folder]))
                                                        <div class="folder-transfer-by-modern">
                                                            @foreach($transferByCounts[$folder] as $transferBy => $checkedFolders)
                                                                @foreach($checkedFolders as $checkedFolder => $byMuftis)
                                                                    @foreach($byMuftis as $byMufti => $transferByCount)
                                                                        <a href="{{ route('sending-fatawa', ['selectedDarul' => $daruliftaName, 'mailfolder' => $folder, 'transfer_by' => $transferBy, 'selectedmujeeb' => $this->selectedmujeeb, 'selectedmufti' => $this->selectedmufti, 'selectedTimeFrame' => $this->tempSelectedTimeFrame, 'startDate' => $this->startDate, 'endDate' => $this->endDate]) }}"
                                                                           class="badge bg-secondary text-decoration-none me-1 mb-1">
                                                                            <i class="fas fa-user me-1"></i>
                                                                            {{ $transferBy }}: {{ $transferByCount }}
                                                                        </a>
                                                                    @endforeach
                                                                @endforeach
                                                            @endforeach
                                                        </div>
                                                    @endif
                                                </div>
                                                @php
                                                    $foldercount++;
                                                    $overallFolderCount++;
                                                @endphp
                                            @endforeach
                                        </div>
                                    </td>
                                    <td>
                                        <div class="summary-stats-modern">
                                            <span class="badge bg-primary">
                                                <i class="fas fa-file-alt me-1"></i>
                                                Fatawa: {{ $daruliftaTotalCounts }}
                                            </span>
                                            <span class="badge bg-success ms-1">
                                                <i class="fas fa-folder me-1"></i>
                                                Folders: {{ $foldercount }}
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Overall Summary -->
        <div class="mt-4 p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-md-6">
                    <h5 class="text-primary mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        <span id="finalTotalFatawa">{{ $totalCounts ?? 0 }}</span>
                    </h5>
                    <small class="text-muted">Total Sent Fatawa</small>
                </div>
                <div class="col-md-6">
                    <h5 class="text-success mb-0">
                        <i class="fas fa-folder me-2"></i>
                        <span id="finalTotalFolders">{{ $overallFolderCount ?? 0 }}</span>
                    </h5>
                    <small class="text-muted">Total Folders</small>
                </div>
            </div>
        </div>
    </div>
</div>
