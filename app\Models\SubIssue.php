<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubIssue extends Model
{
    use HasFactory;

    protected $fillable = [
        'parent_id',
        'name',
        'is_enable',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    public function parentIssue()
    {
        return $this->belongsTo(Issue::class, 'parent_id', 'id');
    }
}
