<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DailyPerformance;
use App\Models\Department;
use App\Models\User;
use Carbon\Carbon;

class PerformanceSummary extends Component
{
    public $viewType = 'overall'; // overall, department, superior
    public $monthFilter = 'current'; // current, previous, custom
    public $statusFilter = 'all';
    public $departmentFilter = 'all';
    public $startDate;
    public $endDate;
    public $summaryData = [];
    public $loading = false;

    public function mount()
    {
        $this->loadSummary();
    }

    public function updatedViewType()
    {
        $this->loadSummary();
    }

    public function updatedMonthFilter()
    {
        $this->loadSummary();
    }

    public function updatedStatusFilter()
    {
        $this->loadSummary();
    }

    public function updatedDepartmentFilter()
    {
        $this->loadSummary();
    }

    public function updatedStartDate()
    {
        if ($this->monthFilter === 'custom') {
            $this->loadSummary();
        }
    }

    public function updatedEndDate()
    {
        if ($this->monthFilter === 'custom') {
            $this->loadSummary();
        }
    }

    public function loadSummary()
    {
        $this->loading = true;
        
        try {
            // Check if user is authorized
            if (!auth()->check()) {
                $this->summaryData = [];
                session()->flash('error', 'You must be logged in to view performance summary');
                $this->loading = false;
                return;
            }

            // Get summary data directly using the same logic as the controller
            $this->summaryData = $this->getSummaryData();
            
        } catch (\Exception $e) {
            $this->summaryData = [];
            session()->flash('error', 'Error loading summary: ' . $e->getMessage());
        }

        $this->loading = false;
    }

    private function getSummaryData()
    {
        // Set date range based on month filter
        if ($this->monthFilter === 'current') {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        } elseif ($this->monthFilter === 'previous') {
            $startDate = Carbon::now()->subMonth()->startOfMonth();
            $endDate = Carbon::now()->subMonth()->endOfMonth();
        } elseif ($this->monthFilter === 'custom' && $this->startDate && $this->endDate) {
            $startDate = Carbon::parse($this->startDate);
            $endDate = Carbon::parse($this->endDate);
        } else {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        }

        $user = auth()->user();
        $query = DailyPerformance::query()->with(['user', 'task', 'department']);
        
        // Apply date filter
        $query->whereBetween('performance_date', [$startDate, $endDate]);
        
        // Apply user permissions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->departments->flatMap->users->pluck('id');
                $userIds = $assistantIds->merge($departmentUserIds)->unique();
                $query->whereIn('user_id', $userIds);
            }
        }
        
        // Apply status filter
        if ($this->statusFilter !== 'all') {
            if ($this->statusFilter === 'submitted') {
                $query->where('is_submitted', true);
            } elseif ($this->statusFilter === 'pending') {
                $query->where('is_submitted', false);
            } elseif ($this->statusFilter === 'overdue') {
                $query->whereDate('performance_date', '<', Carbon::today())
                      ->where('is_submitted', false);
            }
        }
        
        // Apply department filter
        if ($this->departmentFilter !== 'all') {
            $query->whereHas('user.departments', function ($q) {
                $q->where('departments.id', $this->departmentFilter);
            });
        }

        switch ($this->viewType) {
            case 'department':
                return $this->getDepartmentWisePerformanceSummary($query, $startDate, $endDate);
            case 'superior':
                return $this->getSuperiorWisePerformanceSummary($query, $startDate, $endDate);
            default:
                return $this->getOverallPerformanceSummary($query, $startDate, $endDate);
        }
    }

    private function getOverallPerformanceSummary($query, $startDate, $endDate)
    {
        $user = auth()->user();

        // Role-based data filtering
        if ($user->isNazim() || $user->hasRole('nazim-ul-umoor')) {
            // Nazim sees all users with tasks
            $usersWithTasks = User::whereHas('assignedTasks', function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            })->with(['assignedTasks' => function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            }])->get();
        } elseif ($user->isSuperior()) {
            // Superior sees their team (assistants + department members + themselves)
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->departments->flatMap->users->pluck('id');
            $teamUserIds = $assistantIds->merge($departmentUserIds)->push($user->id)->unique();

            $usersWithTasks = User::whereIn('id', $teamUserIds)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                })->with(['assignedTasks' => function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                }])->get();
        } else {
            // Assistant/Mujeeb sees only themselves
            $usersWithTasks = User::where('id', $user->id)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                })->with(['assignedTasks' => function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                }])->get();
        }

        $totalExpectedReports = 0;
        $submittedCount = 0;
        $pendingCount = 0;
        $overdueCount = 0;
        $totalHours = 0;
        $excellentRatings = 0;
        $poorRatings = 0;
        $submittedReports = 0;

        foreach ($usersWithTasks as $taskUser) {
            foreach ($taskUser->assignedTasks as $task) {
                $taskStartDate = Carbon::parse($task->created_at)->startOfDay();
                $taskEndDate = in_array($task->status, ['completed', 'cancelled'])
                    ? Carbon::parse($task->updated_at)->startOfDay()
                    : Carbon::today();

                // Count expected reports for each day the task was active in the date range
                $currentDate = max($startDate, $taskStartDate);
                $endDateForTask = min($endDate, $taskEndDate);

                while ($currentDate->lte($endDateForTask)) {
                    // Only count working days (not holidays/Sundays)
                    if (!\App\Models\PerformanceHoliday::requiresPerformance($currentDate->format('Y-m-d'))) {
                        $currentDate->addDay();
                        continue;
                    }

                    $totalExpectedReports++;

                    $performance = DailyPerformance::where('user_id', $taskUser->id)
                        ->where('task_id', $task->id)
                        ->where('performance_date', $currentDate->format('Y-m-d'))
                        ->first();

                    if ($performance && $performance->is_submitted) {
                        $submittedCount++;
                        $submittedReports++;
                        $totalHours += $performance->hours_worked ?? 0;

                        // Self-rating removed - use superior rating instead if available
                        if ($performance->superior_rating === 'excellent') {
                            $excellentRatings++;
                        } elseif ($performance->superior_rating === 'poor') {
                            $poorRatings++;
                        }
                    } else {
                        if ($currentDate->lt(Carbon::today())) {
                            $overdueCount++;
                        } else {
                            $pendingCount++;
                        }
                    }

                    $currentDate->addDay();
                }
            }
        }

        // Determine role for labels
        $userRole = $user->isNazim() || $user->hasRole('nazim-ul-umoor') ? 'nazim' :
                   ($user->isSuperior() ? 'superior' : 'assistant');

        return [
            'view_type' => 'overall',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'summary' => [
                'total_users' => $usersWithTasks->count(),
                'submitted' => $submittedCount,
                'pending' => $pendingCount,
                'overdue' => $overdueCount,
                'submission_rate' => $totalExpectedReports > 0 ? round(($submittedCount / $totalExpectedReports) * 100, 1) : 0,
                'avg_hours' => $submittedReports > 0 ? round($totalHours / $submittedReports, 1) : 0,
                'excellent_ratings' => $excellentRatings,
                'poor_ratings' => $poorRatings,
                'user_role' => $userRole
            ]
        ];
    }

        private function getDepartmentWisePerformanceSummary($query, $startDate, $endDate)
    {
        $performances = $query->get();
        $departments = Department::active()->get();
        
        $departmentSummary = [];
        
        foreach ($departments as $department) {
            $deptPerformances = $performances->where('department_id', $department->id);
            
            if ($deptPerformances->count() > 0) {
                $deptUserIds = $deptPerformances->pluck('user_id')->unique();
                $submittedCount = $deptPerformances->where('is_submitted', true)->count();
                
                $departmentSummary[] = [
                    'department' => [
                        'id' => $department->id,
                        'name' => $department->name
                    ],
                    'summary' => [
                        'total_users' => $deptUserIds->count(),
                        'submitted' => $submittedCount,
                        'pending' => $deptPerformances->where('is_submitted', false)->count(),
                        'overdue' => $deptPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count(),
                        'submission_rate' => $deptPerformances->count() > 0 ? round(($submittedCount / $deptPerformances->count()) * 100, 1) : 0,
                        'avg_hours' => round($deptPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                        'excellent_ratings' => $deptPerformances->where('superior_rating', 'excellent')->count()
                    ],
                    'users' => $this->getDepartmentUsersPerformanceSummary($deptPerformances, $department, $deptUserIds)
                ];
            }
        }
        
        return [
            'view_type' => 'department',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'departments' => $departmentSummary
        ];
    }

    private function getSuperiorWisePerformanceSummary($query, $startDate, $endDate)
    {
        $performances = $query->get();
        $superiors = User::whereHas('roles', function($q) {
            $q->where('name', 'Superior');
        })->with('assistants')->get();
        
        $superiorSummary = [];
        
        foreach ($superiors as $superior) {
            $superiorPerformances = $performances->where('user_id', $superior->id);
            
            $assistantsSummary = [];
            foreach ($superior->assistants as $assistant) {
                $assistantPerformances = $performances->where('user_id', $assistant->id);
                
                if ($assistantPerformances->count() > 0) {
                    $submittedCount = $assistantPerformances->where('is_submitted', true)->count();
                    
                    $assistantsSummary[] = [
                        'assistant' => [
                            'id' => $assistant->id,
                            'name' => $assistant->name,
                            'email' => $assistant->email
                        ],
                        'summary' => [
                            'total_reports' => $assistantPerformances->count(),
                            'submitted' => $submittedCount,
                            'pending' => $assistantPerformances->where('is_submitted', false)->count(),
                            'overdue' => $assistantPerformances->filter(function($perf) {
                                return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                            })->count(),
                            'avg_hours' => round($assistantPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                            'avg_rating' => $this->getAverageRating($assistantPerformances->where('is_submitted', true))
                        ]
                    ];
                }
            }
            
            $totalTeamReports = $superiorPerformances->count() + collect($assistantsSummary)->sum('summary.total_reports');
            
            if ($totalTeamReports > 0) {
                $superiorSubmitted = $superiorPerformances->where('is_submitted', true)->count();
                
                $superiorSummary[] = [
                    'superior' => [
                        'id' => $superior->id,
                        'name' => $superior->name,
                        'email' => $superior->email
                    ],
                    'superior_performance' => [
                        'total_reports' => $superiorPerformances->count(),
                        'submitted' => $superiorSubmitted,
                        'pending' => $superiorPerformances->where('is_submitted', false)->count(),
                        'overdue' => $superiorPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count(),
                        'avg_hours' => round($superiorPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                        'avg_rating' => $this->getAverageRating($superiorPerformances->where('is_submitted', true))
                    ],
                    'team_summary' => [
                        'total_reports' => $totalTeamReports,
                        'submitted' => $superiorSubmitted + collect($assistantsSummary)->sum('summary.submitted'),
                        'pending' => $superiorPerformances->where('is_submitted', false)->count() + collect($assistantsSummary)->sum('summary.pending'),
                        'overdue' => $superiorPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count() + collect($assistantsSummary)->sum('summary.overdue'),
                        'avg_hours' => round(($superiorPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0 + collect($assistantsSummary)->avg('summary.avg_hours')) / 2, 1)
                    ],
                    'assistants' => $assistantsSummary
                ];
            }
        }
        
        return [
            'view_type' => 'superior',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'superiors' => $superiorSummary
        ];
    }

    private function getDepartmentUsersPerformanceSummary($performances, $department, $userIds)
    {
        $usersSummary = [];
        
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            if ($user) {
                $userPerformances = $performances->where('user_id', $userId);
                $submittedCount = $userPerformances->where('is_submitted', true)->count();
                
                $usersSummary[] = [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->isSuperior() ? 'Superior' : ($user->isMujeeb() ? 'Assistant' : 'Other')
                    ],
                    'summary' => [
                        'total_reports' => $userPerformances->count(),
                        'submitted' => $submittedCount,
                        'pending' => $userPerformances->where('is_submitted', false)->count(),
                        'overdue' => $userPerformances->filter(function($perf) {
                            return $perf->performance_date < Carbon::today() && !$perf->is_submitted;
                        })->count(),
                        'avg_hours' => round($userPerformances->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
                        'avg_rating' => $this->getAverageRating($userPerformances->where('is_submitted', true))
                    ]
                ];
            }
        }
        
        return $usersSummary;
    }

    private function getAverageRating($performances)
    {
        $ratingValues = [
            'poor' => 1,
            'fair' => 2,
            'good' => 3,
            'excellent' => 4
        ];
        
        $ratings = $performances->whereNotNull('superior_rating')->pluck('superior_rating');
        
        if ($ratings->isEmpty()) {
            return 'N/A';
        }
        
        $avgValue = $ratings->map(function($rating) use ($ratingValues) {
            return $ratingValues[$rating] ?? 0;
        })->avg();
        
        $ratingLabels = array_flip($ratingValues);
        return $ratingLabels[round($avgValue)] ?? 'N/A';
    }

    public function getStatusColor($status)
    {
        return match($status) {
            'submitted' => 'success',
            'pending' => 'warning',
            'overdue' => 'danger',
            default => 'primary'
        };
    }

    public function getRatingColor($rating)
    {
        return match($rating) {
            'excellent' => 'success',
            'good' => 'info',
            'fair' => 'warning',
            'poor' => 'danger',
            default => 'secondary'
        };
    }

    public function render()
    {
        $departments = Department::active()->select('id', 'name')->orderBy('name')->get();
        
        return view('livewire.performance-summary', [
            'departments' => $departments
        ]);
    }
}