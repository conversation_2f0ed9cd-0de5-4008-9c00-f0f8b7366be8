<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Task;
use App\Models\Department;
use App\Models\User;
use Carbon\Carbon;

class WorkflowTaskSummary extends Component
{
    public $viewType = 'overall'; // overall, department, superior
    public $monthFilter = 'current'; // current, previous, custom
    public $statusFilter = 'all';
    public $departmentFilter = 'all';
    public $startDate;
    public $endDate;
    public $summaryData = [];
    public $loading = false;
    public $showViewMoreButton = true; // Property to control View More button visibility

    public function mount($showViewMoreButton = true)
    {
        $this->showViewMoreButton = $showViewMoreButton;
        $this->loadSummary();
    }

    public function updatedViewType()
    {
        $this->loadSummary();
    }

    public function updatedMonthFilter()
    {
        $this->loadSummary();
    }

    public function updatedStatusFilter()
    {
        $this->loadSummary();
    }

    public function updatedDepartmentFilter()
    {
        $this->loadSummary();
    }

    public function updatedStartDate()
    {
        if ($this->monthFilter === 'custom') {
            $this->loadSummary();
        }
    }

    public function updatedEndDate()
    {
        if ($this->monthFilter === 'custom') {
            $this->loadSummary();
        }
    }

    public function loadSummary()
    {
        $this->loading = true;
        
        try {
            // Check if user is authorized
            if (!auth()->check()) {
                $this->summaryData = [];
                session()->flash('error', 'You must be logged in to view task summary');
                $this->loading = false;
                return;
            }

            // Get summary data directly using the same logic as the controller
            $this->summaryData = $this->getSummaryData();
            
        } catch (\Exception $e) {
            $this->summaryData = [];
            session()->flash('error', 'Error loading summary: ' . $e->getMessage());
        }

        $this->loading = false;
    }

    private function getSummaryData()
    {
        // Set date range based on month filter
        if ($this->monthFilter === 'current') {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        } elseif ($this->monthFilter === 'previous') {
            $startDate = Carbon::now()->subMonth()->startOfMonth();
            $endDate = Carbon::now()->subMonth()->endOfMonth();
        } elseif ($this->monthFilter === 'custom' && $this->startDate && $this->endDate) {
            $startDate = Carbon::parse($this->startDate);
            $endDate = Carbon::parse($this->endDate);
        } else {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        }

        $user = auth()->user();
        $query = Task::query()->with(['assignedTo', 'assignedBy', 'department']);
        
        // Apply date filter
        $query->whereBetween('created_at', [$startDate, $endDate]);
        
        // Apply user permissions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $departmentIds = $user->departments->pluck('id');
                $query->where(function ($q) use ($user, $departmentIds) {
                    $q->where('assigned_by', $user->id)
                      ->orWhereIn('department_id', $departmentIds);
                });
            }
        }
        
        // Apply status filter
        if ($this->statusFilter !== 'all') {
            $query->where('status', $this->statusFilter);
        }
        
        // Apply department filter
        if ($this->departmentFilter !== 'all') {
            $query->where('department_id', $this->departmentFilter);
        }

        switch ($this->viewType) {
            case 'department':
                return $this->getDepartmentWiseSummary($query, $startDate, $endDate);
            case 'superior':
                return $this->getSuperiorWiseSummary($query, $startDate, $endDate);
            default:
                return $this->getOverallSummary($query, $startDate, $endDate);
        }
    }

    private function getOverallSummary($query, $startDate, $endDate)
    {
        $tasks = $query->get();
        
        // Group tasks by task_number to count unique tasks
        $groupedTasks = $tasks->groupBy('task_number');
        $uniqueTaskCount = $groupedTasks->count();
        
        // For status counts, we need to consider the main task status
        $mainTasks = $groupedTasks->map(function ($taskGroup) {
            // Return the parent task if exists, otherwise the first task
            return $taskGroup->where('role_type', 'superior')->first() 
                   ?? $taskGroup->where('parent_task_id', null)->first()
                   ?? $taskGroup->first();
        });
        
        return [
            'view_type' => 'overall',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'summary' => [
                'total' => $uniqueTaskCount,
                'pending' => $mainTasks->where('status', 'pending')->count(),
                'in_progress' => $mainTasks->where('status', 'in_progress')->count(),
                'completed' => $mainTasks->where('status', 'completed')->count(),
                'cancelled' => $mainTasks->where('status', 'cancelled')->count(),
                'overdue' => $mainTasks->filter(function($task) {
                    return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                })->count()
            ]
        ];
    }

    private function getDepartmentWiseSummary($query, $startDate, $endDate)
    {
        $tasks = $query->get();
        $departments = Department::active()->get();
        
        $departmentSummary = [];
        
        foreach ($departments as $department) {
            $deptTasks = $tasks->where('department_id', $department->id);
            
            if ($deptTasks->count() > 0) {
                $departmentSummary[] = [
                    'department' => [
                        'id' => $department->id,
                        'name' => $department->name
                    ],
                    'summary' => [
                        'total' => $deptTasks->count(),
                        'pending' => $deptTasks->where('status', 'pending')->count(),
                        'in_progress' => $deptTasks->where('status', 'in_progress')->count(),
                        'completed' => $deptTasks->where('status', 'completed')->count(),
                        'cancelled' => $deptTasks->where('status', 'cancelled')->count(),
                        'overdue' => $deptTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count()
                    ],
                    'users' => $this->getDepartmentUsersSummary($deptTasks, $department)
                ];
            }
        }
        
        // Add tasks without department
        $noDeptTasks = $tasks->whereNull('department_id');
        if ($noDeptTasks->count() > 0) {
            $departmentSummary[] = [
                'department' => [
                    'id' => null,
                    'name' => 'No Department'
                ],
                'summary' => [
                    'total' => $noDeptTasks->count(),
                    'pending' => $noDeptTasks->where('status', 'pending')->count(),
                    'in_progress' => $noDeptTasks->where('status', 'in_progress')->count(),
                    'completed' => $noDeptTasks->where('status', 'completed')->count(),
                    'cancelled' => $noDeptTasks->where('status', 'cancelled')->count(),
                    'overdue' => $noDeptTasks->filter(function($task) {
                        return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                    })->count()
                ],
                'users' => $this->getDepartmentUsersSummary($noDeptTasks, null)
            ];
        }
        
        return [
            'view_type' => 'department',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'departments' => $departmentSummary
        ];
    }

    private function getSuperiorWiseSummary($query, $startDate, $endDate)
    {
        $tasks = $query->get();
        $superiors = User::whereHas('roles', function($q) {
            $q->where('name', 'Superior');
        })->with('assistants')->get();
        
        $superiorSummary = [];
        
        foreach ($superiors as $superior) {
            $superiorTasks = $tasks->where('assigned_to', $superior->id);
            
            $assistantsSummary = [];
            foreach ($superior->assistants as $assistant) {
                $assistantTasks = $tasks->where('assigned_to', $assistant->id);
                
                if ($assistantTasks->count() > 0) {
                    $assistantsSummary[] = [
                        'assistant' => [
                            'id' => $assistant->id,
                            'name' => $assistant->name,
                            'email' => $assistant->email
                        ],
                        'summary' => [
                            'total' => $assistantTasks->count(),
                            'pending' => $assistantTasks->where('status', 'pending')->count(),
                            'in_progress' => $assistantTasks->where('status', 'in_progress')->count(),
                            'completed' => $assistantTasks->where('status', 'completed')->count(),
                            'cancelled' => $assistantTasks->where('status', 'cancelled')->count(),
                            'overdue' => $assistantTasks->filter(function($task) {
                                return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                            })->count()
                        ]
                    ];
                }
            }
            
            $totalSuperiorTasks = $superiorTasks->count() + collect($assistantsSummary)->sum('summary.total');
            
            if ($totalSuperiorTasks > 0) {
                $superiorSummary[] = [
                    'superior' => [
                        'id' => $superior->id,
                        'name' => $superior->name,
                        'email' => $superior->email
                    ],
                    'superior_tasks' => [
                        'total' => $superiorTasks->count(),
                        'pending' => $superiorTasks->where('status', 'pending')->count(),
                        'in_progress' => $superiorTasks->where('status', 'in_progress')->count(),
                        'completed' => $superiorTasks->where('status', 'completed')->count(),
                        'cancelled' => $superiorTasks->where('status', 'cancelled')->count(),
                        'overdue' => $superiorTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count()
                    ],
                    'team_summary' => [
                        'total' => $totalSuperiorTasks,
                        'pending' => $superiorTasks->where('status', 'pending')->count() + collect($assistantsSummary)->sum('summary.pending'),
                        'in_progress' => $superiorTasks->where('status', 'in_progress')->count() + collect($assistantsSummary)->sum('summary.in_progress'),
                        'completed' => $superiorTasks->where('status', 'completed')->count() + collect($assistantsSummary)->sum('summary.completed'),
                        'cancelled' => $superiorTasks->where('status', 'cancelled')->count() + collect($assistantsSummary)->sum('summary.cancelled'),
                        'overdue' => $superiorTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count() + collect($assistantsSummary)->sum('summary.overdue')
                    ],
                    'assistants' => $assistantsSummary
                ];
            }
        }
        
        return [
            'view_type' => 'superior',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'superiors' => $superiorSummary
        ];
    }

    private function getDepartmentUsersSummary($tasks, $department)
    {
        $usersSummary = [];
        $userIds = $tasks->pluck('assigned_to')->unique();
        
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            if ($user) {
                $userTasks = $tasks->where('assigned_to', $userId);
                
                $usersSummary[] = [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->isSuperior() ? 'Superior' : ($user->isMujeeb() ? 'Assistant' : 'Other')
                    ],
                    'summary' => [
                        'total' => $userTasks->count(),
                        'pending' => $userTasks->where('status', 'pending')->count(),
                        'in_progress' => $userTasks->where('status', 'in_progress')->count(),
                        'completed' => $userTasks->where('status', 'completed')->count(),
                        'cancelled' => $userTasks->where('status', 'cancelled')->count(),
                        'overdue' => $userTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count()
                    ]
                ];
            }
        }
        
        return $usersSummary;
    }

    public function getStatusColor($status)
    {
        return match($status) {
            'completed' => 'success',
            'in_progress' => 'warning',
            'pending' => 'info',
            'cancelled' => 'secondary',
            'overdue' => 'danger',
            default => 'primary'
        };
    }

    public function render()
    {
        $departments = Department::active()->select('id', 'name')->orderBy('name')->get();
        
        return view('livewire.workflow-task-summary', [
            'departments' => $departments
        ]);
    }
}