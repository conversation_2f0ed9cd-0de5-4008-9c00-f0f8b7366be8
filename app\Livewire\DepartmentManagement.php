<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Department;
use App\Models\User;
use App\Models\DepartmentSupervisorAssistant;
use App\Services\DepartmentTeamManagementService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class DepartmentManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $showCreateModal = false;
    public $showEditModal = false;
    public $showAssignModal = false;
    
    public $departmentId;
    public $name = '';
    public $description = '';
    public $is_active = true;
    
    public $selectedUsers = [];
    public $availableUsers = [];
    public $assignedUsers = [];
    
    public $search = '';
    public $filterActive = 'all';

    protected $departmentTeamService;

    protected $rules = [
        'name' => 'required|string|max:255|unique:departments,name',
        'description' => 'nullable|string',
        'is_active' => 'boolean',
    ];

    public function __construct()
    {
        $this->departmentTeamService = app(DepartmentTeamManagementService::class);
    }

    public function mount()
    {
        $this->authorize('manage-departments');
        $this->loadAvailableUsers();
    }

    public function render()
    {
        $departments = Department::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->filterActive !== 'all', function ($query) {
                $query->where('is_active', $this->filterActive === 'active');
            })
            ->withCount([
                'users', // Old system count
                'supervisors', // New system supervisors
                'assistants', // New system assistants
                'teamMembers', // New system total (supervisors + assistants)
                'tasks' // Associated tasks count
            ])
            ->paginate(10);

        return view('livewire.department-management', [
            'departments' => $departments,
        ]);
    }

    public function openCreateModal()
    {
        $this->authorize('create', Department::class);
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function openEditModal($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('update', $department);
        
        $this->departmentId = $department->id;
        $this->name = $department->name;
        $this->description = $department->description;
        $this->is_active = $department->is_active;
        
        $this->showEditModal = true;
    }

    public function openAssignModal($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('assignUsers', $department);
        
        $this->departmentId = $departmentId;
        $this->loadAssignedUsers($departmentId);
        $this->selectedUsers = $this->assignedUsers->pluck('id')->toArray();
        
        $this->showAssignModal = true;
    }

    public function createDepartment()
    {
        $this->authorize('create', Department::class);
        $this->validate();

        Department::create([
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        session()->flash('message', 'Department created successfully.');
    }

    public function updateDepartment()
    {
        $department = Department::findOrFail($this->departmentId);
        $this->authorize('update', $department);

        $this->rules['name'] = 'required|string|max:255|unique:departments,name,' . $this->departmentId;
        $this->validate();

        $department->update([
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        session()->flash('message', 'Department updated successfully.');
    }

    public function assignUsers()
    {
        $department = Department::findOrFail($this->departmentId);
        $this->authorize('assignUsers', $department);

        // Prepare sync data with pivot fields for old system
        $syncData = [];
        foreach ($this->selectedUsers as $userId) {
            $syncData[$userId] = [
                'assigned_at' => now(),
                'assigned_by' => auth()->id(),
            ];
        }

        // Sync users with the department (old system)
        $department->users()->sync($syncData);

        // Also sync with new system - assign users as supervisors by default
        // First, deactivate all existing assignments for this department
        DepartmentSupervisorAssistant::where('department_id', $department->id)
            ->update(['is_active' => false]);

        // Then create new assignments
        foreach ($this->selectedUsers as $userId) {
            $user = User::find($userId);
            if ($user) {
                try {
                    $this->departmentTeamService->assignSupervisor($department, $user, auth()->user());
                } catch (\Exception $e) {
                    // If user is already assigned, just continue
                    continue;
                }
            }
        }

        $this->showAssignModal = false;
        session()->flash('message', 'Users assigned to department successfully.');
    }

    public function deleteDepartment($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('delete', $department);

        // Check if department can be safely deleted
        if (!$department->canBeDeleted()) {
            $reason = $department->getDeletionBlockReason();
            session()->flash('error', 'Cannot delete department. ' . $reason . '. Please remove all assignments first.');
            return;
        }

        // Safe to delete - remove any inactive assignments first
        DepartmentSupervisorAssistant::where('department_id', $departmentId)->delete();
        
        $department->delete();
        session()->flash('message', 'Department "' . $department->name . '" deleted successfully.');
    }

    public function toggleStatus($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('update', $department);

        $department->update(['is_active' => !$department->is_active]);
        
        $status = $department->is_active ? 'activated' : 'deactivated';
        session()->flash('message', "Department {$status} successfully.");
    }

    private function resetForm()
    {
        $this->departmentId = null;
        $this->name = '';
        $this->description = '';
        $this->is_active = true;
        $this->selectedUsers = [];
        $this->resetValidation();
    }

    private function loadAvailableUsers()
    {
        $this->availableUsers = User::select('id', 'name', 'email')
            ->orderBy('name')
            ->get();
    }

    private function loadAssignedUsers($departmentId)
    {
        // Get users from both old and new systems
        $oldSystemUsers = Department::findOrFail($departmentId)
            ->users()
            ->select('users.id', 'users.name', 'users.email')
            ->get();

        $newSystemUsers = User::whereHas('departmentAssignments', function ($q) use ($departmentId) {
            $q->where('department_id', $departmentId)
              ->where('is_active', true);
        })->select('id', 'name', 'email')->get();

        // Merge and remove duplicates
        $this->assignedUsers = $oldSystemUsers->merge($newSystemUsers)->unique('id');
    }

    public function closeModals()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showAssignModal = false;
        $this->resetForm();
    }
}
