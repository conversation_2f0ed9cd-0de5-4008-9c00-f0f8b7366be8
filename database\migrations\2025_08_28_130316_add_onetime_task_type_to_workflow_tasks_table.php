<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'one-time' to the task type enum
        DB::statement("ALTER TABLE workflow_tasks MODIFY COLUMN type ENUM('daily', 'weekly', 'operational', 'one-time') DEFAULT 'daily'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'one-time' from the task type enum
        DB::statement("ALTER TABLE workflow_tasks MODIFY COLUMN type ENUM('daily', 'weekly', 'operational') DEFAULT 'daily'");
    }
};