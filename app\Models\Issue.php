<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Issue extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'parent_id',
        'type',
        'is_enable',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    public function subIssues()
    {
        return $this->hasMany(SubIssue::class, 'parent_id', 'id');
    }
}
