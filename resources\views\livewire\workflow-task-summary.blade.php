@push('styles')
<link rel="stylesheet" href="{{ asset('css/task-summary.css') }}">
@endpush

<div>
    <!-- Task Summary Header replacing the empty purple area -->
    <div class="page-header min-height-300 border-radius-xl mt-4" 
         style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row h-100 align-items-center">
                <div class="col-12">
                    <div class="p-4">
                    <!-- Header Section -->
                    <div class="row align-items-center mb-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="icon icon-lg icon-shape bg-white shadow text-center border-radius-xl me-3">
                                    <i class="material-icons opacity-10 text-primary">task</i>
                                </div>
                                <div>
                                    <h5 class="mb-0 text-white">Task Management</h5>
                                    <p class="text-white-50 mb-0">Assign and manage daily/weekly tasks</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <!-- View Type Toggle -->
                            <div class="btn-group" role="group">
                                <button wire:click="$set('viewType', 'overall')" 
                                        class="btn btn-sm {{ $viewType === 'overall' ? 'btn-light' : 'btn-outline-light' }}">
                                    <i class="fas fa-chart-bar me-1"></i> OVERALL
                                </button>
                                <button wire:click="$set('viewType', 'department')" 
                                        class="btn btn-sm {{ $viewType === 'department' ? 'btn-light' : 'btn-outline-light' }}">
                                    <i class="fas fa-building me-1"></i> DEPARTMENT
                                </button>
                                <button wire:click="$set('viewType', 'superior')" 
                                        class="btn btn-sm {{ $viewType === 'superior' ? 'btn-light' : 'btn-outline-light' }}">
                                    <i class="fas fa-users me-1"></i> SUPERIOR
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filters Row -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <label class="form-label text-white text-xs">Time Period</label>
                            <select wire:model.live="monthFilter" class="form-select form-select-sm">
                                <option value="current">Current Month</option>
                                <option value="previous">Previous Month</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        
                        @if($monthFilter === 'custom')
                        <div class="col-md-2">
                            <label class="form-label text-white text-xs">Start Date</label>
                            <input type="date" wire:model.live="startDate" class="form-control form-control-sm">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white text-xs">End Date</label>
                            <input type="date" wire:model.live="endDate" class="form-control form-control-sm">
                        </div>
                        @endif
                        
                        <div class="col-md-2">
                            <label class="form-label text-white text-xs">Status Filter</label>
                            <select wire:model.live="statusFilter" class="form-select form-select-sm">
                                <option value="all">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        
                        @if($viewType !== 'superior')
                        <div class="col-md-2">
                            <label class="form-label text-white text-xs">Department</label>
                            <select wire:model.live="departmentFilter" class="form-select form-select-sm">
                                <option value="all">All Departments</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}">{{ $department->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        @endif
                        
                        <div class="col-md-2">
                            <label class="form-label text-white text-xs">&nbsp;</label>
                            <button wire:click="loadSummary" class="btn btn-sm btn-light d-block">
                                <i class="fas fa-sync-alt"></i> REFRESH
                            </button>
                        </div>
                    </div>
                    
                    <!-- Summary Content -->
                    @if($loading)
                        <!-- Loading State -->
                        <div class="text-center py-3">
                            <div class="spinner-border text-white" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-white-50 mb-0">Loading summary data...</p>
                        </div>
                    @elseif(!empty($summaryData))
                        <!-- Period Badge -->
                        <div class="mb-3">
                            <span class="badge bg-white text-primary px-3 py-2">
                                <i class="fas fa-calendar me-2"></i> 
                                {{ $summaryData['period']['label'] ?? 'Summary' }}
                            </span>
                        </div>
                        
                        <!-- Overall View - Summary Cards in Purple Header -->
                        @if($viewType === 'overall' && isset($summaryData['summary']))
                        <div class="row g-3">
                            <div class="col-xl-2 col-md-4 col-sm-6">
                                <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                    <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">task</i>
                                    </div>
                                    <h4 class="mb-0 text-primary">{{ $summaryData['summary']['total'] }}</h4>
                                    <span class="text-xs text-secondary">Total Tasks</span>
                                </div>
                            </div>
                            
                            <div class="col-xl-2 col-md-4 col-sm-6">
                                <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                    <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">pending</i>
                                    </div>
                                    <h4 class="mb-0 text-info">{{ $summaryData['summary']['pending'] }}</h4>
                                    <span class="text-xs text-secondary">Pending</span>
                                </div>
                            </div>
                            
                            <div class="col-xl-2 col-md-4 col-sm-6">
                                <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                    <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">hourglass_empty</i>
                                    </div>
                                    <h4 class="mb-0 text-warning">{{ $summaryData['summary']['in_progress'] }}</h4>
                                    <span class="text-xs text-secondary">In Progress</span>
                                </div>
                            </div>
                            
                            <div class="col-xl-2 col-md-4 col-sm-6">
                                <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                    <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">check_circle</i>
                                    </div>
                                    <h4 class="mb-0 text-success">{{ $summaryData['summary']['completed'] }}</h4>
                                    <span class="text-xs text-secondary">Completed</span>
                                </div>
                            </div>
                            
                            <div class="col-xl-2 col-md-4 col-sm-6">
                                <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                    <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">schedule</i>
                                    </div>
                                    <h4 class="mb-0 text-danger">{{ $summaryData['summary']['overdue'] }}</h4>
                                    <span class="text-xs text-secondary">Overdue</span>
                                </div>
                            </div>
                            
                            <div class="col-xl-2 col-md-4 col-sm-6">
                                <div class="bg-white rounded-3 p-3 text-center shadow-sm compact-summary-card">
                                    <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md mx-auto mb-2" style="width: 40px; height: 40px;">
                                        <i class="material-icons opacity-10 text-white" style="font-size: 20px; line-height: 40px;">cancel</i>
                                    </div>
                                    <h4 class="mb-0 text-secondary">{{ $summaryData['summary']['cancelled'] }}</h4>
                                    <span class="text-xs text-secondary">Cancelled</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- View More Button - Only show on Dashboard -->
                        @if($showViewMoreButton)
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <a href="{{ route('workflow-tasks.index') }}" class="view-more-button">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    View More Details
                                </a>
                            </div>
                        </div>
                        @endif
                        @endif
                        
                        <!-- Department View -->
                        @if($viewType === 'department' && isset($summaryData['departments']))
                        <div class="text-center py-3">
                            <h6 class="text-white mb-2">
                                <i class="fas fa-building me-2"></i> 
                                Department-wise Summary
                            </h6>
                            <p class="text-white-50 mb-0">{{ count($summaryData['departments']) }} departments found</p>
                        </div>
                        @endif
                        
                        <!-- Superior View -->
                        @if($viewType === 'superior' && isset($summaryData['superiors']))
                        <div class="text-center py-3">
                            <h6 class="text-white mb-2">
                                <i class="fas fa-users me-2"></i> 
                                Superior-wise Summary
                            </h6>
                            <p class="text-white-50 mb-0">{{ count($summaryData['superiors']) }} superiors found</p>
                        </div>
                        @endif
                    @else
                        <!-- Empty State -->
                        <div class="text-center py-3">
                            <i class="fas fa-chart-bar fa-2x text-white-50 mb-2"></i>
                            <h6 class="text-white">No Summary Data Available</h6>
                            <p class="text-white-50 mb-0">No tasks found for the selected filters.</p>
                        </div>
                    @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department and Superior Details (Below Header) -->
    @if(!$loading && !empty($summaryData))
        <div class="container-fluid">
        <!-- Department View Details -->
        @if($viewType === 'department' && isset($summaryData['departments']))
        <div class="row mt-4 department-details">
            @foreach($summaryData['departments'] as $deptData)
            <div class="col-12 mb-4">
                <div class="card department-card">
                    <div class="card-header pb-0">
                        <div class="d-flex align-items-center">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md me-3">
                                <i class="material-icons opacity-10 text-white">business</i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $deptData['department']['name'] }}</h6>
                                <p class="text-sm text-secondary mb-0">
                                    {{ $deptData['summary']['total'] }} total tasks
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Department Summary Cards -->
                        <div class="row mb-3">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-primary mb-0">{{ $deptData['summary']['total'] }}</h5>
                                    <span class="text-xs text-secondary">Total</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-info mb-0">{{ $deptData['summary']['pending'] }}</h5>
                                    <span class="text-xs text-secondary">Pending</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-warning mb-0">{{ $deptData['summary']['in_progress'] }}</h5>
                                    <span class="text-xs text-secondary">In Progress</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-success mb-0">{{ $deptData['summary']['completed'] }}</h5>
                                    <span class="text-xs text-secondary">Completed</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-danger mb-0">{{ $deptData['summary']['overdue'] }}</h5>
                                    <span class="text-xs text-secondary">Overdue</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-secondary mb-0">{{ $deptData['summary']['cancelled'] }}</h5>
                                    <span class="text-xs text-secondary">Cancelled</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Department Users -->
                        @if(!empty($deptData['users']))
                        <div class="table-responsive">
                            <table class="table table-sm align-items-center mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Pending</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">In Progress</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Completed</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Overdue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($deptData['users'] as $userData)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm bg-gradient-{{ $userData['user']['role'] === 'Superior' ? 'success' : 'info' }} me-2">
                                                    <span class="text-white text-xs font-weight-bold">
                                                        {{ strtoupper(substr($userData['user']['name'], 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-weight-bold mb-0">{{ $userData['user']['name'] }}</p>
                                                    <span class="badge badge-sm bg-gradient-{{ $userData['user']['role'] === 'Superior' ? 'success' : 'info' }}">
                                                        {{ $userData['user']['role'] }}
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center"><span class="text-sm font-weight-bold">{{ $userData['summary']['total'] }}</span></td>
                                        <td class="text-center"><span class="text-sm">{{ $userData['summary']['pending'] }}</span></td>
                                        <td class="text-center"><span class="text-sm">{{ $userData['summary']['in_progress'] }}</span></td>
                                        <td class="text-center"><span class="text-sm">{{ $userData['summary']['completed'] }}</span></td>
                                        <td class="text-center">
                                            @if($userData['summary']['overdue'] > 0)
                                                <span class="badge badge-sm bg-gradient-danger">{{ $userData['summary']['overdue'] }}</span>
                                            @else
                                                <span class="text-sm">0</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        @endif

        <!-- Superior View Details -->
        @if($viewType === 'superior' && isset($summaryData['superiors']))
        <div class="row mt-4 superior-details">
            @foreach($summaryData['superiors'] as $superiorData)
            <div class="col-12 mb-4">
                <div class="card superior-card">
                    <div class="card-header pb-0">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-lg bg-gradient-success me-3">
                                <span class="text-white font-weight-bold">
                                    {{ strtoupper(substr($superiorData['superior']['name'], 0, 2)) }}
                                </span>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $superiorData['superior']['name'] }}</h6>
                                <p class="text-sm text-secondary mb-0">
                                    <span class="badge badge-sm bg-gradient-success">Superior</span>
                                    {{ $superiorData['superior']['email'] }}
                                </p>
                            </div>
                            <div class="ms-auto">
                                <div class="text-end">
                                    <h5 class="mb-0">{{ $superiorData['team_summary']['total'] }}</h5>
                                    <span class="text-xs text-secondary">Team Total</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Team Summary Cards -->
                        <div class="row mb-4">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-primary mb-0">{{ $superiorData['team_summary']['total'] }}</h5>
                                    <span class="text-xs text-secondary">Team Total</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-info mb-0">{{ $superiorData['team_summary']['pending'] }}</h5>
                                    <span class="text-xs text-secondary">Pending</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-warning mb-0">{{ $superiorData['team_summary']['in_progress'] }}</h5>
                                    <span class="text-xs text-secondary">In Progress</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-success mb-0">{{ $superiorData['team_summary']['completed'] }}</h5>
                                    <span class="text-xs text-secondary">Completed</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-danger mb-0">{{ $superiorData['team_summary']['overdue'] }}</h5>
                                    <span class="text-xs text-secondary">Overdue</span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h5 class="text-secondary mb-0">{{ $superiorData['team_summary']['cancelled'] }}</h5>
                                    <span class="text-xs text-secondary">Cancelled</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Superior's Own Tasks -->
                        @if($superiorData['superior_tasks']['total'] > 0)
                        <div class="mb-4">
                            <h6 class="text-sm font-weight-bold mb-2">Superior's Tasks</h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <h6 class="text-primary mb-0">{{ $superiorData['superior_tasks']['total'] }}</h6>
                                        <span class="text-xs">Total</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <h6 class="text-info mb-0">{{ $superiorData['superior_tasks']['pending'] }}</h6>
                                        <span class="text-xs">Pending</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <h6 class="text-warning mb-0">{{ $superiorData['superior_tasks']['in_progress'] }}</h6>
                                        <span class="text-xs">In Progress</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <h6 class="text-success mb-0">{{ $superiorData['superior_tasks']['completed'] }}</h6>
                                        <span class="text-xs">Completed</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <h6 class="text-danger mb-0">{{ $superiorData['superior_tasks']['overdue'] }}</h6>
                                        <span class="text-xs">Overdue</span>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <h6 class="text-secondary mb-0">{{ $superiorData['superior_tasks']['cancelled'] }}</h6>
                                        <span class="text-xs">Cancelled</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        <!-- Assistants Tasks -->
                        @if(!empty($superiorData['assistants']))
                        <div>
                            <h6 class="text-sm font-weight-bold mb-3">Assistants Tasks</h6>
                            <div class="table-responsive">
                                <table class="table table-sm align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Assistant</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Pending</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">In Progress</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Completed</th>
                                            <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Overdue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($superiorData['assistants'] as $assistantData)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm bg-gradient-info me-2">
                                                        <span class="text-white text-xs font-weight-bold">
                                                            {{ strtoupper(substr($assistantData['assistant']['name'], 0, 2)) }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm font-weight-bold mb-0">{{ $assistantData['assistant']['name'] }}</p>
                                                        <span class="badge badge-sm bg-gradient-info">Assistant</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center"><span class="text-sm font-weight-bold">{{ $assistantData['summary']['total'] }}</span></td>
                                            <td class="text-center"><span class="text-sm">{{ $assistantData['summary']['pending'] }}</span></td>
                                            <td class="text-center"><span class="text-sm">{{ $assistantData['summary']['in_progress'] }}</span></td>
                                            <td class="text-center"><span class="text-sm">{{ $assistantData['summary']['completed'] }}</span></td>
                                            <td class="text-center">
                                                @if($assistantData['summary']['overdue'] > 0)
                                                    <span class="badge badge-sm bg-gradient-danger">{{ $assistantData['summary']['overdue'] }}</span>
                                                @else
                                                    <span class="text-sm">0</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
            </div>
            @endif
        </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation classes to cards
        const cards = document.querySelectorAll('.compact-summary-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('summary-card-animated');
            }, index * 100);
        });
        
        // Auto-refresh summary every 5 minutes
        setInterval(() => {
            if (typeof Livewire !== 'undefined') {
                Livewire.emit('loadSummary');
            }
        }, 300000); // 5 minutes
    });
    </script>
    @endpush
</div>