<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UnlockRequest;
use App\Models\UserRestriction;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UnlockHistoryController extends Controller
{
    public function index()
    {
        // Check if user has permission to view unlock history
        $user = auth()->user();
        if (!$user->hasRole('nazim-ul-umoor') && !$user->hasRole('admin')) {
            abort(403, 'Unauthorized access to unlock history.');
        }

        // Get users with lock/unlock history
        $users = User::whereHas('restrictions')
            ->orWhereHas('unlockRequests')
            ->withCount(['restrictions', 'unlockRequests'])
            ->orderBy('name')
            ->paginate(10);

        return view('pages.unlock-history', compact('users'));
    }

    public function userHistory($userId)
    {
        // Check if user has permission to view unlock history
        $user = auth()->user();
        if (!$user->hasRole('nazim-ul-umoor') && !$user->hasRole('admin') && $user->id != $userId) {
            abort(403, 'Unauthorized access to unlock history.');
        }

        $targetUser = User::findOrFail($userId);
        
        // Get all restrictions (locks) for this user
        $restrictions = UserRestriction::with(['restrictedBy', 'liftedBy'])
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->get();
            
        // Get all unlock requests for this user
        $unlockRequests = UnlockRequest::with(['dismissedBy', 'unlockedBy'])
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->get();
            
        // Combine both into a timeline
        $timeline = collect();
        
        foreach ($restrictions as $restriction) {
            $timeline->push([
                'type' => 'lock',
                'date' => $restriction->restricted_at,
                'reason' => $restriction->reason,
                'restriction_type' => $restriction->restriction_type,
                'by_user' => $restriction->restrictedBy ? $restriction->restrictedBy->name : 'System',
                'data' => $restriction
            ]);
            
            if (!$restriction->is_active) {
                $timeline->push([
                    'type' => 'unlock',
                    'date' => $restriction->lifted_at,
                    'reason' => $restriction->lift_reason,
                    'by_user' => $restriction->liftedBy ? $restriction->liftedBy->name : 'System',
                    'was_manual' => $restriction->was_manually_unlocked,
                    'data' => $restriction
                ]);
            }
        }
        
        foreach ($unlockRequests as $request) {
            $timeline->push([
                'type' => 'request',
                'date' => $request->created_at,
                'message' => $request->message,
                'status' => $request->status,
                'data' => $request
            ]);
            
            if ($request->status == 'unlocked') {
                $timeline->push([
                    'type' => 'approved',
                    'date' => $request->updated_at,
                    'reason' => $request->unlock_reason,
                    'by_user' => $request->unlockedBy ? $request->unlockedBy->name : 'Unknown',
                    'data' => $request
                ]);
            } elseif ($request->status == 'dismissed') {
                $timeline->push([
                    'type' => 'dismissed',
                    'date' => $request->updated_at,
                    'by_user' => $request->dismissedBy ? $request->dismissedBy->name : 'Unknown',
                    'data' => $request
                ]);
            }
        }
        
        // Sort timeline by date (newest first)
        $timeline = $timeline->sortByDesc('date')->values();
        
        // Get statistics
        $stats = [
            'total_locks' => $restrictions->count(),
            'active_locks' => $restrictions->where('is_active', true)->count(),
            'total_unlocks' => $restrictions->where('is_active', false)->count(),
            'total_requests' => $unlockRequests->count(),
            'approved_requests' => $unlockRequests->where('status', 'unlocked')->count(),
            'dismissed_requests' => $unlockRequests->where('status', 'dismissed')->count(),
            'pending_requests' => $unlockRequests->where('status', 'pending')->count(),
        ];
        
        return view('pages.user-unlock-history', compact('targetUser', 'timeline', 'stats'));
    }
    
    public function systemStats()
    {
        // Check if user has permission to view unlock history
        $user = auth()->user();
        if (!$user->hasRole('nazim-ul-umoor') && !$user->hasRole('admin')) {
            abort(403, 'Unauthorized access to unlock history.');
        }
        
        // Get system-wide statistics
        $stats = [
            'total_locks' => UserRestriction::count(),
            'active_locks' => UserRestriction::where('is_active', true)->count(),
            'total_unlocks' => UserRestriction::where('is_active', false)->count(),
            'total_requests' => UnlockRequest::count(),
            'approved_requests' => UnlockRequest::where('status', 'unlocked')->count(),
            'dismissed_requests' => UnlockRequest::where('status', 'dismissed')->count(),
            'pending_requests' => UnlockRequest::where('status', 'pending')->count(),
            'manual_unlocks' => UserRestriction::where('was_manually_unlocked', true)->count(),
            'system_unlocks' => UserRestriction::where('is_active', false)
                ->where(function($q) {
                    $q->where('was_manually_unlocked', false)
                      ->orWhereNull('was_manually_unlocked');
                })->count(),
        ];
        
        // Get monthly statistics
        $monthlyStats = DB::table('user_restrictions')
            ->select(DB::raw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count'))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();
            
        // Get restriction types breakdown
        $restrictionTypes = DB::table('user_restrictions')
            ->select('restriction_type', DB::raw('COUNT(*) as count'))
            ->groupBy('restriction_type')
            ->orderBy('count', 'desc')
            ->get();
            
        return view('pages.unlock-system-stats', compact('stats', 'monthlyStats', 'restrictionTypes'));
    }
}