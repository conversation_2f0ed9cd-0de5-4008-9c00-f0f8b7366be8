@php
    $totalCounts = 0;
    $overallFolderCount = 0;
    $overallTotal = 0;

    // Calculate statistics from remainingFatawa data
    if (isset($remainingFatawa)) {
        foreach ($remainingFatawa as $daruliftaName => $folders) {
            foreach ($folders as $folderId => $files) {
                // Handle both arrays and collections
                $fileCount = is_array($files) ? count($files) : $files->count();
                $totalCounts += $fileCount;
                $overallFolderCount++;
            }
        }
    }

    // Calculate overall total including Talaq fatawa
    $overallTotal = $totalCounts + ($talaqFatawaCount ?? 0);

    // Calculate statistics from sendingFatawa data if selectedmufti is 'all'
    if ($selectedmufti == 'all' && isset($sendingFatawa)) {
        $totalCounts = 0;
        $overallFolderCount = 0;

        foreach ($sendingFatawa as $daruliftaName => $checkers) {
            foreach ($checkers as $checker => $dates) {
                foreach ($dates as $date => $files) {
                    // Handle both arrays and collections
                    $fileCount = is_array($files) ? count($files) : $files->count();
                    $totalCounts += $fileCount;
                    $overallFolderCount++;
                }
            }
        }
    }
@endphp

<!-- Summary Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value" id="overallTotalCount">{{ $overallTotal }}</div>
        <div class="stat-label">
            <i class="fas fa-clipboard-list me-1"></i>
            Total Remaining Fatawa
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="totalFatawaCount">{{ $totalCounts }}</div>
        <div class="stat-label">
            <i class="fas fa-file-alt me-1"></i>
            Regular Fatawa
        </div>
    </div>
    <div class="stat-card">
        <a href="{{ route('talaq-remaining') }}" class="text-decoration-none">
            <div class="stat-value text-primary" id="talaqFatawaCount">{{ $talaqFatawaCount ?? 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-heart-broken me-1"></i>
                Talaq Fatawa
            </div>
        </a>
    </div>
    <style>
    .stat-card a {
        display: block;
        width: 100%;
        height: 100%;
        color: inherit;
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }
    .stat-card a:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        text-decoration: none !important;
    }
    .stat-card a .stat-value.text-primary {
        color: #007bff !important;
    }
    .stat-card a:hover .stat-value.text-primary {
        color: #0056b3 !important;
    }
    </style>
    <div class="stat-card">
        <div class="stat-value" id="totalFolderCount">{{ $overallFolderCount }}</div>
        <div class="stat-label">
            <i class="fas fa-folder me-1"></i>
            Total Folders
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ count($daruliftaNames) }}</div>
        <div class="stat-label">
            <i class="fas fa-building me-1"></i>
            Active Daruliftaas
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ count($mujeebs) }}</div>
        <div class="stat-label">
            <i class="fas fa-users me-1"></i>
            Active Mujeebs
        </div>
    </div>
</div>
