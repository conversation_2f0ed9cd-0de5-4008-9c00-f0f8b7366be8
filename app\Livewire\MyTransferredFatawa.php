<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class MyTransferredFatawa extends Component
{
    use WithPagination;

    public $search = '';
    public $activeTab = 'received'; // 'received' or 'sent'

    protected $queryString = [
        'search' => ['except' => ''],
        'activeTab' => ['except' => 'received']
    ];

    public function mount()
    {
        // Check if user is authenticated and has mujeeb role or Darulifta role
        $userRoles = Auth::user()->roles->pluck('name')->toArray();
        $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
        $hasDaruliftaRole = !empty(array_intersect($userRoles, $daruliftaRoles));
        
        if (!Auth::check() || (!Auth::user()->isMujeeb() && !$hasDaruliftaRole)) {
            return redirect()->route('dashboard');
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function render()
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();
        $userName = $user->name;
        
        // Check if user has a Darulifta role
        $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
        $userDaruliftaRole = array_intersect($userRoles, $daruliftaRoles);
        $hasDaruliftaRole = !empty($userDaruliftaRole);

        if ($this->activeTab === 'received') {
            $query = DB::table('uploaded_files')
                ->where('checked_folder', 'Assigned')
                ->whereNotNull('transfer_by');
                
            if ($hasDaruliftaRole) {
                // User has Darulifta role - show fatawa where sender mujeeb belongs to their Darulifta
                $userDarulifta = reset($userDaruliftaRole);
                $daruliftaMujeebs = DB::table('mujeebs')
                    ->where('darul_name', $userDarulifta)
                    ->pluck('mujeeb_name')
                    ->toArray();
                    
                if (!empty($daruliftaMujeebs)) {
                    $query->whereIn('sender', $daruliftaMujeebs);
                } else {
                    $query->where('id', -1); // No results
                }
            } else {
                // Mujeeb user - show fatawa transferred TO them
                $query->where('sender', $userName);
            }
            
            $query->when($this->search, function ($query) {
                    return $query->where(function ($query) {
                        $query->where('file_code', 'like', '%' . $this->search . '%')
                            ->orWhere('transfer_by', 'like', '%' . $this->search . '%')
                            ->orWhere('darulifta_name', 'like', '%' . $this->search . '%')
                            ->orWhere('category', 'like', '%' . $this->search . '%');
                    });
                })
                ->select('uploaded_files.*')
                ->orderBy('updated_at', 'desc');

            $transferredFatawa = $query->paginate(20, ['*'], 'received');
        } else {
            $query = DB::table('uploaded_files')
                ->where('checked_folder', 'Assigned');
                
            if ($hasDaruliftaRole) {
                // User has Darulifta role - show fatawa where transfer_by mujeeb belongs to their Darulifta
                $userDarulifta = reset($userDaruliftaRole);
                $daruliftaMujeebs = DB::table('mujeebs')
                    ->where('darul_name', $userDarulifta)
                    ->pluck('mujeeb_name')
                    ->toArray();
                    
                if (!empty($daruliftaMujeebs)) {
                    $query->whereIn('transfer_by', $daruliftaMujeebs);
                } else {
                    $query->where('id', -1); // No results
                }
            } else {
                // Mujeeb user - show fatawa transferred BY them
                $query->where('transfer_by', $userName);
            }
            
            $query->when($this->search, function ($query) {
                    return $query->where(function ($query) {
                        $query->where('file_code', 'like', '%' . $this->search . '%')
                            ->orWhere('sender', 'like', '%' . $this->search . '%')
                            ->orWhere('darulifta_name', 'like', '%' . $this->search . '%')
                            ->orWhere('category', 'like', '%' . $this->search . '%');
                    });
                })
                ->select('uploaded_files.*')
                ->orderBy('updated_at', 'desc');

            $transferredFatawa = $query->paginate(20, ['*'], 'sent');
        }

        return view('livewire.my-transferred-fatawa', [
            'transferredFatawa' => $transferredFatawa
        ])->layout('layouts.app');
    }
}