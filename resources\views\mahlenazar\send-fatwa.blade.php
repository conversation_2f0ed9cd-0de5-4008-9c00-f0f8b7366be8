<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="mahlenazar"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Send Mahl-e-Nazar Fatwa"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Modern UI Styles -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <style>
            * {
                font-family: 'Inter', sans-serif;
            }

            .modern-container {
                max-width: 1000px;
                margin: 2rem auto;
                padding: 0 1rem;
            }

            .main-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
                color: white;
            }

            .card-header {
                padding: 2rem;
                text-align: center;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .card-header h1 {
                font-size: 2rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .card-body {
                padding: 2rem;
                background: white;
                color: #333;
            }

            .fatwa-info {
                background: #f8f9fa;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                border-left: 4px solid #667eea;
            }

            .fatwa-info h3 {
                color: #667eea;
                margin-bottom: 1rem;
                font-weight: 600;
            }

            .info-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }

            .info-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .info-label {
                font-weight: 600;
                color: #495057;
                min-width: 100px;
            }

            .info-value {
                color: #667eea;
                font-weight: 500;
            }

            .form-controls {
                display: grid;
                gap: 1.5rem;
            }

            .form-group {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .form-group label {
                font-weight: 600;
                color: #495057;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .form-control {
                padding: 0.75rem 1rem;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
            }

            .form-control:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }

            .file-input-label {
                cursor: pointer;
                display: block;
            }

            .file-input-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2rem;
                border-radius: 12px;
                text-align: center;
                transition: all 0.3s ease;
                border: 2px dashed rgba(255,255,255,0.3);
            }

            .file-input-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            }

            .file-upload-text {
                font-size: 1.2rem;
                font-weight: 600;
                margin-top: 0.5rem;
            }

            .file-upload-subtext {
                font-size: 0.9rem;
                opacity: 0.8;
                margin-top: 0.25rem;
            }

            input[type="file"] {
                display: none;
            }

            .submit-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1rem 2rem;
                border: none;
                border-radius: 8px;
                font-size: 1.1rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                width: 100%;
                margin-top: 1rem;
            }

            .submit-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            }

            .alert {
                padding: 1rem;
                border-radius: 8px;
                margin-bottom: 1rem;
            }

            .alert-success {
                background-color: #d4edda;
                border-color: #c3e6cb;
                color: #155724;
            }

            .alert-danger {
                background-color: #f8d7da;
                border-color: #f5c6cb;
                color: #721c24;
            }
        </style>

        <div class="modern-container">
            <div class="main-card">
                <div class="card-header">
                    <h1><i class="fas fa-paper-plane"></i> Send Mahl-e-Nazar Fatwa for Checking</h1>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-weight: 400;">Send your Mahl-e-Nazar fatwa to a checker</p>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if(session('fileErrors'))
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach(session('fileErrors') as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Fatwa Information -->
                    <div class="fatwa-info">
                        <h3><i class="fas fa-info-circle"></i> Fatwa Information</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Fatwa Code:</span>
                                <span class="info-value">{{ $fatwa->file_code }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Sender:</span>
                                <span class="info-value">{{ $fatwa->sender }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Darulifta:</span>
                                <span class="info-value">{{ $fatwa->darulifta_name }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Original Date:</span>
                                <span class="info-value">{{ $fatwa->mail_folder_date }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Category:</span>
                                <span class="info-value">{{ $fatwa->category ?? 'N/A' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">File Type:</span>
                                <span class="info-value">Mahl-e-Nazar</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Original File Name:</span>
                                <span class="info-value" style="color: #e74c3c; font-weight: bold;">{{ $fatwa->file_name }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Send Form -->
                    <form action="{{ route('send-mahlenazar-fatwa.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="fatwa_id" value="{{ $fatwa->id }}">
                        
                        <div class="form-controls">
                            <div class="form-group">
                                <label for="checker"><i class="fas fa-user-check"></i> Select Checker</label>
                                <select class="form-control" id="checker" name="checker" required>
                                    <option value="">Choose a checker...</option>
                                    @foreach($checkers as $checker)
                                        <option value="{{ $checker->folder_id }}"
                                                {{ $checker->folder_id == 'mufti_ali_asghar' ? 'selected' : '' }}>
                                            {{ $checker->checker_name }}
                                           
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="transfer_by"><i class="fas fa-user"></i> Transfer By</label>
                                <input type="text" class="form-control" id="transfer_by" name="transfer_by" 
                                       value="{{ Auth::user()->name }}" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="folder_date"><i class="fas fa-calendar-alt"></i> Folder Date</label>
                                <input type="date" class="form-control" id="folder_date" name="folder_date" 
                                       value="{{ date('Y-m-d') }}" required>
                            </div>
                            
                            <div class="form-group">
                                <div class="file-requirement-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                                    <i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>
                                    <strong>Important:</strong> The uploaded file name must exactly match: <code style="background: #f8f9fa; padding: 2px 5px; border-radius: 3px;">{{ $fatwa->file_name }}</code>
                                </div>
                                <div class="file-input-label">
                                    <div class="file-input-button" id="fileDropArea">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <div class="file-upload-text">Upload Fatwa Files</div>
                                        <div class="file-upload-subtext">Drag & drop files or click to browse (PDF, DOC, DOCX)</div>
                                    </div>
                                    <input type="file" id="files" name="files[]" multiple required accept=".pdf,.doc,.docx" style="display: none;">
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="submit-button">
                            <i class="fas fa-paper-plane"></i> Send Fatwa for Checking
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <script>
            // File upload drag and drop functionality
            document.addEventListener('DOMContentLoaded', function() {
                const fileDropArea = document.getElementById('fileDropArea');
                const fileInput = document.getElementById('files');

                fileDropArea.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    fileInput.click();
                });

                fileDropArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    fileDropArea.style.backgroundColor = 'rgba(255,255,255,0.1)';
                });

                fileDropArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    fileDropArea.style.backgroundColor = '';
                });

                fileDropArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    fileDropArea.style.backgroundColor = '';
                    
                    const files = e.dataTransfer.files;
                    fileInput.files = files;
                    updateFileDisplay();
                });

                fileInput.addEventListener('change', updateFileDisplay);

                function updateFileDisplay() {
                    const files = fileInput.files;
                    if (files.length > 0) {
                        const fileText = files.length === 1 ? 
                            `Selected: ${files[0].name}` : 
                            `Selected ${files.length} files`;
                        document.querySelector('.file-upload-text').textContent = fileText;
                    }
                }
            });
        </script>
    </main>
</x-layout>
