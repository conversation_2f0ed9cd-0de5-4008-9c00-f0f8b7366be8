<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Task Management</h5>
                            <p class="text-sm mb-0">Assign and manage daily/weekly tasks</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <button wire:click="openCreateModal" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-plus"></i>&nbsp;&nbsp;Create Task
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4">
                        <div class="col-md-3">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search tasks...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterStatus" class="form-select">
                                    <option value="all">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterType" class="form-select">
                                    <option value="all">All Types</option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="operational">Operational</option>
                                    <option value="one-time">One-time</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterAssignedTo" class="form-select">
                                    <option value="all">All Users</option>
                                    @foreach($availableUsers as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select wire:model.live="filterDepartment" class="form-select">
                                    <option value="all">All Departments</option>
                                    @foreach($departments as $department)
                                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tasks Table -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Assigned To</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Type</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tasks as $task)
                                <tr class="{{ $task->isOverdue() ? 'table-warning' : '' }}">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                                @if($task->department)
                                                    <span class="badge badge-sm bg-gradient-info">{{ $task->department->name }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->assignedTo->name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $task->assignedTo->email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center text-sm">
                                        <span class="badge badge-sm bg-gradient-{{ $task->type === 'daily' ? 'primary' : 'secondary' }}">
                                            {{ ucfirst($task->type) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm {{ $this->getPriorityBadgeClass($task->priority) }}">
                                            {{ $this->getPriorityText($task->priority) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $task->due_date->format('M d, Y') }}
                                        </span>
                                        @if($task->isOverdue())
                                            <br><small class="text-danger">Overdue</small>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm {{ $this->getStatusBadgeClass($task->status) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button wire:click="openViewModal({{ $task->id }})" 
                                                    class="btn btn-sm btn-outline-info mb-0" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @can('update', $task)
                                            <button wire:click="openEditModal({{ $task->id }})" 
                                                    class="btn btn-sm btn-outline-primary mb-0" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            @endcan
                                            @if($task->status === 'pending' && $task->assigned_to === auth()->id())
                                            <button wire:click="changeStatus({{ $task->id }}, 'in_progress')" 
                                                    class="btn btn-sm btn-outline-warning mb-0" title="Start Task">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            @endif
                                            @if($task->status === 'in_progress' && $task->assigned_to === auth()->id())
                                            <button wire:click="markCompleted({{ $task->id }})" 
                                                    class="btn btn-sm btn-outline-success mb-0" title="Complete">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            @endif
                                            @can('delete', $task)
                                            <button wire:click="deleteTask({{ $task->id }})" 
                                                    wire:confirm="Are you sure you want to delete this task?"
                                                    class="btn btn-sm btn-outline-danger mb-0" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <p class="text-secondary mb-0">No tasks found.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="px-4">
                        {{ $tasks->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Create Task Modal -->
    @if($showCreateModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Task</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="createTask">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror">
                                    @error('title') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Priority *</label>
                                    <select wire:model="priority" class="form-select @error('priority') is-invalid @enderror">
                                        <option value="1">Low</option>
                                        <option value="2">Medium</option>
                                        <option value="3">High</option>
                                    </select>
                                    @error('priority') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control @error('description') is-invalid @enderror" rows="3"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Type *</label>
                                    <select wire:model="type" class="form-select @error('type') is-invalid @enderror">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="operational">Operational</option>
                                        <option value="one-time">One-time</option>
                                    </select>
                                    @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                        <option value="">Select User</option>
                                        @foreach($availableUsers as $user)
                                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Due Date *</label>
                                    <input type="date" wire:model="due_date" class="form-control @error('due_date') is-invalid @enderror">
                                    @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Department</label>
                            <select wire:model="department_id" class="form-select @error('department_id') is-invalid @enderror">
                                <option value="">Select Department (Optional)</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}">{{ $department->name }}</option>
                                @endforeach
                            </select>
                            @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="createTask">Create Task</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Edit Task Modal -->
    @if($showEditModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Task</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="updateTask">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror">
                                    @error('title') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Priority *</label>
                                    <select wire:model="priority" class="form-select @error('priority') is-invalid @enderror">
                                        <option value="1">Low</option>
                                        <option value="2">Medium</option>
                                        <option value="3">High</option>
                                    </select>
                                    @error('priority') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control @error('description') is-invalid @enderror" rows="3"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Type *</label>
                                    <select wire:model="type" class="form-select @error('type') is-invalid @enderror">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="operational">Operational</option>
                                        <option value="one-time">One-time</option>
                                    </select>
                                    @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Status *</label>
                                    <select wire:model="status" class="form-select @error('status') is-invalid @enderror">
                                        <option value="pending">Pending</option>
                                        <option value="in_progress">In Progress</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                    @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                        <option value="">Select User</option>
                                        @foreach($availableUsers as $user)
                                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Due Date *</label>
                                    <input type="date" wire:model="due_date" class="form-control @error('due_date') is-invalid @enderror">
                                    @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Department</label>
                                    <select wire:model="department_id" class="form-select @error('department_id') is-invalid @enderror">
                                        <option value="">Select Department (Optional)</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}">{{ $department->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Completion Notes</label>
                                    <textarea wire:model="completion_notes" class="form-control" rows="2" placeholder="Add notes about task completion..."></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="updateTask">Update Task</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- View Task Modal -->
    @if($showViewModal && $selectedTask)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Task Details</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-primary">{{ $selectedTask->title }}</h6>
                            @if($selectedTask->description)
                                <p class="text-sm text-secondary">{{ $selectedTask->description }}</p>
                            @endif
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge badge-lg {{ $this->getStatusBadgeClass($selectedTask->status) }}">
                                {{ ucfirst(str_replace('_', ' ', $selectedTask->status)) }}
                            </span>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Assigned To</small>
                                <p class="mb-0">{{ $selectedTask->assignedTo->name }}</p>
                                <small class="text-muted">{{ $selectedTask->assignedTo->email }}</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Assigned By</small>
                                <p class="mb-0">{{ $selectedTask->assignedBy->name }}</p>
                                <small class="text-muted">{{ $selectedTask->created_at->format('M d, Y h:i A') }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Type</small>
                                <p class="mb-0">
                                    <span class="badge bg-gradient-{{ $selectedTask->type === 'daily' ? 'primary' : 'secondary' }}">
                                        {{ ucfirst($selectedTask->type) }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Priority</small>
                                <p class="mb-0">
                                    <span class="badge {{ $this->getPriorityBadgeClass($selectedTask->priority) }}">
                                        {{ $this->getPriorityText($selectedTask->priority) }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Due Date</small>
                                <p class="mb-0">{{ $selectedTask->due_date->format('M d, Y') }}</p>
                                @if($selectedTask->isOverdue())
                                    <small class="text-danger">Overdue</small>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Department</small>
                                <p class="mb-0">
                                    @if($selectedTask->department)
                                        <span class="badge bg-gradient-info">{{ $selectedTask->department->name }}</span>
                                    @else
                                        <span class="text-muted">Not assigned</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($selectedTask->completion_notes)
                    <div class="row">
                        <div class="col-12">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Completion Notes</small>
                                <p class="mb-0">{{ $selectedTask->completion_notes }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($selectedTask->completed_at)
                    <div class="row">
                        <div class="col-12">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Completed At</small>
                                <p class="mb-0">{{ $selectedTask->completed_at->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                    @can('update', $selectedTask)
                        <button type="button" class="btn btn-primary" wire:click="openEditModal({{ $selectedTask->id }})">Edit Task</button>
                    @endcan
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
