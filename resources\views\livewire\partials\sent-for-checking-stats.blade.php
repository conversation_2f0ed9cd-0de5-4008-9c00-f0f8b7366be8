{{-- Sent For Checking Statistics Partial --}}

@php
    $totalCounts = 0;
    $overallFolderCount = 0;
    
    // Calculate totals from sendingFatawa data
    if ($selectedmufti == 'all') {
        foreach($daruliftaNames as $daruliftaName) {
            if(isset($sendingFatawa[$daruliftaName])) {
                foreach($sendingFatawa[$daruliftaName] as $checked => $dates) {
                    $daruliftaTotalCounts = 0;
                    $folderCounts = [];
                    
                    foreach($dates as $date => $fatawas) {
                        $count = count($fatawas);
                        $daruliftaTotalCounts += $count;
                        $totalCounts += $count;
                        
                        if (!isset($folderCounts[$date])) {
                            $folderCounts[$date] = 0;
                            $overallFolderCount++;
                        }
                    }
                }
            }
        }
    } else {
        foreach($daruliftaNames as $daruliftaName) {
            if(isset($sendingFatawa[$daruliftaName])) {
                $daruliftaTotalCounts = 0;
                $folderCounts = [];
                
                foreach($sendingFatawa[$daruliftaName] as $date => $fatawas) {
                    $count = count($fatawas);
                    $daruliftaTotalCounts += $count;
                    $totalCounts += $count;
                    
                    if (!isset($folderCounts[$date])) {
                        $folderCounts[$date] = 0;
                        $overallFolderCount++;
                    }
                }
            }
        }
    }
@endphp

<!-- Summary Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value" id="totalFatawaCount">{{ $totalCounts }}</div>
        <div class="stat-label">
            <i class="fas fa-paper-plane me-1"></i>
            Total Sent Fatawa
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="totalFolderCount">{{ $overallFolderCount }}</div>
        <div class="stat-label">
            <i class="fas fa-folder me-1"></i>
            Total Folders
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ count($daruliftaNames) }}</div>
        <div class="stat-label">
            <i class="fas fa-building me-1"></i>
            Active Daruliftaas
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ count($mujeebs) }}</div>
        <div class="stat-label">
            <i class="fas fa-users me-1"></i>
            Active Mujeebs
        </div>
    </div>
    @if ($selectedmufti != 'all')
        <div class="stat-card">
            <div class="stat-value">1</div>
            <div class="stat-label">
                <i class="fas fa-user-check me-1"></i>
                Selected Mufti
            </div>
        </div>
    @else
        <div class="stat-card">
            <div class="stat-value">{{ count($checkerlist) }}</div>
            <div class="stat-label">
                <i class="fas fa-user-check me-1"></i>
                Total Muftis
            </div>
        </div>
    @endif
</div>

<!-- Quick Actions -->
<div class="quick-actions">
    <div class="action-buttons">
        @if(Auth::user()->hasRole('Admin') || Auth::user()->hasRole('SuperAdmin'))
            <a href="{{ route('create') }}" class="btn-modern btn-success-modern">
                <i class="fas fa-plus me-2"></i>
                Send New Fatwa Folder
            </a>
        @endif
        
        <button type="button" class="btn-modern btn-info-modern" onclick="refreshData()">
            <i class="fas fa-sync-alt me-2"></i>
            Refresh Data
        </button>
        
        <button type="button" class="btn-modern btn-secondary-modern" onclick="exportData()">
            <i class="fas fa-download me-2"></i>
            Export Report
        </button>
    </div>
</div>

<!-- Filter Summary -->
<div class="filter-summary">
    <div class="filter-tags">
        @if($selectedmujeeb !== 'all')
            <span class="filter-tag">
                <i class="fas fa-user me-1"></i>
                Mujeeb: {{ $selectedmujeeb }}
                <a href="{{ request()->fullUrlWithQuery(['selectedmujeeb' => 'all']) }}" class="remove-filter">×</a>
            </span>
        @endif
        
        @if($selectedmufti !== 'all')
            <span class="filter-tag">
                <i class="fas fa-user-tie me-1"></i>
                Mufti: 
                @foreach($checkerlist as $checker)
                    @if($checker->folder_id == $selectedmufti)
                        {{ $checker->checker_name }}
                        @break
                    @endif
                @endforeach
                <a href="{{ request()->fullUrlWithQuery(['selectedmufti' => 'all']) }}" class="remove-filter">×</a>
            </span>
        @endif
        
        @if($selectedTimeFrame !== 'this_month')
            <span class="filter-tag">
                <i class="fas fa-calendar me-1"></i>
                Time: {{ ucfirst(str_replace('_', ' ', $selectedTimeFrame)) }}
                <a href="{{ request()->fullUrlWithQuery(['selectedTimeFrame' => 'this_month']) }}" class="remove-filter">×</a>
            </span>
        @endif
        
        @if($selectedexclude !== 'exclude_checked')
            <span class="filter-tag">
                <i class="fas fa-filter me-1"></i>
                Show: All Sent
                <a href="{{ request()->fullUrlWithQuery(['selectedexclude' => 'exclude_checked']) }}" class="remove-filter">×</a>
            </span>
        @endif
    </div>
</div>
