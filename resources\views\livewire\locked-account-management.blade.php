<div>


    <!-- Header with Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-lock me-2 text-danger"></i>
                                Locked Account Management
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Manage and unlock restricted user accounts</p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-gradient-danger badge-lg">
                                {{ $stats['total_locked'] }} Locked
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div class="row">
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Locked</p>
                                                <h5 class="font-weight-bolder mb-0">{{ $stats['total_locked'] }}</h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-danger shadow-danger text-center border-radius-md">
                                                <i class="fas fa-lock text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Performance</p>
                                                <h5 class="font-weight-bolder mb-0">{{ $stats['performance_locked'] }}</h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                                <i class="fas fa-clipboard-check text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Fatawa Limit</p>
                                                <h5 class="font-weight-bolder mb-0">{{ $stats['fatawa_locked'] }}</h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                                <i class="fas fa-exclamation-triangle text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Manual Lock</p>
                                                <h5 class="font-weight-bolder mb-0">{{ $stats['manual_locked'] }}</h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-dark shadow text-center border-radius-md">
                                                <i class="fas fa-user-lock text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-4">
            <input type="text" wire:model.live="search" class="form-control" placeholder="Search by name or email...">
        </div>
        <div class="col-md-4">
            <select class="form-select" wire:model.live="filterType">
                <option value="">All Restriction Types</option>
                @foreach($restrictionTypes as $type => $label)
                    <option value="{{ $type }}">{{ $label }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-4">
            <div class="text-end">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {{ $lockedUsers->total() }} locked accounts
                </small>
            </div>
        </div>
    </div>

    <!-- Locked Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Restrictions</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Locked Since</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Details</th>
                                    <th class="text-secondary opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($lockedUsers as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $user->name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $user->email }}</p>
                                                <div class="mt-1">
                                                    @foreach($user->roles as $role)
                                                        <span class="badge badge-sm bg-gradient-info">{{ $role->name }}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @foreach($user->activeRestrictions as $restriction)
                                        <div class="mb-1">
                                            <span class="badge badge-sm {{ $this->getRestrictionBadgeClass($restriction->restriction_type) }}">
                                                <i class="{{ $this->getRestrictionIcon($restriction->restriction_type) }} me-1"></i>
                                                {{ $restrictionTypes[$restriction->restriction_type] ?? $restriction->restriction_type }}
                                            </span>
                                        </div>
                                        @endforeach
                                    </td>
                                    <td>
                                        @php
                                            $earliestRestriction = $user->activeRestrictions->sortBy('restricted_at')->first();
                                        @endphp
                                        @if($earliestRestriction)
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $earliestRestriction->restricted_at->format('M d, Y') }}</h6>
                                            <p class="text-xs text-secondary mb-0">{{ $earliestRestriction->restricted_at->diffForHumans() }}</p>
                                        </div>
                                        @endif
                                    </td>
                                    <td>
                                        @foreach($user->activeRestrictions as $restriction)
                                        <div class="mb-2">
                                            <p class="text-xs mb-0"><strong>Reason:</strong></p>
                                            <p class="text-xs text-secondary mb-1">{{ Str::limit($restriction->reason, 50) }}</p>
                                            
                                            @if($restriction->restriction_type === 'performance_not_submitted')
                                                @php
                                                    $missedDays = $this->getMissedPerformanceDays($user);
                                                @endphp
                                                @if(!empty($missedDays))
                                                <p class="text-xs text-danger mb-0">
                                                    <i class="fas fa-calendar-times me-1"></i>
                                                    {{ count($missedDays) }} missed day(s)
                                                </p>
                                                @endif
                                            @endif
                                        </div>
                                        @endforeach
                                    </td>
                                    <td class="align-middle">
                                        <button class="btn btn-sm bg-gradient-success"
                                                wire:click="unlockUser({{ $user->id }})"
                                                type="button">
                                            <i class="fas fa-unlock me-1"></i>
                                            Unlock Account
                                        </button>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-unlock-alt fa-3x text-success mb-3"></i>
                                            <h6 class="text-secondary">No locked accounts found</h6>
                                            <p class="text-sm text-secondary">All user accounts are currently active.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>



                @if($lockedUsers->hasPages())
                <div class="card-footer">
                    {{ $lockedUsers->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Unlock Modal -->
    @if($showUnlockModal && $selectedUser)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Unlock Account - {{ $selectedUser->name }}</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Current Restrictions:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($selectedUser->activeRestrictions as $restriction)
                            <li>{{ $restrictionTypes[$restriction->restriction_type] ?? $restriction->restriction_type }}</li>
                            @endforeach
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Reason for Unlocking *</label>
                        <textarea class="form-control @error('unlockReason') is-invalid @enderror" 
                                  wire:model="unlockReason" rows="4" 
                                  placeholder="Please provide a detailed reason for unlocking this account..."></textarea>
                        @error('unlockReason') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Unlocking this account will remove all active restrictions and allow the user to access the system immediately.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Cancel</button>
                    <button type="button" class="btn btn-success" wire:click="unlockAccount">
                        <i class="fas fa-unlock me-1"></i>
                        Unlock Account
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif



    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('debug'))
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <strong>Debug:</strong> {{ session('debug') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif
</div>


