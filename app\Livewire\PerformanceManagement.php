<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\DailyPerformance;
use App\Models\DailyPerformanceAttachment;
use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use App\Models\UserRestriction;
use App\Models\PerformanceHoliday;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class PerformanceManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    protected $paginationTheme = 'custom';

    public $selectedDate;
    public $selectedStartDate;
    public $selectedEndDate;
    public $dateRangeMode = 'range'; // single or range - default to range for month view
    public $selectedUser = 'all';
    public $selectedDepartment = 'all';
    public $selectedStatus = 'all';
    public $selectedRating = 'all';
    public $viewMode = 'hierarchical'; // hierarchical or list
    
    public $showViewModal = false;
    public $selectedPerformance = null;
    public $showAttachmentsModal = false;
    public $selectedAttachments = [];
    public $showRatingModal = false;
    public $selectedPerformanceForRating = null;
    public $superiorRating = '';
    public $superiorComments = '';
    
    public $showResetModal = false;
    public $selectedUserForReset = null;
    public $resetReason = '';
    
    public $users = [];
    public $departments = [];
    public $statistics = [];
    public $hierarchicalData = [];
    public $summaryData = []; // For header summary cards

    public function mount()
    {
        // Check if user has permission to view performance (role-based)
        $user = auth()->user();
        if (!$user->hasRole('nazim-ul-umoor') && !$user->isSuperior() && !$user->isMujeeb()) {
            abort(403, 'Unauthorized access to performance management.');
        }

        // Default to current month
        $this->selectedStartDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->selectedEndDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        $this->selectedDate = Carbon::today()->format('Y-m-d');
        $this->loadData();
        $this->loadStatistics();
        $this->loadHierarchicalData();
        $this->loadSummaryData();
    }



    public function render()
    {
        $performances = $this->getPerformances();

        return view('livewire.performance-management', [
            'performances' => $performances,
            'summaryData' => $this->summaryData,
        ]);
    }





    public function loadData()
    {
        $user = auth()->user();

        // Role-based user visibility with proper mixed role handling
        if ($user->hasRole('nazim-ul-umoor')) {
            // Nazim-ul-umoor can see all users
            $this->users = User::select('id', 'name', 'email')->orderBy('name')->get();
            $this->departments = Department::active()->select('id', 'name')->orderBy('name')->get();
        } elseif ($user->isSuperior()) {
            // Superior can see performance based on department-specific relationships
            // They can see:
            // 1. Their own performance (always)
            // 2. Performance of users who are assistants in departments where they are supervisor
            // 3. Performance of their global assistants (but NOT in departments where the assistant is their supervisor)
            
            $userIds = collect([$user->id]); // Always include themselves
            
            // Add assistants from departments where this user is a Superior
            if ($user->supervisorDepartments()->exists()) {
                foreach ($user->supervisorDepartments()->get() as $dept) {
                    // Get users who are ASSISTANTS in this department (not other supervisors)
                    $assistantsInDept = \App\Models\DepartmentSupervisorAssistant::where('department_id', $dept->id)
                        ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                        ->where('is_active', true)
                        ->pluck('user_id');
                    
                    $userIds = $userIds->merge($assistantsInDept);
                }
            }
            
            // Add global assistants, but exclude them from departments where they are supervisors of current user
            $globalAssistantIds = $user->assistants->pluck('id');
            foreach ($globalAssistantIds as $assistantId) {
                $assistant = User::find($assistantId);
                if ($assistant) {
                    // Check if this assistant is a supervisor of current user in any department
                    $isAssistantSupervisorOfUser = \App\Models\DepartmentSupervisorAssistant::where('user_id', $assistantId)
                        ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
                        ->where('is_active', true)
                        ->whereIn('department_id', function($query) use ($user) {
                            $query->select('department_id')
                                ->from('department_supervisor_assistants')
                                ->where('user_id', $user->id)
                                ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                                ->where('is_active', true);
                        })
                        ->exists();
                    
                    // Only add if they're not a supervisor of current user in any shared department
                    if (!$isAssistantSupervisorOfUser) {
                        $userIds->push($assistantId);
                    }
                }
            }
            
            $userIds = $userIds->unique();

            $this->users = User::whereIn('id', $userIds)
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();

            // Show departments where they are Superior
            if ($user->supervisorDepartments()->exists()) {
                $this->departments = $user->supervisorDepartments()
                    ->where('departments.is_active', true)
                    ->select('departments.id', 'departments.name')
                    ->orderBy('departments.name')
                    ->get();
            } else {
                $this->departments = Department::active()->select('id', 'name')->orderBy('name')->get();
            }
        } else {
            // Assistant-only users can only see themselves
            $this->users = collect([$user]);
            $this->departments = collect();
        }
    }

    public function loadStatistics()
    {
        $authUser = auth()->user();
        $startDate = Carbon::parse($this->selectedStartDate)->startOfDay();
        $endDate = Carbon::parse($this->selectedEndDate)->endOfDay();

        // Role-based user filtering with proper mixed role handling
        if ($authUser->hasRole('nazim-ul-umoor')) {
            // Nazim-ul-umoor can see all users with tasks
            $usersQuery = User::whereHas('assignedTasks', function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            });
        } elseif ($authUser->isSuperior()) {
            // Superior can see performance based on department-specific relationships
            // They can see:
            // 1. Their own performance (always)
            // 2. Performance of users in departments where they are supervisor
            // 3. Performance of their global assistants (for backward compatibility)
            
            $userIds = collect([$authUser->id]); // Always include themselves
            
            // Add users from departments where this user is a Superior
            if ($authUser->supervisorDepartments()->exists()) {
                $supervisorDepartmentIds = $authUser->supervisorDepartments()->pluck('departments.id');
                
                // Get all users who have tasks in departments where current user is supervisor
                $departmentUserIds = User::whereHas('assignedTasks', function($q) use ($supervisorDepartmentIds) {
                    $q->whereIn('department_id', $supervisorDepartmentIds)
                      ->whereNotIn('status', ['completed', 'cancelled']);
                })->pluck('id');
                
                $userIds = $userIds->merge($departmentUserIds);
            }
            
            // Add global assistants for backward compatibility (but only if they're not supervisors in other departments)
            $globalAssistantIds = $authUser->assistants->pluck('id');
            $userIds = $userIds->merge($globalAssistantIds);
            
            $userIds = $userIds->unique();
            
            $usersQuery = User::whereIn('id', $userIds)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                });
        } else {
            // Assistant-only users can only see themselves
            $usersQuery = User::where('id', $authUser->id)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                });
        }

        // Apply filters
        if ($this->selectedUser !== 'all') {
            $usersQuery->where('id', $this->selectedUser);
        }

        $users = $usersQuery->with(['assignedTasks' => function ($q) {
            $q->whereNotIn('status', ['completed', 'cancelled'])->with('department');
        }])->get();

        $totalUsers = $users->count();
        $expectedReports = 0;
        $submittedCount = 0;
        $pendingCount = 0;
        $overdueCount = 0;
        $totalHours = 0;
        $submittedReports = 0;

        foreach ($users as $user) {
            foreach ($user->assignedTasks as $task) {
                // Department filter
                if ($this->selectedDepartment !== 'all' && optional($task->department)->id != $this->selectedDepartment) {
                    continue;
                }

                $taskStartDate = Carbon::parse($task->created_at)->startOfDay();
                $taskEndDate = in_array($task->status, ['completed', 'cancelled'])
                    ? Carbon::parse($task->updated_at)->startOfDay()
                    : Carbon::today();

                $current = max($startDate, $taskStartDate);
                $endForTask = min($endDate, $taskEndDate);

                while ($current->lte($endForTask)) {
                    // Only count working days (not holidays/Sundays)
                    if (!\App\Models\PerformanceHoliday::requiresPerformance($current->format('Y-m-d'))) {
                        $current->addDay();
                        continue;
                    }

                    $expectedReports++;

                    $performance = DailyPerformance::where('user_id', $user->id)
                        ->where('task_id', $task->id)
                        ->where('performance_date', $current->format('Y-m-d'))
                        ->first();

                    if ($performance && $performance->is_submitted) {
                        $submittedCount++;
                        $submittedReports++;
                        $totalHours += $performance->hours_worked ?? 0;
                    } else {
                        if ($current->lt(Carbon::today())) {
                            $overdueCount++;
                        } else {
                            $pendingCount++;
                        }
                    }

                    $current->addDay();
                }
            }
        }

        $submissionRate = $expectedReports > 0 ? round(($submittedCount / $expectedReports) * 100, 1) : 0;
        $avgHoursWorked = $submittedReports > 0 ? round($totalHours / $submittedReports, 1) : 0;

        $this->statistics = [
            'total_users' => $totalUsers,
            'monthly_submitted' => $submittedCount,
            'monthly_pending' => $pendingCount,
            'monthly_overdue' => $overdueCount,
            'submission_rate' => $submissionRate,
            'avg_hours_worked' => $avgHoursWorked,
        ];
    }

    public function loadHierarchicalData()
    {
        $user = auth()->user();
        $startDate = Carbon::parse($this->selectedStartDate);
        $endDate = Carbon::parse($this->selectedEndDate);
        
        // Role-based user filtering with proper mixed role handling
        if ($user->hasRole('nazim-ul-umoor')) {
            // Nazim-ul-umoor can see all users with tasks
            $usersWithTasksQuery = User::whereHas('assignedTasks', function($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            });
        } elseif ($user->isSuperior()) {
            // Superior can see performance based on department-specific relationships
            // They can see:
            // 1. Their own performance (always)
            // 2. Performance of users who are assistants in departments where they are supervisor
            // 3. Performance of their global assistants (but NOT in departments where the assistant is their supervisor)
            
            $userIds = collect([$user->id]); // Always include themselves
            
            // Add assistants from departments where this user is a Superior
            if ($user->supervisorDepartments()->exists()) {
                foreach ($user->supervisorDepartments()->get() as $dept) {
                    // Get users who are ASSISTANTS in this department (not other supervisors)
                    $assistantsInDept = \App\Models\DepartmentSupervisorAssistant::where('department_id', $dept->id)
                        ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                        ->where('is_active', true)
                        ->pluck('user_id');
                    
                    $userIds = $userIds->merge($assistantsInDept);
                }
            }
            
            // Add global assistants, but exclude them from departments where they are supervisors of current user
            $globalAssistantIds = $user->assistants->pluck('id');
            foreach ($globalAssistantIds as $assistantId) {
                $assistant = User::find($assistantId);
                if ($assistant) {
                    // Check if this assistant is a supervisor of current user in any department
                    $isAssistantSupervisorOfUser = \App\Models\DepartmentSupervisorAssistant::where('user_id', $assistantId)
                        ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
                        ->where('is_active', true)
                        ->whereIn('department_id', function($query) use ($user) {
                            $query->select('department_id')
                                ->from('department_supervisor_assistants')
                                ->where('user_id', $user->id)
                                ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                                ->where('is_active', true);
                        })
                        ->exists();
                    
                    // Only add if they're not a supervisor of current user in any shared department
                    if (!$isAssistantSupervisorOfUser) {
                        $userIds->push($assistantId);
                    }
                }
            }
            
            $userIds = $userIds->unique();
            
            $usersWithTasksQuery = User::whereIn('id', $userIds)
                ->whereHas('assignedTasks', function($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                });
        } else {
            // Assistant-only users can only see themselves
            $usersWithTasksQuery = User::where('id', $user->id)
                ->whereHas('assignedTasks', function($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                });
        }
        
        if ($this->selectedUser !== 'all') {
            $usersWithTasksQuery->where('id', $this->selectedUser);
        }
        
        $usersWithTasks = $usersWithTasksQuery->with(['assignedTasks' => function($q) {
            $q->whereNotIn('status', ['completed', 'cancelled'])
              ->with('department');
        }])->get();
        
        // Group users by their task departments
        $departmentUsers = [];

        foreach ($usersWithTasks as $userWithTasks) {
            // Handle users with assigned tasks
            if ($userWithTasks->assignedTasks->isNotEmpty()) {
                foreach ($userWithTasks->assignedTasks as $task) {
                // Skip invalid tasks
                if (!$task || !$task->id) {
                    continue;
                }
                
                $taskDepartment = $task->department;
                
                // Skip tasks without departments
                if (!$taskDepartment || !$taskDepartment->id) {
                    continue;
                }
                
                // Skip if department filter is applied and doesn't match
                if ($this->selectedDepartment !== 'all' && $taskDepartment->id != $this->selectedDepartment) {
                    continue;
                }
                
                if (!isset($departmentUsers[$taskDepartment->id])) {
                    $departmentUsers[$taskDepartment->id] = [
                        'department' => $taskDepartment,
                        'users' => []
                    ];
                }
                
                // Check if user already exists in this department
                $existingUserIndex = null;
                foreach ($departmentUsers[$taskDepartment->id]['users'] as $index => $existingUser) {
                    if ($existingUser['user']->id === $userWithTasks->id) {
                        $existingUserIndex = $index;
                        break;
                    }
                }
                
                if ($existingUserIndex === null) {
                    // Add new user to this department
                    $userData = [
                        'user' => $userWithTasks,
                        'tasks' => []
                    ];
                    $departmentUsers[$taskDepartment->id]['users'][] = $userData;
                    $existingUserIndex = count($departmentUsers[$taskDepartment->id]['users']) - 1;
                }
                
                // Add task to user
                $taskData = [
                    'task' => $task,
                    'performances' => []
                ];
                
                // Get performance records for this task in the date range
                $existingPerformances = DailyPerformance::where('user_id', $userWithTasks->id)
                    ->where('task_id', $task->id)
                    ->with('attachments')
                    ->get()
                    ->keyBy(function($performance) {
                        return Carbon::parse($performance->performance_date)->format('Y-m-d');
                    });

                // Create complete performance collection including missing days and overdue entries
                $performances = collect();
                $currentDate = Carbon::parse($task->created_at)->startOfDay();
                $taskEndDate = in_array($task->status, ['completed', 'cancelled'])
                    ? Carbon::parse($task->updated_at)->startOfDay()
                    : Carbon::today();
                
                // Check if user is viewing current month (default behavior)
                $isViewingCurrentMonth = $startDate->isSameMonth(Carbon::now()) && $endDate->isSameMonth(Carbon::now());

                while ($currentDate->lte($taskEndDate)) {
                    // Skip this date if it's a holiday or Sunday (non-working day)
                    if (!\App\Models\PerformanceHoliday::requiresPerformance($currentDate->format('Y-m-d'))) {
                        $currentDate->addDay();
                        continue;
                    }

                    $dateKey = $currentDate->format('Y-m-d');
                    $isInFilterRange = $currentDate->gte($startDate) && $currentDate->lte($endDate);
                    
                    // Apply same filtering logic as list view
                    $shouldInclude = false;
                    if ($isInFilterRange) {
                        $shouldInclude = true;
                    } elseif ($isViewingCurrentMonth && $currentDate->lt(Carbon::today())) {
                        // When viewing current month, also show previous overdue entries
                        $shouldInclude = true;
                    }

                    if ($shouldInclude) {
                        if (isset($existingPerformances[$dateKey])) {
                            $existingPerformance = $existingPerformances[$dateKey];
                            
                            // When viewing current month, only include previous month performances if they are overdue
                            if ($isViewingCurrentMonth && $currentDate->lt(Carbon::today())) {
                                // This is a previous date when viewing current month
                                // Only include if it's NOT submitted (i.e., it's overdue)
                                if (!$existingPerformance->is_submitted) {
                                    $performances->push($existingPerformance);
                                }
                                // Skip submitted performances from previous dates when viewing current month
                            } else {
                                // This is within the selected date range, include all performances
                                $performances->push($existingPerformance);
                            }
                        } else {
                            // Create overdue entry for missing performance
                            $isOverdue = $currentDate->lt(Carbon::today());
                            if ($isOverdue) {
                                $overduePerformance = (object)[
                                    'id' => null,
                                    'user_id' => $userWithTasks->id,
                                    'task_id' => $task->id,
                                    'performance_date' => $dateKey,
                                    'is_submitted' => false,
                                    'hours_worked' => 0,
                                    'overall_rating' => null,
                                    'submitted_at' => null,
                                    'is_overdue' => true,
                                    'tasks_completed' => null,
                                    'challenges_faced' => null,
                                    'next_day_plan' => null,
                                    'additional_notes' => null,
                                    'superior_rating' => null,
                                    'superior_comments' => null,
                                    'attachments' => collect(),
                                ];
                                $performances->push($overduePerformance);
                            }
                        }
                    }
                    $currentDate->addDay();
                }

                // Sort by date descending
                $performances = $performances->sortByDesc('performance_date');
                
                // Apply status filter
                if ($this->selectedStatus !== 'all') {
                    $performances = $performances->filter(function($perf) {
                        $isSubmitted = is_object($perf) ? ($perf->is_submitted ?? false) : $perf->is_submitted;
                        $isOverdue = is_object($perf) ? ($perf->is_overdue ?? false) : (property_exists($perf, 'is_overdue') ? $perf->is_overdue : false);
                        
                        if ($this->selectedStatus === 'submitted') {
                            return $isSubmitted;
                        } elseif ($this->selectedStatus === 'pending') {
                            return !$isSubmitted && !$isOverdue;
                        } elseif ($this->selectedStatus === 'overdue') {
                            return $isOverdue;
                        }
                        return true;
                    });
                }
                
                // Apply rating filter
                if ($this->selectedRating !== 'all') {
                    $performances = $performances->filter(function($perf) {
                        return $perf->superior_rating === $this->selectedRating;
                    });
                }
                
                $taskData['performances'] = $performances;
                $taskData['total_performances'] = $performances->count();
                $taskData['submitted_count'] = $performances->where('is_submitted', true)->count();
                $taskData['avg_hours'] = $performances->where('is_submitted', true)->avg('hours_worked') ?? 0;
                $taskData['attachment_count'] = $performances->sum(function($p) { return $p->attachments->count(); });
                
                if ($taskData['total_performances'] > 0) {
                    $departmentUsers[$taskDepartment->id]['users'][$existingUserIndex]['tasks'][] = $taskData;
                }
                }
            } else {
                // Handle users without assigned tasks but with performance records
                // Apply the same filtering logic as above for consistency
                $userPerformancesQuery = DailyPerformance::where('user_id', $userWithTasks->id)
                    ->with(['task', 'task.department', 'attachments']);
                
                // Apply date filtering with same logic as main section
                if ($isViewingCurrentMonth) {
                    // When viewing current month, include current month + previous overdue only
                    $userPerformancesQuery->where(function($q) use ($startDate, $endDate) {
                        $q->whereBetween('performance_date', [$startDate, $endDate])
                          ->orWhere(function($subQ) {
                              $subQ->where('performance_date', '<', Carbon::today())
                                   ->where('is_submitted', false); // Only overdue from previous dates
                          });
                    });
                } else {
                    // When viewing specific date range, include all performances in that range
                    $userPerformancesQuery->whereBetween('performance_date', [$startDate, $endDate]);
                }
                
                $userPerformances = $userPerformancesQuery->get()->groupBy('task.department.id');

                foreach ($userPerformances as $departmentId => $performances) {
                    if (!$departmentId) continue; // Skip performances without department

                    $firstPerformance = $performances->first();
                    $taskDepartment = $firstPerformance->task->department;

                    // Skip if department filter is applied and doesn't match
                    if ($this->selectedDepartment !== 'all' && $taskDepartment->id != $this->selectedDepartment) {
                        continue;
                    }

                    if (!isset($departmentUsers[$taskDepartment->id])) {
                        $departmentUsers[$taskDepartment->id] = [
                            'department' => $taskDepartment,
                            'users' => []
                        ];
                    }

                    // Check if user already exists in this department
                    $existingUserIndex = null;
                    foreach ($departmentUsers[$taskDepartment->id]['users'] as $index => $existingUser) {
                        if ($existingUser['user']->id === $userWithTasks->id) {
                            $existingUserIndex = $index;
                            break;
                        }
                    }

                    if ($existingUserIndex === null) {
                        // Add new user to this department
                        $userData = [
                            'user' => $userWithTasks,
                            'tasks' => []
                        ];
                        $departmentUsers[$taskDepartment->id]['users'][] = $userData;
                        $existingUserIndex = count($departmentUsers[$taskDepartment->id]['users']) - 1;
                    }

                    // Group performances by task
                    $taskPerformances = $performances->groupBy('task_id');
                    foreach ($taskPerformances as $taskId => $taskPerfs) {
                        $task = $taskPerfs->first()->task;

                        // Apply status and rating filters
                        $filteredPerfs = $taskPerfs;
                        if ($this->selectedStatus !== 'all') {
                            $filteredPerfs = $filteredPerfs->filter(function($perf) {
                                if ($this->selectedStatus === 'submitted') {
                                    return $perf->is_submitted;
                                } elseif ($this->selectedStatus === 'pending') {
                                    return !$perf->is_submitted;
                                }
                                return true;
                            });
                        }

                        if ($this->selectedRating !== 'all') {
                            $filteredPerfs = $filteredPerfs->filter(function($perf) {
                                return $perf->superior_rating === $this->selectedRating;
                            });
                        }

                        if ($filteredPerfs->count() > 0) {
                            $taskData = [
                                'task' => $task,
                                'performances' => $filteredPerfs,
                                'total_performances' => $filteredPerfs->count(),
                                'submitted_count' => $filteredPerfs->where('is_submitted', true)->count(),
                                'avg_hours' => $filteredPerfs->where('is_submitted', true)->avg('hours_worked') ?? 0,
                                'attachment_count' => $filteredPerfs->sum(function($p) { return $p->attachments->count(); }),
                            ];

                            $departmentUsers[$taskDepartment->id]['users'][$existingUserIndex]['tasks'][] = $taskData;
                        }
                    }
                }
            }
        }
        
        // Convert to array and filter out departments without actual performance data
        $this->hierarchicalData = [];
        foreach ($departmentUsers as $departmentData) {
            // Filter out users who don't have any tasks with performance data
            $usersWithPerformanceData = [];
            foreach ($departmentData['users'] as $userData) {
                if (count($userData['tasks']) > 0) {
                    $usersWithPerformanceData[] = $userData;
                }
            }

            // Only include departments that have users with actual performance data
            if (count($usersWithPerformanceData) > 0) {
                $departmentData['users'] = $usersWithPerformanceData;
                $this->hierarchicalData[] = $departmentData;
            }
        }
    }

    public function viewPerformance($performanceId)
    {
        // Handle null or invalid performance IDs
        if (!$performanceId) {
            session()->flash('error', 'Performance record not found.');
            return;
        }
        
        $performance = DailyPerformance::with(['user', 'task', 'department', 'attachments'])
            ->find($performanceId);
        
        if ($performance) {
            $this->selectedPerformance = $performance;
            $this->showViewModal = true;
        } else {
            session()->flash('error', 'Performance record not found.');
        }
    }

    public function closeViewModal()
    {
        $this->showViewModal = false;
        $this->selectedPerformance = null;
    }

    public function viewAttachments($performanceId)
    {
        $performance = DailyPerformance::with('attachments')->find($performanceId);
        
        if ($performance && $performance->attachments->count() > 0) {
            $this->selectedAttachments = $performance->attachments->toArray();
            $this->showAttachmentsModal = true;
        } else {
            session()->flash('error', 'No attachments found for this performance record.');
        }
    }

    public function closeAttachmentsModal()
    {
        $this->showAttachmentsModal = false;
        $this->selectedAttachments = [];
    }

    public function openRatingModal($performanceId)
    {
        $performance = DailyPerformance::with('task')->find($performanceId);

        $authUser = auth()->user();
        $canRate = false;

        if ($performance) {
            // Nazim roles can always rate
            if ($authUser->isNazim() || $authUser->hasRole('nazim-ul-umoor')) {
                $canRate = true;
            } else if ($authUser->isSuperior()) {
                // Superiors can rate their assistant's performance for the same grouped task
                $task = $performance->task;
                if ($task) {
                    $mainTask = $task->getMainTask();
                    $related = $mainTask->relatedTasks();
                    $superiorTask = $related->firstWhere('role_type', 'superior');
                    if ($superiorTask && (int)$superiorTask->assigned_to === (int)$authUser->id) {
                        $canRate = true;
                    }
                }
            }
        }

        if ($performance && $canRate) {
            $this->selectedPerformanceForRating = $performance;
            $this->superiorRating = $performance->superior_rating ?? '';
            $this->superiorComments = $performance->superior_comments ?? '';
            $this->showRatingModal = true;
        } else {
            session()->flash('error', 'You do not have permission to rate this performance.');
        }
    }

    public function submitRating()
    {
        $this->validate([
            'superiorRating' => 'required|in:poor,fair,good,excellent',
            'superiorComments' => 'nullable|string|max:1000'
        ]);

        if ($this->selectedPerformanceForRating) {
            $this->selectedPerformanceForRating->update([
                'superior_rating' => $this->superiorRating,
                'superior_comments' => $this->superiorComments,
                'rated_by' => auth()->id(),
                'rated_at' => now()
            ]);

            $this->closeRatingModal();
            $this->loadHierarchicalData();
            session()->flash('message', 'Performance rating submitted successfully.');
        }
    }

    public function closeRatingModal()
    {
        $this->showRatingModal = false;
        $this->selectedPerformanceForRating = null;
        $this->superiorRating = '';
        $this->superiorComments = '';
    }

    /**
     * Adopt an assistant's submitted performance and submit it on behalf of the assigned superior.
     * This copies the description, hours, notes, and attachments for the same date.
     */
    public function adoptAssistantPerformance($performanceId)
    {
        $authUser = auth()->user();

        // Load assistant's performance with task and attachments
        $assistantPerformance = DailyPerformance::with(['task', 'attachments'])->find($performanceId);
        if (!$assistantPerformance) {
            session()->flash('error', 'Performance record not found.');
            return;
        }

        // Only allow adopting submitted reports
        if (!$assistantPerformance->is_submitted) {
            session()->flash('error', 'Assistant performance must be submitted before it can be adopted.');
            return;
        }

        $task = $assistantPerformance->task; // assistant's assigned task (could be child)
        if (!$task) {
            session()->flash('error', 'Associated task not found.');
            return;
        }

        // Resolve the grouped task and locate the superior's assignment
        $mainTask = $task->getMainTask();
        $relatedTasks = $mainTask->relatedTasks();
        $superiorTask = $relatedTasks->firstWhere('role_type', 'superior');

        if (!$superiorTask) {
            session()->flash('error', 'No superior is linked to this grouped task.');
            return;
        }

        // Authorization: the current user must be the assigned superior or a Nazim
        if (!($authUser->isNazim() || $authUser->hasRole('nazim-ul-umoor') || (int)$superiorTask->assigned_to === (int)$authUser->id)) {
            session()->flash('error', 'You are not authorized to adopt this performance.');
            return;
        }

        // Ensure we create/update the superior's record for the same date and their task id
        $superiorPerformance = DailyPerformance::where('user_id', $superiorTask->assigned_to)
            ->where('task_id', $superiorTask->id)
            ->where('performance_date', $assistantPerformance->performance_date)
            ->first();

        $payload = [
            'user_id' => $superiorTask->assigned_to,
            'task_id' => $superiorTask->id,
            'department_id' => $superiorTask->department_id,
            'performance_date' => $assistantPerformance->performance_date,
            'tasks_completed' => $assistantPerformance->tasks_completed,
            'challenges_faced' => $assistantPerformance->challenges_faced,
            'next_day_plan' => $assistantPerformance->next_day_plan,
            'hours_worked' => $assistantPerformance->hours_worked,
            'overall_rating' => $assistantPerformance->overall_rating,
            'additional_notes' => $assistantPerformance->additional_notes,
            'is_submitted' => true,
            'submitted_at' => now(),
        ];

        if ($superiorPerformance) {
            // If already submitted, do nothing
            if ($superiorPerformance->is_submitted) {
                session()->flash('message', 'Superior performance is already submitted for this date.');
                return;
            }
            $superiorPerformance->update($payload);
        } else {
            $superiorPerformance = DailyPerformance::create($payload);
        }

        // Copy evidence files (only DB rows; files are reused by path)
        foreach ($assistantPerformance->attachments as $attachment) {
            DailyPerformanceAttachment::create([
                'daily_performance_id' => $superiorPerformance->id,
                'original_name' => $attachment->original_name,
                'file_name' => $attachment->file_name,
                'file_path' => $attachment->file_path,
                'file_type' => $attachment->file_type,
                'mime_type' => $attachment->mime_type,
                'file_size' => $attachment->file_size,
                'uploaded_by' => $attachment->uploaded_by,
            ]);
        }

        $this->loadHierarchicalData();
        session()->flash('message', 'Assistant performance adopted and submitted for the superior.');
    }

    public function updatedSelectedStartDate()
    {
        $this->resetPage();
        $this->loadHierarchicalData();
        $this->loadStatistics();
        $this->loadSummaryData();
    }

    public function updatedSelectedEndDate()
    {
        $this->resetPage();
        $this->loadHierarchicalData();
        $this->loadStatistics();
        $this->loadSummaryData();
    }

    public function updatedSelectedUser()
    {
        $this->resetPage();
        $this->loadHierarchicalData();
        $this->loadSummaryData();
    }

    public function updatedSelectedDepartment()
    {
        $this->resetPage();
        $this->loadHierarchicalData();
        $this->loadSummaryData();
    }

    public function updatedSelectedStatus()
    {
        $this->resetPage();
        $this->loadHierarchicalData();
        $this->loadSummaryData();
    }

    public function updatedSelectedRating()
    {
        $this->resetPage();
        $this->loadHierarchicalData();
        $this->loadSummaryData();
    }

    public function updatedViewMode()
    {
        if ($this->viewMode === 'hierarchical') {
            $this->loadHierarchicalData();
        }
    }

    public function getFileIcon($file)
    {
        $extension = strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION));
        
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'])) {
            return 'fas fa-image text-success';
        } elseif ($extension === 'pdf') {
            return 'fas fa-file-pdf text-danger';
        } elseif (in_array($extension, ['doc', 'docx'])) {
            return 'fas fa-file-word text-primary';
        } elseif (in_array($extension, ['xls', 'xlsx'])) {
            return 'fas fa-file-excel text-success';
        } elseif (in_array($extension, ['ppt', 'pptx'])) {
            return 'fas fa-file-powerpoint text-warning';
        } else {
            return 'fas fa-file text-secondary';
        }
    }

    public function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function canViewInBrowser($file)
    {
        $viewableTypes = [
            'application/pdf',
            'text/plain',
            'text/html',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml'
        ];
        
        return in_array($file['mime_type'] ?? '', $viewableTypes);
    }

    public function getPerformances()
    {
        $authUser = auth()->user();
        $startDate = Carbon::parse($this->selectedStartDate);
        $endDate = Carbon::parse($this->selectedEndDate);
        $currentMonth = Carbon::now()->startOfMonth();

        // Role-based user filtering with proper mixed role handling
        if ($authUser->hasRole('nazim-ul-umoor')) {
            // Nazim-ul-umoor can see all users with tasks
            $usersWithTasks = User::whereHas('assignedTasks', function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            });
        } elseif ($authUser->isSuperior()) {
            // Superior can see performance based on department-specific relationships
            // They can see:
            // 1. Their own performance (always)
            // 2. Performance of users who are assistants in departments where they are supervisor
            // 3. Performance of their global assistants (but NOT in departments where the assistant is their supervisor)
            
            $userIds = collect([$authUser->id]); // Always include themselves
            
            // Add assistants from departments where this user is a Superior
            if ($authUser->supervisorDepartments()->exists()) {
                foreach ($authUser->supervisorDepartments()->get() as $dept) {
                    // Get users who are ASSISTANTS in this department (not other supervisors)
                    $assistantsInDept = \App\Models\DepartmentSupervisorAssistant::where('department_id', $dept->id)
                        ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                        ->where('is_active', true)
                        ->pluck('user_id');
                    
                    $userIds = $userIds->merge($assistantsInDept);
                }
            }
            
            // Add global assistants, but exclude them from departments where they are supervisors of current user
            $globalAssistantIds = $authUser->assistants->pluck('id');
            foreach ($globalAssistantIds as $assistantId) {
                $assistant = User::find($assistantId);
                if ($assistant) {
                    // Check if this assistant is a supervisor of current user in any department
                    $isAssistantSupervisorOfUser = \App\Models\DepartmentSupervisorAssistant::where('user_id', $assistantId)
                        ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
                        ->where('is_active', true)
                        ->whereIn('department_id', function($query) use ($authUser) {
                            $query->select('department_id')
                                ->from('department_supervisor_assistants')
                                ->where('user_id', $authUser->id)
                                ->where('role_type', \App\Models\DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                                ->where('is_active', true);
                        })
                        ->exists();
                    
                    // Only add if they're not a supervisor of current user in any shared department
                    if (!$isAssistantSupervisorOfUser) {
                        $userIds->push($assistantId);
                    }
                }
            }
            
            $userIds = $userIds->unique();
            
            $usersWithTasks = User::whereIn('id', $userIds)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                });
        } else {
            // Assistant-only users can only see themselves
            $usersWithTasks = User::where('id', $authUser->id)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                });
        }

        // Apply additional filters
        if ($this->selectedUser !== 'all') {
            $usersWithTasks->where('id', $this->selectedUser);
        }

        if ($this->selectedDepartment !== 'all') {
            $usersWithTasks->whereHas('assignedTasks.department', function ($q) {
                $q->where('departments.id', $this->selectedDepartment);
            });
        }

        $users = $usersWithTasks->with(['assignedTasks' => function ($q) {
            $q->whereNotIn('status', ['completed', 'cancelled'])
              ->with(['department']);
        }])->get();

        $performanceData = collect();

        // Get date range for checking performance
        if ($this->dateRangeMode === 'range' && $this->selectedStartDate && $this->selectedEndDate) {
            $filterStartDate = Carbon::parse($this->selectedStartDate);
            $filterEndDate = Carbon::parse($this->selectedEndDate);
        } else {
            $filterStartDate = Carbon::parse($this->selectedDate);
            $filterEndDate = Carbon::parse($this->selectedDate);
        }

        foreach ($users as $user) {
            foreach ($user->assignedTasks as $task) {
                // Skip invalid tasks
                if (!$task || !$task->id) {
                    continue;
                }
                
                // Get date range for checking performance
                $taskStartDate = Carbon::parse($task->created_at)->startOfDay();
                $taskEndDate = in_array($task->status, ['completed', 'cancelled'])
                    ? Carbon::parse($task->updated_at)->startOfDay()
                    : Carbon::today();

                // Check each date from task creation to task end (or today)
                $currentDate = $taskStartDate->copy();
                while ($currentDate->lte($taskEndDate)) {
                    // Skip this date if it's a holiday or Sunday (non-working day)
                    if (!\App\Models\PerformanceHoliday::requiresPerformance($currentDate->format('Y-m-d'))) {
                        $currentDate->addDay();
                        continue;
                    }

                    // Check if this date falls within the user's selected filter range
                    $isInFilterRange = $currentDate->gte($filterStartDate) && $currentDate->lte($filterEndDate);
                    
                    // Check if this date is in current month or later
                    $isCurrentOrFutureMonth = $currentDate->gte($currentMonth);

                    // Check performance for this task
                    $performance = DailyPerformance::where('user_id', $user->id)
                        ->where('task_id', $task->id)
                        ->whereDate('performance_date', $currentDate->format('Y-m-d'))
                        ->first();

                    $isSubmitted = $performance && $performance->is_submitted;
                    // A date is overdue if it's before today AND no submitted performance exists
                    $isOverdue = $currentDate->lt(Carbon::today()) && !$isSubmitted;

                    // Filtering logic:
                    // 1. If user selects current month: show current month + previous overdue
                    // 2. If user selects previous month: show all performance for that month
                    // 3. If user selects date range: show all performance in that range
                    $shouldInclude = false;
                    
                    // Check if user is viewing current month (default behavior)
                    $isViewingCurrentMonth = $filterStartDate->isSameMonth(Carbon::now()) && $filterEndDate->isSameMonth(Carbon::now());
                    
                    if ($isInFilterRange) {
                        // Always include if within selected date range
                        $shouldInclude = true;
                    } elseif ($isViewingCurrentMonth && $isOverdue) {
                        // When viewing current month, also show previous overdue entries
                        $shouldInclude = true;
                    }

                    if (!$shouldInclude) {
                        $currentDate->addDay();
                        continue;
                    }

                    // Create entry for missing performance (overdue) or existing performance
                    if (!$performance && $isOverdue) {
                        // Create overdue entry for missing performance
                        $performance = (object)[
                            'id' => null,
                            'user_id' => $user->id,
                            'task_id' => $task->id,
                            'performance_date' => $currentDate->format('Y-m-d'),
                            'is_submitted' => false,
                            'hours_worked' => 0,
                            'overall_rating' => null,
                            'submitted_at' => null,
                            'tasks_completed' => null,
                            'challenges_faced' => null,
                            'next_day_plan' => null,
                            'additional_notes' => null,
                            'superior_rating' => null,
                            'superior_comments' => null,
                            'is_overdue' => true,
                            ];
                    }

                    // Skip if no performance record exists and it's not overdue
                    if (!$performance && !$isOverdue) {
                        $currentDate->addDay();
                        continue;
                    }

                    // Apply status filter
                    $includeRecord = true;
                    if ($this->selectedStatus === 'submitted' && !$isSubmitted) {
                        $includeRecord = false;
                    } elseif ($this->selectedStatus === 'pending' && ($isSubmitted || $isOverdue)) {
                        $includeRecord = false;
                    } elseif ($this->selectedStatus === 'overdue' && !$isOverdue) {
                        $includeRecord = false;
                    }

                    // Apply rating filter (only for submitted performances with actual rating)
                    if ($this->selectedRating !== 'all' && $performance && $performance->overall_rating !== $this->selectedRating) {
                        $includeRecord = false;
                    }

                    if ($includeRecord) {
                        // Create individual performance entry for each user-task-date combination
                        $performanceData->push((object)[
                            'id' => $performance && (is_object($performance) ? (property_exists($performance, 'id') ? $performance->id : null) : $performance->id) ?: null,
                            'user' => $user,
                            'task' => $task,
                            'related_tasks' => collect([$task]), // Individual task, not grouped
                            'task_number' => $task->task_number,
                            'performance_date' => $currentDate->format('Y-m-d'),
                            'is_submitted' => $isSubmitted,
                            'is_overdue' => $isOverdue,
                            'performance' => $performance,
                            'hours_worked' => $performance ? $performance->hours_worked : 0,
                            'overall_rating' => $performance ? $performance->overall_rating : null,
                            'submitted_at' => $performance ? $performance->submitted_at : null,
                        ]);
                    }

                    $currentDate->addDay();
                }
            }
        }

        // Sort by date descending, then by submission status (overdue first, then pending, then submitted)
        $performanceData = $performanceData->sortByDesc('performance_date')
            ->sortBy(function($item) {
                if ($item->is_overdue) return 0;
                if (!$item->is_submitted) return 1;
                return 2;
            });

        // Return all data without pagination
        return $performanceData;
    }

    public function openViewModal($performanceId)
    {
        $this->selectedPerformance = DailyPerformance::with(['user', 'task', 'department'])->findOrFail($performanceId);
        $this->authorize('viewPerformance', $this->selectedPerformance->user);
        $this->showViewModal = true;
    }

    public function closeModal()
    {
        $this->showViewModal = false;
        $this->selectedPerformance = null;
    }

    public function openResetModal($userId)
    {
        $this->authorize('view-all-performance');
        $this->selectedUserForReset = User::findOrFail($userId);
        $this->resetReason = '';
        $this->showResetModal = true;
    }

    public function closeResetModal()
    {
        $this->showResetModal = false;
        $this->selectedUserForReset = null;
        $this->resetReason = '';
    }

    public function resetUserAccess()
    {
        $this->authorize('view-all-performance');

        if (!$this->selectedUserForReset) {
            return;
        }

        // Remove any active performance restrictions
        UserRestriction::where('user_id', $this->selectedUserForReset->id)
            ->where('restriction_type', 'performance_not_submitted')
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => auth()->id(),
                'lifted_at' => now(),
                'lift_reason' => $this->resetReason ?: 'Access reset by admin',
            ]);

        // Create a performance record for today if it doesn't exist
        $today = Carbon::today();
        $existingPerformance = DailyPerformance::where('user_id', $this->selectedUserForReset->id)
            ->where('performance_date', $today)
            ->first();

        if (!$existingPerformance) {
            DailyPerformance::create([
                'user_id' => $this->selectedUserForReset->id,
                'performance_date' => $today,
                'tasks_completed' => 'Access reset by admin - Please update with actual performance',
                'fatawa_processed' => 0,
                'questions_answered' => 0,
                'hours_worked' => 0,
                'overall_rating' => 'fair',
                'additional_notes' => 'Access reset by admin: ' . ($this->resetReason ?: 'No reason provided'),
                'is_submitted' => true,
                'submitted_at' => now(),
            ]);
        } elseif (!$existingPerformance->is_submitted) {
            // If performance exists but not submitted, mark it as submitted
            $existingPerformance->update([
                'is_submitted' => true,
                'submitted_at' => now(),
                'additional_notes' => ($existingPerformance->additional_notes ?: '') .
                    "\n\nAccess reset by admin: " . ($this->resetReason ?: 'No reason provided'),
            ]);
        }

        session()->flash('message', "Access reset successfully for {$this->selectedUserForReset->name}. User can now access the system.");

        $this->closeResetModal();
        $this->loadStatistics();
    }

    public function updatedSelectedDate()
    {
        $this->resetPage();
    }

    public function updatedDateRangeMode()
    {
        $this->resetPage();
    }

    public function exportReport()
    {
        $this->authorize('generate-reports');
        
        // This would generate a PDF or Excel report
        session()->flash('message', 'Report export functionality will be implemented.');
    }

    public function getRatingColor($rating)
    {
        return match($rating) {
            'poor' => 'danger',
            'fair' => 'warning',
            'good' => 'info',
            'excellent' => 'success',
            default => 'secondary'
        };
    }

    public function getStatusColor($isSubmitted)
    {
        return $isSubmitted ? 'success' : 'warning';
    }

    public function sendReminder($userId)
    {
        $this->authorize('manage-users');
        
        // This would send a reminder notification to the user
        $user = User::findOrFail($userId);
        session()->flash('message', "Reminder sent to {$user->name}.");
    }

    public function getMissingPerformanceDates($userId, $taskId)
    {
        $user = User::find($userId);
        $task = $user->assignedTasks()->find($taskId);
        
        if (!$task) return collect();

        $taskStartDate = Carbon::parse($task->created_at)->startOfDay();
        $taskEndDate = in_array($task->status, ['completed', 'cancelled']) 
            ? Carbon::parse($task->updated_at)->startOfDay() 
            : Carbon::today();

        $missingDates = collect();
        $currentDate = $taskStartDate->copy();

        while ($currentDate->lte($taskEndDate)) {
            $performance = DailyPerformance::where('user_id', $userId)
                ->where('task_id', $taskId)
                ->whereDate('performance_date', $currentDate->format('Y-m-d'))
                ->where('is_submitted', true)
                ->first();

            if (!$performance) {
                $missingDates->push($currentDate->format('Y-m-d'));
            }

            $currentDate->addDay();
        }

        return $missingDates;
    }

    public function loadSummaryData()
    {
        $startDate = Carbon::parse($this->selectedStartDate)->startOfDay();
        $endDate = Carbon::parse($this->selectedEndDate)->endOfDay();
        $user = auth()->user();

        // Role-based user filtering with proper mixed role handling
        if ($user->hasRole('nazim-ul-umoor')) {
            // Nazim-ul-umoor can see all users with tasks
            $usersWithTasks = User::whereHas('assignedTasks', function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            })->with(['assignedTasks' => function ($q) {
                $q->whereNotIn('status', ['completed', 'cancelled']);
            }])->get();
        } elseif ($user->isSuperior()) {
            // Superior can see their assistants and themselves
            // IMPORTANT: Do NOT include their own superior's performance
            $assistantIds = $user->assistants->pluck('id');
            
            // For mixed role users (Superior + Assistant), only show:
            // 1. Their own performance
            // 2. Their direct assistants' performance
            // 3. Users from departments where they are Superior
            $userIds = collect([$user->id]); // Always include themselves
            $userIds = $userIds->merge($assistantIds); // Add their assistants
            
            // Add users from departments where this user is a Superior
            if ($user->supervisorDepartments()->exists()) {
                $departmentUserIds = $user->supervisorDepartments()
                    ->with('teamMembers')
                    ->get()
                    ->flatMap->teamMembers
                    ->pluck('id');
                $userIds = $userIds->merge($departmentUserIds);
            }
            
            $userIds = $userIds->unique();
            
            $usersWithTasks = User::whereIn('id', $userIds)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                })->with(['assignedTasks' => function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                }])->get();
        } else {
            // Assistant-only users can only see themselves
            $usersWithTasks = User::where('id', $user->id)
                ->whereHas('assignedTasks', function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                })->with(['assignedTasks' => function ($q) {
                    $q->whereNotIn('status', ['completed', 'cancelled']);
                }])->get();
        }

        $totalExpectedReports = 0;
        $submittedCount = 0;
        $pendingCount = 0;
        $overdueCount = 0;
        $totalHours = 0;
        $submittedReports = 0;

        foreach ($usersWithTasks as $taskUser) {
            foreach ($taskUser->assignedTasks as $task) {
                $taskStartDate = Carbon::parse($task->created_at)->startOfDay();
                $taskEndDate = in_array($task->status, ['completed', 'cancelled'])
                    ? Carbon::parse($task->updated_at)->startOfDay()
                    : Carbon::today();

                // Count expected reports for each day the task was active in the date range
                $currentDate = max($startDate, $taskStartDate);
                $endDateForTask = min($endDate, $taskEndDate);

                while ($currentDate->lte($endDateForTask)) {
                    // Only count working days (not holidays/Sundays)
                    if (!\App\Models\PerformanceHoliday::requiresPerformance($currentDate->format('Y-m-d'))) {
                        $currentDate->addDay();
                        continue;
                    }

                    $totalExpectedReports++;

                    $performance = DailyPerformance::where('user_id', $taskUser->id)
                        ->where('task_id', $task->id)
                        ->where('performance_date', $currentDate->format('Y-m-d'))
                        ->first();

                    if ($performance && $performance->is_submitted) {
                        $submittedCount++;
                        $submittedReports++;
                        $totalHours += $performance->hours_worked ?? 0;
                    } else {
                        if ($currentDate->lt(Carbon::today())) {
                            $overdueCount++;
                        } else {
                            $pendingCount++;
                        }
                    }

                    $currentDate->addDay();
                }
            }
        }

        // Determine role-based labels
        $userRole = $user->hasRole('nazim-ul-umoor') ? 'nazim' :
                   ($user->isSuperior() ? 'superior' : 'assistant');

        $this->summaryData = [
            'total_users' => $usersWithTasks->count(),
            'submitted' => $submittedCount,
            'pending' => $pendingCount,
            'overdue' => $overdueCount,
            'submission_rate' => $totalExpectedReports > 0 ? round(($submittedCount / $totalExpectedReports) * 100, 1) : 0,
            'avg_hours' => $submittedReports > 0 ? round($totalHours / $submittedReports, 1) : 0,
            'user_role' => $userRole,
        ];
    }
}
