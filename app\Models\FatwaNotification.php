<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class FatwaNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'fatwa_code',
        'assigned_by',
        'data',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    /**
     * Get the user that owns the notification.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for read notifications
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    /**
     * Scope for recent notifications (last 30 days)
     */
    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays(30));
    }

    /**
     * Create a fatwa assignment notification
     */
    public static function createFatwaAssignmentNotification($userId, $fatwaCode, $assignedBy)
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'fatwa_assignment',
            'title' => 'New Fatwa Assignment',
            'message' => "{$assignedBy} assigned you Fatwa No {$fatwaCode}",
            'fatwa_code' => $fatwaCode,
            'assigned_by' => $assignedBy,
            'data' => [
                'fatwa_code' => $fatwaCode,
                'assigned_by' => $assignedBy,
                'assigned_at' => now()->toISOString(),
            ],
        ]);
    }
}