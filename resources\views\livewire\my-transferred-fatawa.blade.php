<div>
    <div wire:ignore>
        <x-layout bodyClass="g-sidenav-show bg-gray-200">
            <x-navbars.sidebar activePage="my-transferred-fatawa"></x-navbars.sidebar>
        </x-layout>
    </div>

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        @livewire('navbar', ['titlePage' => 'My Transferred Fatawa'])

        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="card my-4">
                        <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                            <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                                <h6 class="text-white text-capitalize ps-3">My Transferred Fatawa</h6>
                            </div>
                        </div>
                        <div class="card-body px-4 pb-2">
                            
                            <!-- Tab Navigation -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <ul class="nav nav-tabs" role="tablist">
                                        @php
                                            $userRoles = Auth::user()->roles->pluck('name')->toArray();
                                            $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
                                            $hasDaruliftaRole = !empty(array_intersect($userRoles, $daruliftaRoles));
                                        @endphp
                                        
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link {{ $activeTab === 'received' ? 'active' : '' }}" 
                                                    wire:click="setActiveTab('received')" 
                                                    type="button">
                                                @if($hasDaruliftaRole)
                                                    Received by Mujeebs
                                                @else
                                                    Received Fatawa
                                                @endif
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link {{ $activeTab === 'sent' ? 'active' : '' }}" 
                                                    wire:click="setActiveTab('sent')" 
                                                    type="button">
                                                @if($hasDaruliftaRole)
                                                    Sent by Mujeebs
                                                @else
                                                    Sent Fatawa
                                                @endif
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Search Bar -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="input-group input-group-outline">
                                        <label class="form-label">Search...</label>
                                        <input type="text" class="form-control" wire:model.live="search">
                                    </div>
                                </div>
                            </div>

                            <!-- Content based on active tab -->
                            <div class="tab-content">
                                @if($activeTab === 'received')
                                    <div class="tab-pane fade show active">
                                        @php
                                            $userRoles = Auth::user()->roles->pluck('name')->toArray();
                                            $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
                                            $hasDaruliftaRole = !empty(array_intersect($userRoles, $daruliftaRoles));
                                        @endphp
                                        
                                        @if($hasDaruliftaRole)
                                            <h5 class="mb-3">Fatawa Received by My Darulifta Mujeebs</h5>
                                            <p class="text-muted mb-4">These are fatawa that have been transferred to mujeebs in your Darulifta.</p>
                                        @else
                                            <h5 class="mb-3">Fatawa Transferred to Me</h5>
                                            <p class="text-muted mb-4">These are fatawa that have been transferred to you by other users.</p>
                                        @endif
                                        
                                        @if($transferredFatawa->count() > 0)
                                            <div class="table-responsive">
                                                <table class="table align-items-center mb-0">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">File Code</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Darulifta</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Category</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Transferred By</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Mail Folder Date</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Transfer Date</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($transferredFatawa as $fatwa)
                                                            <tr>
                                                                <td>
                                                                    <div class="d-flex px-2 py-1">
                                                                        <div class="d-flex flex-column justify-content-center">
                                                                            <h6 class="mb-0 text-sm">{{ $fatwa->file_code }}</h6>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ $fatwa->darulifta_name }}</p>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ $fatwa->category }}</p>
                                                                </td>
                                                                <td>
                                                                    <span class="badge badge-sm bg-gradient-info">{{ $fatwa->transfer_by }}</span>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ $fatwa->mail_folder_date }}</p>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ \Carbon\Carbon::parse($fatwa->updated_at)->format('Y-m-d H:i') }}</p>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                            <!-- Pagination -->
                                            <div class="mt-4">
                                                {{ $transferredFatawa->links() }}
                                            </div>
                                        @else
                                            <div class="text-center py-4">
                                                @if($hasDaruliftaRole)
                                                    <p class="text-muted">No fatawa have been transferred to your Darulifta mujeebs yet.</p>
                                                @else
                                                    <p class="text-muted">No fatawa have been transferred to you yet.</p>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                @else
                                    <div class="tab-pane fade show active">
                                        @php
                                            $userRoles = Auth::user()->roles->pluck('name')->toArray();
                                            $daruliftaRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
                                            $hasDaruliftaRole = !empty(array_intersect($userRoles, $daruliftaRoles));
                                        @endphp
                                        
                                        @if($hasDaruliftaRole)
                                            <h5 class="mb-3">Fatawa Sent by My Darulifta Mujeebs</h5>
                                            <p class="text-muted mb-4">These are fatawa that have been transferred by mujeebs in your Darulifta.</p>
                                        @else
                                            <h5 class="mb-3">Fatawa Transferred by Me</h5>
                                            <p class="text-muted mb-4">These are fatawa that you have transferred to other users.</p>
                                        @endif
                                        
                                        @if($transferredFatawa->count() > 0)
                                            <div class="table-responsive">
                                                <table class="table align-items-center mb-0">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">File Code</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Darulifta</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Category</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Transferred To</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Mail Folder Date</th>
                                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Transfer Date</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($transferredFatawa as $fatwa)
                                                            <tr>
                                                                <td>
                                                                    <div class="d-flex px-2 py-1">
                                                                        <div class="d-flex flex-column justify-content-center">
                                                                            <h6 class="mb-0 text-sm">{{ $fatwa->file_code }}</h6>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ $fatwa->darulifta_name }}</p>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ $fatwa->category }}</p>
                                                                </td>
                                                                <td>
                                                                    <span class="badge badge-sm bg-gradient-success">{{ $fatwa->sender }}</span>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ $fatwa->mail_folder_date }}</p>
                                                                </td>
                                                                <td>
                                                                    <p class="text-xs font-weight-bold mb-0">{{ \Carbon\Carbon::parse($fatwa->updated_at)->format('Y-m-d H:i') }}</p>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                            <!-- Pagination -->
                                            <div class="mt-4">
                                                {{ $transferredFatawa->links() }}
                                            </div>
                                        @else
                                            <div class="text-center py-4">
                                                @if($hasDaruliftaRole)
                                                    <p class="text-muted">Your Darulifta mujeebs haven't transferred any fatawa yet.</p>
                                                @else
                                                    <p class="text-muted">You haven't transferred any fatawa yet.</p>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <x-footers.auth></x-footers.auth>
    </main>
</div>