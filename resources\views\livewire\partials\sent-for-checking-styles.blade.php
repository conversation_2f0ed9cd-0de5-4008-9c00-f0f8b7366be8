{{-- Sent For Checking Styles Partial --}}
<style>
    /* CSS Variables for consistent theming */
    :root {
        --primary-color: #007bff;
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --info-color: #17a2b8;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-color: #dee2e6;
        --border-radius: 0.375rem;
        --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --transition: all 0.15s ease-in-out;
    }

    /* Table Responsive Styles */
    .table-responsive {
        width: 100%;
        overflow-x: auto;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .table-responsive table {
        width: 100%;
        margin-bottom: 1rem;
        background-color: transparent;
        border-collapse: collapse;
    }

    .table-responsive th,
    .table-responsive td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        border-left: none;
        border-right: none;
        padding: 0.75rem;
        vertical-align: middle;
    }

    .table-responsive th:first-child,
    .table-responsive td:first-child {
        border-left: 1px solid var(--border-color);
    }

    .table-responsive th:last-child,
    .table-responsive td:last-child {
        border-right: 1px solid var(--border-color);
    }

    .table-bordered {
        border-collapse: collapse;
    }

    /* Modern Button Styles */
    .btn-modern {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        margin-bottom: 0;
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        text-decoration: none;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: var(--border-radius);
        transition: var(--transition);
        user-select: none;
    }

    .btn-modern:hover {
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }

    .btn-modern:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-modern:active {
        transform: translateY(0);
        box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    }

    .btn-modern.btn-outline-modern {
        background-color: transparent;
        border-color: var(--border-color);
        color: var(--dark-color);
    }

    .btn-primary-modern {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .btn-primary-modern:hover {
        background-color: #0056b3;
        border-color: #0056b3;
        color: white;
    }

    .btn-success-modern {
        background-color: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }

    .btn-success-modern:hover {
        background-color: #1e7e34;
        border-color: #1e7e34;
        color: white;
    }

    .btn-info-modern {
        background-color: var(--info-color);
        border-color: var(--info-color);
        color: white;
    }

    .btn-info-modern:hover {
        background-color: #117a8b;
        border-color: #117a8b;
        color: white;
    }

    .btn-secondary-modern {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        color: white;
    }

    .btn-secondary-modern:hover {
        background-color: #545b62;
        border-color: #545b62;
        color: white;
    }

    /* Filter Section Styles */
    .filter-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--box-shadow);
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .form-group-modern {
        display: flex;
        flex-direction: column;
    }

    .form-group-modern label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: var(--dark-color);
        font-size: 0.875rem;
    }

    .form-control-modern {
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: var(--transition);
        background-color: white;
    }

    .form-control-modern:focus {
        outline: 0;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--box-shadow);
        transition: var(--transition);
        border-left: 4px solid var(--primary-color);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--secondary-color);
        font-weight: 500;
    }

    /* Quick Actions */
    .quick-actions {
        margin-bottom: 2rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    /* Filter Summary */
    .filter-summary {
        margin-bottom: 1rem;
    }

    .filter-tags {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .filter-tag {
        background-color: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .remove-filter {
        color: white;
        text-decoration: none;
        font-weight: bold;
        margin-left: 0.25rem;
    }

    .remove-filter:hover {
        color: #ffcccc;
    }

    /* Card Styles */
    .card-modern {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        border: none;
        margin-bottom: 1.5rem;
    }

    .card-header-modern {
        background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: none;
    }

    .card-body-modern {
        padding: 1.5rem;
    }

    /* Folder Entries Styles */
    .folder-entries {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        max-width: 1000px;
    }

    .folder-entry {
        background-color: var(--light-color);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        transition: var(--transition);
    }

    .folder-entry:hover {
        background-color: var(--primary-color);
        color: white;
        text-decoration: none;
    }

    /* Loading and Animation Styles */
    .loading-spinner {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 2px solid var(--border-color);
        border-radius: 50%;
        border-top-color: var(--primary-color);
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .filter-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .action-buttons {
            flex-direction: column;
        }

        .btn-modern {
            width: 100%;
            justify-content: center;
        }

        .folder-entries {
            max-width: 100%;
        }
    }

    @media (max-width: 576px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
    /* Modern Folder Entry Styles */
    .folder-entries-modern {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: flex-start;
    }

    .folder-entry-modern {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border: 1px solid #cbd5e1;
        border-radius: 8px;
        padding: 0.75rem;
        min-width: 200px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .folder-entry-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
    }

    .folder-date-modern {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #1e293b;
    }

    .folder-date-modern a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .folder-date-modern a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }

    .folder-transfer-by-modern {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        margin-top: 0.5rem;
    }

    .folder-transfer-by-modern .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .folder-transfer-by-modern .badge:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .summary-stats-modern {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }

    .summary-stats-modern .badge {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-weight: 600;
    }
</style>
