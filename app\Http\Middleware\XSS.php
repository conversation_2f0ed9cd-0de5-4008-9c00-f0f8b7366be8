<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class XSS
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Apply middleware only to specific routes
        if (
            !$request->is('talaq-fatawa/save-child') &&
            !$request->is('save-fatawa') &&
            !$request->is('mark-as-checked/*') &&
            !$request->is('save-mn-fatawa/*') &&
            !$request->is('download-word') &&
            !$request->is('reciptque/store') &&
            !$request->is('reciptque/update/*') &&
            !$request->is('reciptque/edit/*') // Added conditions for reciptque routes
        ) {
            $input = $request->all();
            array_walk_recursive($input, function (&$input) {
                $input = strip_tags($input);
            });
            $request->merge($input);
        }

        return $next($request);
    }
}
