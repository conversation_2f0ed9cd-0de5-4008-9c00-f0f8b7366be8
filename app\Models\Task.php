<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Task extends Model
{
    use HasFactory;

    protected $table = 'workflow_tasks';

    // Task type constants
    const TYPE_DAILY = 'daily';
    const TYPE_WEEKLY = 'weekly';
    const TYPE_OPERATIONAL = 'operational';
    const TYPE_ONE_TIME = 'one-time';

    // Role type constants
    const ROLE_TYPE_SUPERIOR = 'superior';
    const ROLE_TYPE_ASSISTANT = 'assistant';
    const ROLE_TYPE_DIRECT = 'direct';

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'title',
        'description',
        'type',
        'status',
        'assigned_to',
        'assigned_by',
        'department_id',
        'due_date',
        'completed_at',
        'completion_notes',
        'priority',
        'task_number',
        'parent_task_id',
        'role_type',
    ];

    protected $casts = [
        'due_date' => 'date',
        'completed_at' => 'date',
    ];

    /**
     * Get the user this task is assigned to.
     */
    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who assigned this task.
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Get the department this task belongs to.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the daily performances for this task.
     */
    public function dailyPerformances()
    {
        return $this->hasMany(DailyPerformance::class, 'task_id');
    }

    /**
     * Scope to get pending tasks.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get completed tasks.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get overdue tasks.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', Carbon::today())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope to get tasks for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Check if the task is overdue.
     */
    public function isOverdue()
    {
        return $this->due_date < Carbon::today() && !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get the parent task (for assistant tasks).
     */
    public function parentTask()
    {
        return $this->belongsTo(Task::class, 'parent_task_id');
    }

    /**
     * Get child tasks (for superior tasks).
     */
    public function childTasks()
    {
        return $this->hasMany(Task::class, 'parent_task_id');
    }

    /**
     * Get all related tasks (parent + children).
     */
    public function relatedTasks()
    {
        if ($this->parent_task_id) {
            // This is a child task, get parent and siblings
            return Task::where('parent_task_id', $this->parent_task_id)
                      ->orWhere('id', $this->parent_task_id)
                      ->get();
        } else {
            // This is a parent task, get children
            return Task::where('parent_task_id', $this->id)
                      ->orWhere('id', $this->id)
                      ->get();
        }
    }

    /**
     * Check if this is a grouped task (has parent or children).
     */
    public function isGroupedTask()
    {
        return $this->parent_task_id !== null || $this->childTasks()->exists();
    }

    /**
     * Get the main task (parent if exists, otherwise self).
     */
    public function getMainTask()
    {
        return $this->parent_task_id ? $this->parentTask : $this;
    }

    /**
     * Generate unique task number.
     */
    public static function generateTaskNumber()
    {
        $date = Carbon::now()->format('Ymd');
        $lastTask = Task::where('task_number', 'like', "TSK-{$date}-%")
                       ->orderBy('task_number', 'desc')
                       ->first();
        
        if ($lastTask) {
            $lastNumber = (int) substr($lastTask->task_number, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '001';
        }
        
        return "TSK-{$date}-{$newNumber}";
    }

    /**
     * Mark task as completed.
     */
    public function markCompleted($notes = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => Carbon::today(),
            'completion_notes' => $notes,
        ]);
    }
}
