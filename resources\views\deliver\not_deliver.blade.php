<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="deliver"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="deliver_list"></x-navbars.navs.auth>

        <style>
            /* Modern UI Styles */
            .modern-page-container {
                padding: 2rem;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                min-height: 100vh;
            }

            .modern-header-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
            }

            .modern-header-content {
                padding: 2rem;
                color: white;
                text-align: center;
            }

            .modern-header-title {
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .modern-header-subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
                margin-bottom: 1.5rem;
            }

            .modern-nav-button {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                color: white;
                padding: 0.75rem 2rem;
                border-radius: 50px;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .modern-nav-button:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }

            .modern-data-card {
                background: white;
                border-radius: 15px;
                box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .modern-data-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            }

            .modern-data-header {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                padding: 1.5rem 2rem;
                border: none;
                position: relative;
            }

            .modern-data-header::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            }

            .modern-data-title {
                font-size: 1.3rem;
                font-weight: 600;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }

            .modern-table-container {
                padding: 0;
                overflow-x: auto;
            }

            .modern-table {
                width: 100%;
                margin: 0;
                border-collapse: separate;
                border-spacing: 0;
                font-family: 'Jameel Noori Nastaleeq', Arial, sans-serif;
            }

            .modern-table thead th {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                color: #495057;
                font-weight: 600;
                padding: 1rem;
                text-align: center;
                border: none;
                font-size: 0.9rem;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .modern-table tbody td {
                padding: 1rem;
                text-align: center;
                border-bottom: 1px solid #f1f3f4;
                vertical-align: middle;
                transition: background-color 0.3s ease;
            }

            .modern-table tbody tr:hover {
                background-color: #f8f9ff;
            }

            .modern-table tbody tr:last-child td {
                border-bottom: none;
            }

            .text-direction-rtl {
                direction: rtl;
            }

            .modern-input {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.5rem;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                width: 100%;
                max-width: 200px;
            }

            .modern-input:focus {
                border-color: #4facfe;
                box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
                outline: none;
            }

            .modern-checkbox {
                width: 20px;
                height: 20px;
                accent-color: #4facfe;
                cursor: pointer;
            }

            .status-badge {
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
            }

            .status-not-delivered {
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                color: white;
            }

            @media (max-width: 768px) {
                .modern-page-container {
                    padding: 1rem;
                }

                .modern-header-content {
                    padding: 1.5rem;
                }

                .modern-header-title {
                    font-size: 1.5rem;
                }

                .modern-table-container {
                    font-size: 0.8rem;
                }

                .modern-table thead th,
                .modern-table tbody td {
                    padding: 0.5rem;
                }
            }
        </style>

        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <div class="modern-page-container">
            <!-- Modern Header Card -->
            <div class="modern-header-card">
                <div class="modern-header-content">
                    <h1 class="modern-header-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Not Delivered to Sayel
                    </h1>
                    <p class="modern-header-subtitle">
                        Manage fatawa that have not been delivered to Sayel
                    </p>
                    <a href="{{ route('deliver') }}" class="modern-nav-button">
                        <i class="fas fa-check-circle"></i>
                        View Delivered Fatawa
                    </a>
                </div>
            </div>

            <!-- Data Display Section -->
            @foreach($daruliftaNames as $daruliftaName)
                @if(isset($okFatawa[$daruliftaName]))
                    <div class="modern-data-card">
                        <div class="modern-data-header">
                            <h4 class="modern-data-title">
                                <i class="fas fa-university"></i>
                                {{ $daruliftaName }}
                            </h4>
                        </div>

                        <div class="card-body p-0">
                            @foreach($mailfolderDates as $mailfolderDate)
                                @if(isset($okFatawa[$daruliftaName][$mailfolderDate]))
                                    @php
                                        $mailfolderDateCount = count($okFatawa[$daruliftaName][$mailfolderDate]);
                                    @endphp

                                    <div class="modern-data-card mb-3 mx-3 mt-3">
                                        <div class="modern-data-header">
                                            <h5 class="modern-data-title">
                                                <i class="fas fa-calendar-alt"></i>
                                                {{ \Carbon\Carbon::parse($mailfolderDate)->format('d M Y') }}
                                                <span class="status-badge status-not-delivered ms-2">
                                                    {{ $mailfolderDateCount }} Fatawa
                                                </span>
                                            </h5>
                                        </div>

                                        <div class="modern-table-container">
                                            <table class="modern-table">
                                                <thead>
                                                    <tr>
                                                        <th>S.NO</th>
                                                        <th>FATWA NO</th>
                                                        <th>MUJEEB</th>
                                                        <th>CATEGORY</th>
                                                        <th>CHECKED DATE</th>
                                                        <th>CHECKED FOLDER</th>
                                                        <th>DELIVER STATUS</th>
                                                        <th>NOT DELIVER REASON</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($okFatawa[$daruliftaName][$mailfolderDate] as $index => $file)
                                                        <tr>
                                                            <td>
                                                                <span class="fw-bold text-primary">{{ $index + 1 }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="fw-bold text-dark">{{ $file->file_code }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-gradient-success px-3 py-2">{{ $file->sender }}</span>
                                                            </td>
                                                            <td class="text-direction-rtl">
                                                                <span class="text-muted">{{ $file->category }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="text-muted">
                                                                    {{ $file->checked_date ? \Carbon\Carbon::parse($file->checked_date)->format('d M Y') : 'N/A' }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-gradient-info px-3 py-2">{{ $file->checked_folder }}</span>
                                                            </td>
                                                            <td>
                                                                <input type="checkbox"
                                                                       class="modern-checkbox deliver-checkbox"
                                                                       data-file-id="{{ $file->id }}"
                                                                       {{ $file->deliver ? 'checked' : '' }}>
                                                            </td>
                                                            <td>
                                                                <input type="text"
                                                                       class="modern-input not-deliver-reason-input text-direction-rtl"
                                                                       data-file-id="{{ $file->id }}"
                                                                       value="{{ $file->not_deliver_reason }}"
                                                                       placeholder="Enter reason...">
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
            <script>
                $(document).ready(function () {
                  $('#myTable').DataTable();
            
                });
              </script>
              <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

              <script>
                 const checkboxes = document.querySelectorAll('.deliver-checkbox');

checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', async function() {
        const folderId = this.getAttribute('data-file-id');
        const isChecked = this.checked;

        try {
            // Include the CSRF token in the request headers
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            const response = await axios.post(`/update-deliver/${folderId}`, { isChecked }, {
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                },
            });

            if (response.data.success) {
                // Update the 'selected' attribute of the checkbox based on the isChecked value
                this.checked = isChecked;
            }
        } catch (error) {
            console.error(error);
        }
    });
});
document.addEventListener("DOMContentLoaded", function() {
    const notDeliverReasonInputs = document.querySelectorAll('.not-deliver-reason-input');

    notDeliverReasonInputs.forEach(input => {
        input.addEventListener('change', async function() {
            const fileId = this.getAttribute('data-file-id');
            const newNotDeliverReason = this.value;

            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                const response = await axios.post(`/update-not-deliver-reason/${fileId}`, { newNotDeliverReason }, {
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                    },
                });

                if (response.data.success) {
                    // Optionally, update the view to reflect the change
                }
            } catch (error) {
                console.error(error);
            }
        });
    });
});
              </script>
            <x-footers.auth></x-footers.auth>
        </div>
    </main>
    <x-plugins></x-plugins>

</x-layout>
