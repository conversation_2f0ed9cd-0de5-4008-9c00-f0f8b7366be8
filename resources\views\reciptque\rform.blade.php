<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="reciption"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Reciption Page"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Include Libraries -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2-bootstrap-5-theme.min.css">

        <!-- Modern Styles -->
        <style>
            .modern-card {
                background: #ffffff;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                border: none;
                margin-bottom: 2rem;
                overflow: hidden;
            }

            .modern-card-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1.5rem 2rem;
                border: none;
            }

            .modern-card-body {
                padding: 2rem;
            }

            .modern-form-group {
                margin-bottom: 1.5rem;
            }

            .modern-label {
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 0.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .modern-input {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            .modern-input:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .modern-select {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            .modern-select:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .modern-textarea {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                font-family: 'Jameel Noori Nastaleeq', serif;
                min-height: 120px;
                resize: vertical;
            }

            .modern-textarea:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
            }

            .btn-modern {
                border-radius: 8px;
                padding: 0.75rem 2rem;
                font-weight: 600;
                border: none;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1rem;
            }

            .btn-modern-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .btn-modern-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                color: white;
            }

            .required-field {
                color: #e53e3e;
                font-weight: bold;
            }

            .form-section {
                background: #f8f9ff;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                border: 1px solid #e9ecef;
            }

            .section-title {
                color: #2d3748;
                font-weight: 700;
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1.1rem;
            }

            .white-box {
                font-family: 'Jameel Noori Nastaleeq', serif;
            }

            /* Select2 Custom Styling */
            .select2-container--bootstrap-5 .select2-selection--single {
                border: 2px solid #e9ecef !important;
                border-radius: 8px !important;
                height: 50px !important;
                padding: 0.75rem 1rem !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                background-color: white !important;
            }

            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
                line-height: 32px !important;
                padding-left: 0 !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                color: #495057 !important;
            }

            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
                height: 48px !important;
                right: 10px !important;
            }

            .select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
                border-color: #667eea !important;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            }

            .select2-dropdown {
                border: 2px solid #667eea !important;
                border-radius: 8px !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                z-index: 9999 !important;
            }

            .select2-results__option {
                font-family: 'Jameel Noori Nastaleeq', serif !important;
                padding: 0.75rem 1rem !important;
            }

            .select2-results__option--highlighted {
                background-color: #667eea !important;
                color: white !important;
            }

            .select2-search__field {
                border: 1px solid #e9ecef !important;
                border-radius: 6px !important;
                padding: 0.5rem !important;
                font-family: 'Jameel Noori Nastaleeq', serif !important;
            }

            .alert-danger {
                background-color: #fed7d7;
                border: 1px solid #feb2b2;
                color: #c53030;
                padding: 0.5rem;
                border-radius: 6px;
                margin-top: 0.25rem;
                font-size: 0.875rem;
            }
        </style>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                // Get the input element by its ID
                var dateInput = document.getElementById("rec_date");

                // Create a new date object with the current date
                var currentDate = new Date();

                // Format the current date as "YYYY-MM-DD"
                var formattedDate = currentDate.toISOString().split('T')[0];

                // Set the formatted date as the input's value
                dateInput.value = formattedDate;
            });
        </script>

        <div class="container-fluid py-4">
            <div class="white-box">
                <!-- Header Section -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="text-center">
                            <h4 class="mb-0 text-white">
                                <i class="fas fa-clipboard-list me-2"></i>
                                Darulifta Ahlesunnat Reception Form
                            </h4>
                            <p class="mb-0 opacity-75">Submit new questions and manage reception details</p>
                        </div>
                    </div>

                    <div class="modern-card-body">
                        <form action="{{ route('reciptque.store') }}" method="POST" enctype="multipart/form-data" class="form-horizontal answer-form" role="form" id="action">
                            @csrf

                            <!-- Basic Information Section -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle text-primary"></i>
                                    Basic Information
                                </h5>
                                <div class="row">

                                    <!-- Date -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="rec_date" class="modern-label">
                                                <i class="fas fa-calendar-alt text-primary"></i>
                                                Date <span class="required-field">*</span>
                                            </label>
                                            <input type="date" name="rec_date" id="rec_date" tabindex="1" class="modern-input w-100" required>
                                            @error('date')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Question Type -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="question_type" class="modern-label">
                                                <i class="fas fa-envelope text-primary"></i>
                                                Question Daily/Email <span class="required-field">*</span>
                                            </label>
                                            <select name="question_type" id="question_type" tabindex="1" class="modern-select w-100" required>
                                                <option value="Daily">Daily</option>
                                                <option value="Email">Email</option>
                                            </select>
                                            @error('phone')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Ifta Code -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="ifta_code" class="modern-label">
                                                <i class="fas fa-file-code text-primary"></i>
                                                Ifta Code <span class="required-field">*</span>
                                            </label>
                                            <input type="text" name="ifta_code" id="ifta_code" tabindex="1" class="modern-input w-100" value="{{ old('ifta_code', $newIftaCode) }}" required>
                                            @error('ifta_code')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Phone -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="number_phone" class="modern-label">
                                                <i class="fas fa-phone text-primary"></i>
                                                Phone No
                                            </label>
                                            <input type="tel" name="phone" id="number_phone" tabindex="1" class="modern-input w-100" placeholder="Enter Phone No" minlength="10" maxlength="15" pattern="\d{10,15}" value="">
                                            @error('phone')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Email -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="qs_email" class="modern-label">
                                                <i class="fas fa-envelope text-primary"></i>
                                                Email
                                            </label>
                                            <input type="email" name="email" id="qs_email" tabindex="3" class="modern-input w-100" placeholder="Enter Email" maxlength="100" value="">
                                            @error('email')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Sayel -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="qs_title" class="modern-label">
                                                <i class="fas fa-user text-primary"></i>
                                                Sayel <span class="required-field">*</span>
                                            </label>
                                            <input type="text" name="sayel" id="qs_title" tabindex="2" class="modern-input w-100" placeholder="Sayel" pattern="[A-Z][a-z. -\u0600-\u06FF]{3,99}" value="" required minlength="4" maxlength="100">
                                            @error('name')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Address -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="qs_address" class="modern-label">
                                                <i class="fas fa-map-marker-alt text-primary"></i>
                                                Address
                                            </label>
                                            <input type="text" name="address" id="qs_address" tabindex="4" class="modern-input w-100" placeholder="Address" maxlength="150" pattern="[A-Za-z0-9\s:\/#,.\u0600-\u06FF]{1,150}" value="">
                                            @error('address')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <!-- Muzo -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="issue" class="modern-label">
                                                <i class="fas fa-tags text-primary"></i>
                                                Muzo <span class="required-field">*</span>
                                            </label>
                                            <select id="issue" name="issue" tabindex="5" class="modern-select w-100 searchable-select">
                                                <option value="">Select Muzo</option>
                                                @foreach($issues as $issue)
                                                    <option value="{{ $issue->id }}">{{ $issue->name }}</option>
                                                @endforeach
                                            </select>
                                            @error('issue_category_id')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Zeli Muzo -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="sub_issue" class="modern-label">
                                                <i class="fas fa-tag text-primary"></i>
                                                Zeli Muzo <span class="required-field">*</span>
                                            </label>
                                            <select id="sub_issue" name="sub_issue" tabindex="6" class="modern-select w-100 searchable-select">
                                                <option value="">Select a Zeli-Muzo</option>
                                            </select>
                                            @error('issue_subcategory_id')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Question Details Section -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-question-circle text-primary"></i>
                                    Question Details
                                </h5>
                                <div class="row">
                                    <!-- Question Title -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="question_title" class="modern-label">
                                                <i class="fas fa-heading text-primary"></i>
                                                Question Title <span class="required-field">*</span>
                                            </label>
                                            <input type="text" name="question_title" tabindex="7" class="modern-input w-100" placeholder="Enter Question Title" maxlength="150" pattern="[A-Za-z0-9\s:\/#,.\u0600-\u06FF]{1,150}" value="">
                                            @error('question_title')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Expected Date -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="expected_date" class="modern-label">
                                                <i class="fas fa-calendar-check text-primary"></i>
                                                Expected Date <span class="required-field">*</span>
                                            </label>
                                            <input type="date" name="expected_date" tabindex="8" class="modern-input w-100" id="expected_date" value="">
                                            @error('expected_date')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Branch -->
                                    <div class="col-md-4">
                                        <div class="modern-form-group">
                                            <label for="question_branch" class="modern-label">
                                                <i class="fas fa-building text-primary"></i>
                                                Branch <span class="required-field">*</span>
                                            </label>
                                            <select name="question_branch" tabindex="9" class="modern-select w-100">
                                                @php
                                                    $darulifta = (count(Auth::user()->roles) > 1) ? $branches : $rolebranches;
                                                @endphp
                                                @foreach($darulifta as $branch)
                                                <option value="{{ $branch->darul_name }}">{{ $branch->darul_name }}</option>
                                                @endforeach
                                            </select>
                                            @error('question_branch')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Question Text -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="modern-form-group">
                                            <label for="summernote" class="modern-label">
                                                <i class="fas fa-question-circle text-primary"></i>
                                                Question <span class="required-field">*</span>
                                            </label>
                                            @error('file')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                            <textarea name="question" tabindex="10" class="modern-textarea w-100" id="summernote" rows="6" placeholder="Enter Question Here"></textarea>
                                            @error('question')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Assignment Section -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-user-check text-primary"></i>
                                    Assignment Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="modern-form-group">
                                            <label for="assign_id" class="modern-label">
                                                <i class="fas fa-user-plus text-primary"></i>
                                                Assign Question To Mujeeb
                                            </label>
                                            <select name="assign_id" tabindex="12" class="modern-select w-100">
                                                @php
                                                    $mujeeb = (count(Auth::user()->roles) > 1) ? $user : $rolemujeeb;
                                                @endphp
                                                <option value="" selected>Select Mujeeb For Send Fatwa</option>
                                                @foreach($mujeeb as $assignUser)
                                                <option value="{{ $assignUser->mujeeb_name }}">{{ $assignUser->mujeeb_name }}</option>
                                                @endforeach
                                            </select>
                                            @error('assign_to')
                                            <div class="alert-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Section -->
                            <div class="text-center">
                                <button id="btn_submit" class="btn-modern btn-modern-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                    Submit Question
                                </button>
                            </div>

                            <input type="hidden" id="uploaded-files" name="uploaded">
                            <input type="hidden" name="question_text" class="text">
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
        <script>

    $(document).ready(function () {
        // Initialize Select2 for searchable dropdowns
        $('.searchable-select').select2({
            placeholder: 'Search and select...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap-5',
            dropdownParent: $('body')
        });

        // Handle Muzo change to load Zeli Muzo
        $('#issue').on('change', function () {
            var issueId = $(this).val();
            var subIssueSelect = $('#sub_issue');

            if (issueId) {
                // Show loading state
                subIssueSelect.prop('disabled', true);
                subIssueSelect.empty().append('<option value="">Loading...</option>');

                $.ajax({
                    url: '{{ route("get-sub-issues") }}',
                    type: 'GET',
                    data: {issue_id: issueId},
                    success: function (data) {
                        subIssueSelect.empty();
                        subIssueSelect.append('<option value="">Select a Zeli-Muzo</option>');
                        $.each(data, function (key, value) {
                            subIssueSelect.append('<option value="' + value.id + '">' + value.name + '</option>');
                        });

                        // Re-initialize Select2 for the updated dropdown
                        subIssueSelect.select2('destroy').select2({
                            placeholder: 'Search and select Zeli-Muzo...',
                            allowClear: true,
                            width: '100%',
                            theme: 'bootstrap-5',
                            dropdownParent: $('body')
                        });

                        subIssueSelect.prop('disabled', false);
                    },
                    error: function() {
                        subIssueSelect.empty().append('<option value="">Error loading data</option>');
                        subIssueSelect.prop('disabled', false);
                    }
                });
            } else {
                subIssueSelect.empty();
                subIssueSelect.append('<option value="">Select a Zeli-Muzo</option>');
                subIssueSelect.select2('destroy').select2({
                    placeholder: 'Search and select Zeli-Muzo...',
                    allowClear: true,
                    width: '100%',
                    theme: 'bootstrap-5',
                    dropdownParent: $('body')
                });
            }
        });
    });
    </script>

    <script>
    $(document).ready(function () {
    // Add an event listener for the phone input
    $('#number_phone').on('blur', function () {
        let phone = $(this).val();
        if (phone) {
            // Send an AJAX request to check if the phone exists
            $.ajax({
                url: '{{ route("checkPhoneExist") }}',
                type: 'GET',
                data: { phone: phone },
                success: function (data) {
                    if (data) {
                        // Populate the "sayel" and "address" fields
                        if (data.sayel) {
                            $('#qs_title').val(data.sayel);
                        }
                        if (data.address) {
                            $('#qs_address').val(data.address);
                        }
                    }
                }
            });
        }
    });

    // Add an event listener for the email input
    $('#qs_email').on('blur', function () {
        let email = $(this).val();
        if (email) {
            // Send an AJAX request to check if the email exists
            $.ajax({
                url: '{{ route("checkEmailExist") }}',
                type: 'GET',
                data: { email: email },
                success: function (data) {
                    if (data) {
                        // Populate the "sayel" and "address" fields
                        if (data.sayel) {
                            $('#qs_title').val(data.sayel);
                        }
                        if (data.address) {
                            $('#qs_address').val(data.address);
                        }
                    }
                }
            });
        }
    });
});
$(document).ready(function () {
    // Add an event listener for the question_type select
    $('#question_type').on('change', function () {
        if ($(this).val() === 'Email') {
            // If Email is selected, disable the phone input
            $('#number_phone').prop('disabled', true);
        } else {
            // If Daily is selected, enable the phone input
            $('#number_phone').prop('disabled', false);
        }
    });
});
$(document).ready(function () {
    // Function to calculate and set expected_date
    function setExpectedDate() {
        const recDate = new Date($('#rec_date').val());
        const expectedDate = new Date(recDate);
        expectedDate.setDate(recDate.getDate() + 3);
        const formattedExpectedDate = expectedDate.toISOString().split('T')[0];
        $('#expected_date').val(formattedExpectedDate);
    }

    // Initialize rec_date with the current date
    const currentDate = new Date();
    const formattedCurrentDate = currentDate.toISOString().split('T')[0];
    $('#rec_date').val(formattedCurrentDate);

    // Call the function to set expected_date on initial load
    setExpectedDate();

    // Add event listeners for the rec_date input
    $('#rec_date').on('change', setExpectedDate);

    // Add an event listener for manually adjusting expected_date
    $('#expected_date').on('change', function () {
        // You can add validation or further handling here if needed
    });
});
</script>


        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
