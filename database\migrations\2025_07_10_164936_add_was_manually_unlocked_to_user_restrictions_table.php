<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_restrictions', function (Blueprint $table) {
            $table->boolean('was_manually_unlocked')->default(false)->after('lift_reason');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_restrictions', function (Blueprint $table) {
            $table->dropColumn('was_manually_unlocked');
        });
    }
};
