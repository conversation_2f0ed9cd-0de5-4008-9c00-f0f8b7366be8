<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UnlockRequest;
use Illuminate\Support\Facades\Auth;

class UnlockRequestController extends Controller
{
    public function store(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->back()->with('unlock_error', 'You must be logged in to request unlock.');
        }
        // Prevent duplicate pending requests
        if (UnlockRequest::where('user_id', $user->id)->where('status', 'pending')->exists()) {
            return redirect()->back()->with('unlock_error', 'You already have a pending unlock request.');
        }
        $validated = $request->validate([
            'message' => 'nullable|string|max:1000',
        ]);
        UnlockRequest::create([
            'user_id' => $user->id,
            'message' => $validated['message'] ?? null,
            'status' => 'pending',
        ]);
        return redirect()->back()->with('unlock_success', 'Your unlock request has been sent to Nazim-ul-Umoor.');
    }
}
