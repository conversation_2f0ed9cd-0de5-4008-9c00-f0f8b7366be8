<div>
    <div class="container-bt file-list-section">
        <div class="row text-center">
            <div class="form-group col-md-3">
                <span class="text-black">
                    <i class="material-icons text-sm">add</i>&nbsp;&nbsp;Fatawa For Ok:
                </span>
                <label for="ok" class="upload-area">
                    <i class="material-icons text-sm">cloud_upload</i>&nbsp;&nbsp;<span></span>
                    <input type="file" id="ok1" wire:model.live="ok" data-label="ok" multiple>
                </label>
            </div>

            <div class="form-group col-md-3">
                <span class="text-black">
                    <i class="material-icons text-sm">add</i>&nbsp;&nbsp;Fatawa For Mahl-e-Nazar:
                </span>
                <label for="mahl-e-nazar" class="upload-area">
                    <i class="material-icons text-sm">cloud_upload</i>&nbsp;&nbsp;<span></span>
                    <input type="file" id="mahl-e-nazar" wire:model.live="mahlENazar" data-label="Mahl-e-Nazar" multiple>
                </label>
            </div>

            <div class="form-group col-md-3">
                <span class="text-black">
                    <i class="material-icons text-sm">add</i>&nbsp;&nbsp;Fatawa For Tahqiqi:
                </span>
                <label for="tahqiqi" class="upload-area">
                    <i class="material-icons text-sm">cloud_upload</i>&nbsp;&nbsp;<span></span>
                    <input type="file" id="tahqiqi" wire:model.live="tahqiqi" data-label="Tahqiqi" multiple>
                </label>
            </div>

            <div class="form-group col-md-3">
                <span class="text-black">
                    <i class="material-icons text-sm">add</i>&nbsp;&nbsp;Other Files:
                </span>
                <label for="other" class="upload-area">
                    <i class="material-icons text-sm">cloud_upload</i>&nbsp;&nbsp;<span></span>
                    <input type="file" id="other" wire:model.live="other" data-label="Other" multiple>
                </label>
            </div>
        </div>

        <!-- File List in Dropdown -->
        <div class="col-12 mt-4 file-list-section">
            <h5>Uploaded Files:</h5>

            @foreach($fileList as $type => $files)
                @if(count($files) > 0)
                    <div class="mb-3">
                        <span data-bs-toggle="collapse" data-bs-target="#collapse-{{ $type }}" aria-expanded="false" aria-controls="collapse-{{ $type }}">
                            <i class="material-icons text-sm">add</i>&nbsp;&nbsp;{{ ucfirst(str_replace('-', ' ', $type)) }} Files ({{ count($files) }})
                        </span>
                        <div class="collapse mt-2" id="collapse-{{ $type }}">
                            <ul class="list-group">
                                @foreach($files as $index => $file)
                                @php
                            // Extract the base folder (before the first /) if there are subfolders
                            $baseFolder = explode('/', $file['folder'])[0];
                        @endphp
                                    <li class="list-group-item d-flex justify-content-between align-items-center" style="background-color: #f8f9fa;">
                                        <span>
                                            <strong>File:</strong> {{ $file['name'] }}<br>
                                            <strong>Folder:</strong> {{ $file['folder'] }}
                                        </span>

                                        <!-- View and Download Links -->
                                        <div class="d-flex align-items-center">
                                        <span class="view ">
                                <a href="{{ route('viewCheck', [
                                        'date' => $baseFolder,  // Use only the base folder without subfolders
                                        'folder' => $type,  
                                        'filename' => $file['name']
                                    ]) }}" target="_blank" >
                                    <i class="fas fa-eye">View </i>
                                </a>
                            </span>
                                            
                                        

                                        <!-- Remove Button -->
                                        <span wire:click="removeFile('{{ $type }}', {{ $index }})" class="btn btn-danger">Remove</span>
                                </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    </div>

    @if (session()->has('message'))
        <div class="alert alert-success">
            {{ session('message') }}
        </div>
    @endif
</div>
