<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\DailyPerformance;
use App\Models\DailyPerformanceAttachment;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DailyPerformanceReport extends Component
{
    use AuthorizesRequests, WithFileUploads;

    public $performance_date;
    public $selected_task_id = null;
    public $tasks_completed = '';
    public $challenges_faced = '';
    public $next_day_plan = '';
    public $hours_worked = '';
    public $overall_rating = null;
    public $additional_notes = '';
    public $attachments = [];
    public $uploadedFiles = [];
    public $selectedTaskForAttachments = null;
    public $taskAttachments = [];
    public $selectedTaskForHistory = null;
    public $taskPerformanceHistory = [];
    public $selectedFileForViewing = null;

    public $isSubmitted = false;
    public $existingRecord = null;
    public $hasAssignedTasks = false;
    public $assignedTasks = [];
    public $userDepartments = [];
    public $selectedTaskDepartment = null;
    public $todaysTasks = [];
    public $completedTasksCount = 0;
    public $allUserTasks = [];

    protected $rules = [
        'performance_date' => 'required|date',
        'selected_task_id' => 'required|exists:workflow_tasks,id',
        'tasks_completed' => 'required|string',
        'challenges_faced' => 'nullable|string',
        'next_day_plan' => 'nullable|string',
        'hours_worked' => 'required|numeric|min:0|max:24',
        'additional_notes' => 'nullable|string',
    ];

    public function mount()
    {
        $this->authorize('submit-performance');
        $this->performance_date = Carbon::today()->format('Y-m-d');
        $this->loadAssignedTasks();
        $this->loadUserDepartments();
        $this->checkIfUserHasTasks();
        $this->loadTodaysTasks();
        $this->loadAllUserTasks();
        $this->loadExistingRecord();
    }

    public function render()
    {
        return view('livewire.daily-performance-report');
    }

    public function checkIfUserHasTasks()
    {
        $this->hasAssignedTasks = Task::where('assigned_to', auth()->id())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->exists();
    }

    public function loadAssignedTasks()
    {
        $this->assignedTasks = Task::where('assigned_to', auth()->id())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['department:id,name', 'assignedBy:id,name'])
            ->orderBy('due_date', 'asc')
            ->get();
    }

    public function updatedSelectedTaskId()
    {
        if ($this->selected_task_id) {
            $task = Task::with('department')->find($this->selected_task_id);
            $this->selectedTaskDepartment = $task ? $task->department : null;
            $this->loadExistingRecord();
        }
    }

    public function loadExistingRecord()
    {
        if (!$this->selected_task_id) {
            return;
        }

        $this->existingRecord = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $this->selected_task_id)
            ->where('performance_date', $this->performance_date)
            ->first();

        if ($this->existingRecord) {
            $this->tasks_completed = $this->existingRecord->tasks_completed ?? '';
            $this->challenges_faced = $this->existingRecord->challenges_faced ?? '';
            $this->next_day_plan = $this->existingRecord->next_day_plan ?? '';
            $this->hours_worked = $this->existingRecord->hours_worked ?? '';
            $this->overall_rating = $this->existingRecord->overall_rating ?? null;
            $this->additional_notes = $this->existingRecord->additional_notes ?? '';
            $this->isSubmitted = $this->existingRecord->is_submitted ?? false;
            $this->loadExistingAttachments();
        } else {
            $this->resetFormFields();
        }
    }

    public function resetFormFields()
    {
        $this->tasks_completed = '';
        $this->challenges_faced = '';
        $this->next_day_plan = '';
        $this->hours_worked = '';
        $this->overall_rating = null;
        $this->additional_notes = '';
        $this->isSubmitted = false;
    }

    public function loadTodaysTasks()
    {
        $this->todaysTasks = Task::where('assigned_to', auth()->id())
            ->where('due_date', $this->performance_date)
            ->with(['assignedBy', 'department'])
            ->get();

        $this->completedTasksCount = $this->todaysTasks->where('status', 'completed')->count();
    }

    public function loadUserDepartments()
    {
        $user = auth()->user();
        
        // For Abid Madani, show all departments he's assigned to without role context
        if ($user->name === 'Abid Madani') {
            // Get all departments from both regular assignments and department-specific assignments
            $regularDepartments = $user->departments()->active()->select('departments.id', 'departments.name')->get();
            $supervisorDepartments = $user->supervisorDepartments()->select('departments.id', 'departments.name')->get();
            $assistantDepartments = $user->assistantDepartments()->select('departments.id', 'departments.name')->get();
            
            // Merge all departments and remove duplicates
            $allDepartments = $regularDepartments
                ->merge($supervisorDepartments)
                ->merge($assistantDepartments)
                ->unique('id')
                ->sortBy('name');
                
            $this->userDepartments = $allDepartments;
        } else {
            $this->userDepartments = $user->departments()->active()->select('departments.id', 'departments.name')->get();
        }
    }

    public function loadAllUserTasks()
    {
        // Load all tasks assigned to the user with their performance reports
        $this->allUserTasks = Task::where('assigned_to', auth()->id())
            ->with([
                'assignedBy:id,name',
                'department:id,name',
                'dailyPerformances' => function($query) {
                    $query->where('user_id', auth()->id())
                          ->where('is_submitted', true)
                          ->with('attachments')
                          ->orderBy('performance_date', 'desc');
                }
            ])
            ->orderBy('due_date', 'asc')
            ->limit(50) // Limit to prevent timeout
            ->get();
    }

    public function updatedPerformanceDate()
    {
        $this->loadTodaysTasks();
        // Don't reload all user tasks when date changes - we want to see all performance reports
        // $this->loadAllUserTasks();
        $this->loadExistingRecord();
    }

    public function saveAsDraft()
    {
        $this->validate();

        $task = Task::find($this->selected_task_id);

        $data = [
            'user_id' => auth()->id(),
            'task_id' => $this->selected_task_id,
            'department_id' => $task ? $task->department_id : null,
            'performance_date' => $this->performance_date,
            'tasks_completed' => $this->tasks_completed,
            'challenges_faced' => $this->challenges_faced,
            'next_day_plan' => $this->next_day_plan,
            'hours_worked' => $this->hours_worked,
            'overall_rating' => null,
            'additional_notes' => $this->additional_notes,
            'is_submitted' => false,
        ];

        $this->existingRecord = DailyPerformance::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'task_id' => $this->selected_task_id,
                'performance_date' => $this->performance_date,
            ],
            $data
        );

        session()->flash('message', 'Performance report saved as draft.');
    }

    public function submitReport()
    {
        $this->validate();

        $task = Task::find($this->selected_task_id);

        $data = [
            'user_id' => auth()->id(),
            'task_id' => $this->selected_task_id,
            'department_id' => $task ? $task->department_id : null,
            'performance_date' => $this->performance_date,
            'tasks_completed' => $this->tasks_completed,
            'challenges_faced' => $this->challenges_faced,
            'next_day_plan' => $this->next_day_plan,
            'hours_worked' => $this->hours_worked,
            'overall_rating' => null,
            'additional_notes' => $this->additional_notes,
            'is_submitted' => true,
            'submitted_at' => Carbon::now(),
        ];

        $this->existingRecord = DailyPerformance::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'task_id' => $this->selected_task_id,
                'performance_date' => $this->performance_date,
            ],
            $data
        );

        $this->isSubmitted = true;
        session()->flash('message', 'Performance report submitted successfully!');

        // Redirect to dashboard or previous page
        return redirect()->route('dashboard');
    }

    public function getRatingColor($rating)
    {
        return match($rating) {
            'poor' => 'danger',
            'fair' => 'warning',
            'good' => 'info',
            'excellent' => 'success',
            default => 'secondary'
        };
    }

    public function getTaskStatusColor($status)
    {
        return match($status) {
            'pending' => 'warning',
            'in_progress' => 'info',
            'completed' => 'success',
            'cancelled' => 'secondary',
            default => 'secondary'
        };
    }

    public function markTaskCompleted($taskId)
    {
        $task = Task::findOrFail($taskId);
        
        if ($task->assigned_to === auth()->id()) {
            $task->markCompleted();
            $this->loadTodaysTasks();
            session()->flash('message', 'Task marked as completed.');
        }
    }

    public function autoFillTasksCompleted()
    {
        $completedTasks = $this->todaysTasks->where('status', 'completed');
        
        if ($completedTasks->count() > 0) {
            $tasksList = $completedTasks->map(function ($task) {
                return "• {$task->title}" . ($task->completion_notes ? " - {$task->completion_notes}" : "");
            })->implode("\n");
            
            $this->tasks_completed = $tasksList;
        }
    }

    public function canEdit()
    {
        return !$this->isSubmitted || auth()->user()->isNazim();
    }

    public function updateMahlENazarCount()
    {
        // Update the User model method to get actual count
        $user = auth()->user();
        $count = \DB::table('uploaded_files')
            ->where('ftype', 'Mahl e Nazar')
            ->where(function ($query) use ($user) {
                $query->where('checker', $user->name)
                      ->orWhere('transfer_by', $user->name);
            })
            ->count();

        return $count;
    }

    public function loadExistingAttachments()
    {
        if ($this->existingRecord) {
            $this->uploadedFiles = $this->existingRecord->attachments()->get()->toArray();
        }
    }

    public function updatedAttachments()
    {
        $this->validate([
            'attachments.*' => 'file|max:10240|mimes:jpg,jpeg,png,gif,pdf,doc,docx,txt,xls,xlsx,ppt,pptx',
        ]);

        // Auto-upload files when selected (without save draft requirement)
        if (!empty($this->attachments)) {
            $this->autoUploadFiles();
        }
    }

    public function uploadFiles()
    {
        if (!$this->existingRecord) {
            session()->flash('error', 'Please save the performance report first before uploading files.');
            return;
        }

        if (empty($this->attachments)) {
            session()->flash('error', 'Please select files to upload.');
            return;
        }

        $this->validate([
            'attachments.*' => 'required|file|max:10240|mimes:jpg,jpeg,png,gif,pdf,doc,docx,txt,xls,xlsx,ppt,pptx',
        ]);

        foreach ($this->attachments as $file) {
            $this->saveAttachment($file);
        }

        $this->attachments = [];
        $this->loadExistingAttachments();
        session()->flash('message', 'Files uploaded successfully!');
    }

    public function autoUploadFiles()
    {
        // Create a draft record if it doesn't exist
        if (!$this->existingRecord && $this->selected_task_id) {
            $task = Task::find($this->selected_task_id);

            $data = [
                'user_id' => auth()->id(),
                'task_id' => $this->selected_task_id,
                'department_id' => $task ? $task->department_id : null,
                'performance_date' => $this->performance_date,
                'tasks_completed' => $this->tasks_completed ?: 'Auto-created for file upload',
                'challenges_faced' => $this->challenges_faced,
                'next_day_plan' => $this->next_day_plan,
                'hours_worked' => $this->hours_worked ?: 0,
                'overall_rating' => null,
                'additional_notes' => $this->additional_notes,
                'is_submitted' => false,
            ];

            $this->existingRecord = DailyPerformance::updateOrCreate(
                [
                    'user_id' => auth()->id(),
                    'task_id' => $this->selected_task_id,
                    'performance_date' => $this->performance_date,
                ],
                $data
            );
        }

        // Upload files directly
        if ($this->existingRecord && !empty($this->attachments)) {
            foreach ($this->attachments as $file) {
                $this->saveAttachment($file);
            }

            $this->attachments = [];
            $this->loadExistingAttachments();
            session()->flash('message', 'Files uploaded successfully!');
        }
    }

    private function saveAttachment($file)
    {
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $fileName = Str::uuid() . '.' . $extension;
        $filePath = 'daily-performance-attachments/' . $fileName;

        // Store the file
        $file->storeAs('daily-performance-attachments', $fileName, 'public');

        // Save to database
        DailyPerformanceAttachment::create([
            'daily_performance_id' => $this->existingRecord->id,
            'original_name' => $originalName,
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_type' => $extension,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'uploaded_by' => auth()->id(),
        ]);
    }

    public function removeAttachment($attachmentId)
    {
        $attachment = DailyPerformanceAttachment::find($attachmentId);
        
        if ($attachment && $attachment->daily_performance_id === $this->existingRecord->id) {
            $attachment->delete();
            $this->loadExistingAttachments();
            session()->flash('message', 'File removed successfully!');
        }
    }

    public function getMaxFileSize()
    {
        return '10MB';
    }

    public function getAllowedFileTypes()
    {
        return 'Images (JPG, PNG, GIF), PDF, Word Documents (DOC, DOCX), Excel (XLS, XLSX), PowerPoint (PPT, PPTX), Text files (TXT)';
    }

    public function getFileIcon($file)
    {
        $extension = strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION));
        
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'])) {
            return 'fas fa-image text-success';
        } elseif ($extension === 'pdf') {
            return 'fas fa-file-pdf text-danger';
        } elseif (in_array($extension, ['doc', 'docx'])) {
            return 'fas fa-file-word text-primary';
        } elseif (in_array($extension, ['xls', 'xlsx'])) {
            return 'fas fa-file-excel text-success';
        } elseif (in_array($extension, ['ppt', 'pptx'])) {
            return 'fas fa-file-powerpoint text-warning';
        } elseif ($extension === 'txt') {
            return 'fas fa-file-alt text-secondary';
        } else {
            return 'fas fa-file text-secondary';
        }
    }

    public function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function editTaskPerformance($taskId)
    {
        $task = Task::find($taskId);
        if (!$task || $task->assigned_to !== auth()->id()) {
            session()->flash('error', 'Task not found or not assigned to you.');
            return;
        }

        // Load performance record for this task (most recent one)
        $performance = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $taskId)
            ->orderBy('performance_date', 'desc')
            ->first();

        if ($performance) {
            // Set the form to edit this performance
            $this->selected_task_id = $taskId;
            $this->performance_date = $performance->performance_date->format('Y-m-d');
            $this->tasks_completed = $performance->tasks_completed ?? '';
            $this->challenges_faced = $performance->challenges_faced ?? '';
            $this->next_day_plan = $performance->next_day_plan ?? '';
            $this->hours_worked = $performance->hours_worked ?? '';
            $this->additional_notes = $performance->additional_notes ?? '';
            $this->existingRecord = $performance;
            $this->isSubmitted = $performance->is_submitted ?? false;
            $this->loadExistingAttachments();
            $this->updatedSelectedTaskId();
            
            session()->flash('message', "Performance record loaded for editing (Date: {$performance->performance_date->format('M d, Y')}).");
        } else {
            // No performance record exists, set up for new entry
            $this->selected_task_id = $taskId;
            $this->performance_date = Carbon::today()->format('Y-m-d');
            $this->resetFormFields();
            $this->existingRecord = null;
            $this->uploadedFiles = [];
            $this->isSubmitted = false;
            $this->updatedSelectedTaskId();
            
            session()->flash('message', 'No performance record found. Creating a new performance entry for this task.');
        }
    }

    public function deleteTaskPerformance($taskId)
    {
        // Get all performance records for this task
        $performances = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $taskId)
            ->with('attachments')
            ->get();

        if ($performances->count() > 0) {
            $deletedCount = 0;
            $attachmentCount = 0;
            
            foreach ($performances as $performance) {
                // Delete attachments first
                foreach ($performance->attachments as $attachment) {
                    $attachment->delete();
                    $attachmentCount++;
                }
                
                // Delete performance record
                $performance->delete();
                $deletedCount++;
            }
            
            // Reset form if this was the currently selected task
            if ($this->selected_task_id == $taskId) {
                $this->resetFormFields();
                $this->selected_task_id = null;
                $this->existingRecord = null;
                $this->uploadedFiles = [];
                $this->isSubmitted = false;
            }
            
            $message = "Deleted {$deletedCount} performance record(s)";
            if ($attachmentCount > 0) {
                $message .= " and {$attachmentCount} attachment(s)";
            }
            $message .= " for this task.";
            
            session()->flash('message', $message);
        } else {
            session()->flash('error', 'No performance records found to delete for this task.');
        }
    }

    public function viewFile($attachmentId)
    {
        $attachment = DailyPerformanceAttachment::find($attachmentId);
        
        if ($attachment) {
            // Check if file can be viewed in browser
            if ($this->isViewableInBrowser($attachment)) {
                $this->selectedFileForViewing = $attachment->toArray();
            } else {
                // For non-viewable files, just download them
                return redirect()->route('daily-performance.attachment.download', $attachmentId);
            }
        } else {
            session()->flash('error', 'File not found.');
        }
    }

    public function closeFileViewer()
    {
        $this->selectedFileForViewing = null;
    }

    private function isViewableInBrowser($attachment)
    {
        $viewableTypes = [
            'application/pdf',
            'text/plain',
            'text/html',
            'text/css',
            'text/javascript',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml'
        ];
        
        return in_array($attachment->mime_type, $viewableTypes);
    }

    public function getFileViewUrl($attachmentId)
    {
        return route('daily-performance.attachment.view', $attachmentId);
    }

    public function canViewInBrowser($file)
    {
        $viewableTypes = [
            'application/pdf',
            'text/plain',
            'text/html',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml'
        ];
        
        return in_array($file['mime_type'] ?? '', $viewableTypes);
    }

    public function viewTaskAttachments($taskId)
    {
        $performance = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $taskId)
            ->with('attachments')
            ->orderBy('performance_date', 'desc')
            ->first();

        if ($performance && $performance->attachments->count() > 0) {
            $this->selectedTaskForAttachments = $taskId;
            $this->taskAttachments = $performance->attachments->toArray();
        } else {
            session()->flash('error', 'No attachments found for this task.');
        }
    }

    public function closeAttachmentsModal()
    {
        $this->selectedTaskForAttachments = null;
        $this->taskAttachments = [];
    }

    public function getTaskPerformanceStatus($taskId)
    {
        $performance = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $taskId)
            ->orderBy('performance_date', 'desc')
            ->first();

        if (!$performance) {
            return ['status' => 'none', 'label' => 'No Report', 'color' => 'secondary'];
        }

        if ($performance->is_submitted) {
            return ['status' => 'submitted', 'label' => 'Submitted', 'color' => 'success'];
        } else {
            return ['status' => 'draft', 'label' => 'Draft', 'color' => 'warning'];
        }
    }

    public function getTaskAttachmentCount($taskId)
    {
        // Count attachments from all performance records for this task
        $totalAttachments = DailyPerformanceAttachment::whereHas('dailyPerformance', function($q) use ($taskId) {
            $q->where('user_id', auth()->id())
              ->where('task_id', $taskId);
        })->count();

        return $totalAttachments;
    }

    public function getTaskAttachments($taskId)
    {
        return DailyPerformanceAttachment::whereHas('dailyPerformance', function ($query) use ($taskId) {
            $query->where('task_id', $taskId)
                  ->where('user_id', auth()->id());
        })->get()->toArray();
    }

    public function getLatestTaskPerformance($taskId)
    {
        return DailyPerformance::where('task_id', $taskId)
            ->where('user_id', auth()->id())
            ->where('is_submitted', true)
            ->orderBy('performance_date', 'desc')
            ->first();
    }

    public function getFileExtension($filename)
    {
        return strtoupper(pathinfo($filename, PATHINFO_EXTENSION));
    }

    public function getTotalFileSize($files)
    {
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += $file['file_size'] ?? 0;
        }
        return $this->formatFileSize($totalSize);
    }

    public function viewPerformanceAttachments($performanceId)
    {
        $performance = DailyPerformance::with('attachments')->find($performanceId);
        if ($performance && $performance->user_id === auth()->id()) {
            $this->selectedTaskForAttachments = $performanceId;
            $this->taskAttachments = $performance->attachments->toArray();
        }
    }

    public function viewPerformanceDetails($performanceId)
    {
        $performance = DailyPerformance::with(['task', 'attachments'])->find($performanceId);
        if ($performance && $performance->user_id === auth()->id()) {
            // You can implement a modal or redirect to show full performance details
            session()->flash('message', 'Performance details: ' . $performance->tasks_completed);
        }
    }

    public function viewTaskPerformanceHistory($taskId)
    {
        $performances = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $taskId)
            ->with('attachments')
            ->orderBy('performance_date', 'desc')
            ->get();

        if ($performances->count() > 0) {
            $this->selectedTaskForHistory = $taskId;
            $this->taskPerformanceHistory = $performances->toArray();
        } else {
            session()->flash('error', 'No performance history found for this task.');
        }
    }

    public function closeHistoryModal()
    {
        $this->selectedTaskForHistory = null;
        $this->taskPerformanceHistory = [];
    }

    public function editSpecificPerformance($performanceId)
    {
        $performance = DailyPerformance::where('id', $performanceId)
            ->where('user_id', auth()->id())
            ->first();

        if ($performance) {
            // Set the form to edit this specific performance
            $this->selected_task_id = $performance->task_id;
            $this->performance_date = $performance->performance_date->format('Y-m-d');
            $this->tasks_completed = $performance->tasks_completed ?? '';
            $this->challenges_faced = $performance->challenges_faced ?? '';
            $this->next_day_plan = $performance->next_day_plan ?? '';
            $this->hours_worked = $performance->hours_worked ?? '';
            $this->additional_notes = $performance->additional_notes ?? '';
            $this->existingRecord = $performance;
            $this->isSubmitted = $performance->is_submitted ?? false;
            $this->loadExistingAttachments();
            $this->updatedSelectedTaskId();
            
            // Close the history modal
            $this->closeHistoryModal();
            
            session()->flash('message', "Performance record loaded for editing (Date: {$performance->performance_date->format('M d, Y')}).");
        } else {
            session()->flash('error', 'Performance record not found.');
        }
    }

    public function deleteSpecificPerformance($performanceId)
    {
        $performance = DailyPerformance::where('id', $performanceId)
            ->where('user_id', auth()->id())
            ->with('attachments')
            ->first();

        if ($performance) {
            $attachmentCount = $performance->attachments->count();
            
            // Delete attachments first
            foreach ($performance->attachments as $attachment) {
                $attachment->delete();
            }
            
            // Delete performance record
            $performance->delete();
            
            // Reset form if this was the currently selected performance
            if ($this->existingRecord && $this->existingRecord->id == $performanceId) {
                $this->resetFormFields();
                $this->selected_task_id = null;
                $this->existingRecord = null;
                $this->uploadedFiles = [];
                $this->isSubmitted = false;
            }
            
            // Refresh the history modal
            if ($this->selectedTaskForHistory) {
                $this->viewTaskPerformanceHistory($this->selectedTaskForHistory);
            }
            
            $message = "Performance record deleted";
            if ($attachmentCount > 0) {
                $message .= " along with {$attachmentCount} attachment(s)";
            }
            $message .= ".";
            
            session()->flash('message', $message);
        } else {
            session()->flash('error', 'Performance record not found.');
        }
    }
}
