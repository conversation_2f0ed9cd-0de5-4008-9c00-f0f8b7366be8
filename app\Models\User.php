<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\CanResetPassword;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'location',
        'phone',
        'about',
        'password_confirmation',
        'mahl_e_nazar_limit', // Allow admin to set per-user limit
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];
    
    public function setPasswordAttribute($password)
    {
        $this->attributes['password'] = bcrypt($password);
    }
    public function roles(){
        return $this->belongsToMany(Role::class);
    }

    /**
     * Get the departments this user is assigned to.
     */
    public function departments()
    {
        return $this->belongsToMany(Department::class, 'user_departments')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get tasks assigned to this user.
     */
    public function assignedTasks()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    /**
     * Get tasks assigned by this user.
     */
    public function createdTasks()
    {
        return $this->hasMany(Task::class, 'assigned_by');
    }

    /**
     * Get daily performance records for this user.
     */
    public function dailyPerformance()
    {
        return $this->hasMany(DailyPerformance::class);
    }

    /**
     * Get active restrictions for this user.
     */
    public function activeRestrictions()
    {
        return $this->hasMany(UserRestriction::class)->where('is_active', true);
    }

    /**
     * Get all restrictions for this user.
     */
    public function restrictions()
    {
        return $this->hasMany(UserRestriction::class);
    }

    /**
     * Get assistants if this user is a supervisor.
     */
    public function assistants()
    {
        return $this->belongsToMany(User::class, 'supervisor_assistants', 'supervisor_id', 'assistant_id')
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get supervisor if this user is an assistant.
     */
    public function supervisor()
    {
        return $this->belongsToMany(User::class, 'supervisor_assistants', 'assistant_id', 'supervisor_id')
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps()
                    ->first();
    }

    /**
     * Get department supervisor/assistant assignments for this user.
     */
    public function departmentAssignments()
    {
        return $this->hasMany(DepartmentSupervisorAssistant::class);
    }

    /**
     * Get active department supervisor/assistant assignments for this user.
     */
    public function activeDepartmentAssignments()
    {
        return $this->hasMany(DepartmentSupervisorAssistant::class)->active();
    }

    /**
     * Get departments where this user is a supervisor.
     */
    public function supervisorDepartments()
    {
        return $this->belongsToMany(Department::class, 'department_supervisor_assistants')
                    ->select('departments.id', 'departments.name', 'departments.description', 'departments.is_active')
                    ->wherePivot('role_type', DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type')
                    ->withTimestamps();
    }

    /**
     * Get departments where this user is an assistant.
     */
    public function assistantDepartments()
    {
        return $this->belongsToMany(Department::class, 'department_supervisor_assistants')
                    ->select('departments.id', 'departments.name', 'departments.description', 'departments.is_active')
                    ->wherePivot('role_type', DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type', 'supervisor_id')
                    ->withTimestamps();
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole($roleName)
    {
        return $this->roles->contains('name', $roleName);
    }

    /**
     * Check if user has any of the specified roles.
     */
    public function hasAnyRole($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }
        return $this->roles->whereIn('name', $roles)->isNotEmpty();
    }

    /**
     * Check if user is a Superior (Shoba Zimmedar).
     */
    public function isSuperior()
    {
        return $this->hasRole('Superior');
    }

    /**
     * Check if user is a Nazim (Admin).
     */
    public function isNazim()
    {
        return $this->hasRole('Nazim') || $this->hasRole('Admin') || $this->hasRole('nazim-ul-umoor');
    }

    /**
     * Check if user is a Mujeeb.
     */
    public function isMujeeb()
    {
        return $this->hasRole('mujeeb') || $this->hasRole('Muawin');
    }

    /**
     * Get all department assignments with role information.
     */
    public function allDepartmentAssignments()
    {
        $supervisorDepts = $this->supervisorDepartments()->get()->map(function ($dept) {
            $dept->role_context = 'supervisor';
            return $dept;
        });
        
        $assistantDepts = $this->assistantDepartments()->get()->map(function ($dept) {
            $dept->role_context = 'assistant';
            return $dept;
        });
        
        // Merge and remove duplicates based on department ID
        return $supervisorDepts->merge($assistantDepts)->unique('id');
    }

    /**
     * Check if user has submitted today's performance for all assigned tasks.
     */
    public function hasSubmittedTodaysPerformance()
    {
        // If user has no assigned tasks, they don't need to submit performance
        if (!$this->hasAssignedTasks()) {
            return true;
        }

        // Get all active tasks assigned to user
        $activeTasks = $this->assignedTasks()
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->pluck('id');

        if ($activeTasks->isEmpty()) {
            return true;
        }

        // Check if user has submitted performance for at least one task today
        $submittedToday = DailyPerformance::where('user_id', $this->id)
            ->where('performance_date', Carbon::today())
            ->where('is_submitted', true)
            ->whereIn('task_id', $activeTasks)
            ->exists();

        return $submittedToday;
    }

    /**
     * Check if user has any active restrictions.
     */
    public function hasActiveRestrictions()
    {
        return UserRestriction::hasActiveRestrictions($this->id);
    }

    /**
     * Check if user has any assigned tasks.
     */
    public function hasAssignedTasks()
    {
        return $this->assignedTasks()
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->exists();
    }

    /**
     * Get user's Mahl-e-Nazar fatawa count.
     */
    public function getMahlENazarCount()
    {
        return \DB::table('uploaded_files')
            ->where('ftype', 'Mahl e Nazar')
            ->where(function ($query) {
                $query->where('checker', $this->name)
                      ->orWhere('transfer_by', $this->name);
            })
            ->count();
    }

    public function unlockRequests()
    {
        return $this->hasMany(UnlockRequest::class);
    }

}
