<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;
use Illuminate\Http\Request; // Add this line
use Barryvdh\DomPDF\Facade\Pdf; // Import the PDF facade




// #[Layout('layouts.app')]
class SentFatawa extends Component
{
    public $selectedMonths = [];
    public $checkerlist;
    public $munsab;
    public $datefilter;
    public $showDetail = false;
    public $showQue = false;
    public $showChat = false;
    public $codebylower;
    public $message;
    public $allfatawa;
    public $userName;
    public $checker;
    Public $obtain;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $remainingFatawa;
    Public $sendingFatawa;
    public $daruliftaNames;
    public $checkers;
    public $mailfolderDate;
    public $darulifta;
    public $mailfolder;
    public $mujeebs;
    public $selectedmujeeb = 'all';
    public $selectedfileCode = 'all';
    public $selectedmufti = 'all';
    public $selectedchecked = 'all_checked';
    public $selectedTimeFrame= 'this_month';
    public $tempSelectedMonths = [];
    public $tempSelectedTimeFrame;
    public $daruliftalist;
    public $startDate;
    public $endDate;
    public $tempStartDate;
    public $tempEndDate;
    public $fileCode;

    // Role-based properties (same as RemainingFatawa)
    public $roleInfo = [];

    // protected $listeners = ['updateSelectedTimeFrame'];

    public function mount(Request $request, $darulifta = null, $mailfolder = null)
        {
            $this->selectedfileCode = $request->query('selectedfileCode', $this->selectedfileCode);
            $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);
            $this->selectedmufti = $request->query('selectedmufti', $this->selectedmufti);
            $this->selectedchecked = $request->query('selectedchecked', $this->selectedchecked);
            $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);
            $this->selectedMonths = request()->query('selectedMonths', []);
            if (is_string($this->selectedMonths)) {
        // Convert comma-separated string to array
                $this->selectedMonths = explode(',', $this->selectedMonths);
            }
            $this->tempSelectedTimeFrame = $this->selectedTimeFrame;
            $this->tempSelectedMonths = $this->selectedMonths;
            $this->startDate = $request->query('startDate');
            $this->endDate = $request->query('endDate');
            $this->tempStartDate = $this->startDate;
            $this->tempEndDate = $this->endDate;
            $this->showDetail = $request->query('showDetail', false) == '1';
            $this->showQue = $request->query('showQue', false) == '1';
            $this->showChat = $request->query('showChat', false) == '1';

            // Determine user role (same as RemainingFatawa)
            $this->determineUserRole();

            $user = Auth::user();
$userRoles = $user->roles;
$roleNames = $userRoles->pluck('name')->toArray();

// Check if the user has the 'Admin' role
if (in_array('Admin', $roleNames)) {
    $this->userName = Auth::user()->name; // Get the authenticated user's name

    // Check if the username is not 'Mufti Ali Asghar'
    if ($this->userName !== 'Mufti Ali Asghar') {

        // Only execute if selectedmufti is not 'transfer_checked'
        if ($this->selectedmufti !== 'transfer_checked') {

            // Query the 'checker' table to find the folder_id based on the user's name
            $checker = DB::table('checker')
                ->where('checker_name', $this->userName)
                ->first();

            // Set selectedmufti to the folder_id or null if the checker isn't found
            $this->selectedmufti = $checker->folder_id ?? null;
        }
    }
}

            $this->checker = DB::table('checker')
                ->where('checker_name', $this->userName)
                ->first()
                ->folder_id ?? null;
                $this->munsab = DB::table('checker')
            ->where('checker_name', $this->userName)
            ->first()
            ->munsab ?? null;
        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;

        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();


        // Additional initialization code
    }
    // public function updatedShowQue($value)
    // {
    //     $this->dispatch('toggleAllOpen', ['value' => $value]);
    // }


    public function selectFileCode($fileCode)
    {
        $this->selectedfileCode = $fileCode;
    }
    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->startDate && $this->endDate && empty($this->selectedMonths)) {
            $this->selectedTimeFrame = 'custom';
            $this->tempSelectedTimeFrame = 'custom';
        }
        $this->remainingFatawadata();
        $this->sendingFatawadata();

    }

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->tempSelectedTimeFrame = $timeFrame;
        if ($timeFrame === 'all') {
            // Resetting the values
            $this->tempStartDate = null;
            $this->tempEndDate = null;
            $this->selectedMonths = [];
            $this->tempSelectedMonths = [];

            // Optional: Debugging statements
            // dd($this->tempStartDate, $this->tempEndDate, $this->selectedMonths, $this->tempSelectedMonths);
        }

    }
    public function updateSelectedFileCode($fileCodeframe)
    {
        $this->selectedfileCode = $fileCodeframe;
    }
    public function updateSelectedMujeeb($mujeebFrame)
    {
        $this->selectedmujeeb = $mujeebFrame;
    }
    public function updateSelectedMufti($muftiFrame)
    {
        $this->selectedmufti = $muftiFrame;
    }
    public function updateSelectedChecked($checked)
    {
        $this->selectedchecked = $checked;
    }

    /**
     * Determine user role and set role-based properties (same as RemainingFatawa)
     */
    private function determineUserRole()
    {
        $user = Auth::user();
        $userName = $user->name;
        $userRoles = $user->roles->pluck('name')->toArray();

        // Set default values
        $this->roleInfo = [
            'isSuperAdmin' => false,
            'isChecker' => false,
            'isMujeeb' => false,
            'userName' => $userName,
            'checkerName' => null,
            'mujeebName' => null,
            'darulName' => null,
            'assignedMujeebs' => [],
        ];

        // Check for SuperAdmin role (highest priority)
        if (in_array('SuperAdmin', $userRoles)) {
            $this->roleInfo['isSuperAdmin'] = true;
            return;
        }

        // Check for Checker role (second priority)
        if (in_array('Checker', $userRoles)) {
            $checker = DB::table('checker')
                ->where('checker_name', $userName)
                ->first();

            if ($checker) {
                $this->roleInfo['isChecker'] = true;
                $this->roleInfo['checkerName'] = $userName;
                $this->roleInfo['checkerFolderId'] = $checker->folder_id ?? null;
                return;
            }
        }

        // Check for Mujeeb role (lowest priority)
        if (in_array('Mujeeb', $userRoles)) {
            $mujeeb = DB::table('mujeebs')
                ->where('mujeeb_name', $userName)
                ->first();

            if ($mujeeb) {
                $this->roleInfo['isMujeeb'] = true;
                $this->roleInfo['mujeebName'] = $userName;
                $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
                return;
            }
        }

        // Check if user is in mujeebs table but not assigned the mujeeb role
        $mujeeb = DB::table('mujeebs')
            ->where('mujeeb_name', $userName)
            ->first();

        if ($mujeeb) {
            $this->roleInfo['isMujeeb'] = true;
            $this->roleInfo['mujeebName'] = $userName;
            $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
        }
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);

        // Handle Mujeeb role filtering (same as RemainingFatawa)
        if ($this->roleInfo['isMujeeb']) {
            // If user is mujeeb, only show their darul's data
            $this->daruliftaNames = [$this->roleInfo['darulName']];
            $this->mujeebs = [$this->roleInfo['userName']];

            // Load file codes and checkers for this mujeeb's department
            $this->fileCode = DB::table('uploaded_files')
                ->select('file_code')
                ->where('selected', 1)
                ->where('darulifta_name', $this->roleInfo['darulName'])
                ->where('sender', $this->roleInfo['userName'])
                ->distinct()
                ->orderBy('file_code')
                ->pluck('file_code');

            $this->checkers = DB::table('checker')
                ->select('folder_id')
                ->distinct()
                ->pluck('folder_id');
            return;
        }

        if ($this->darulifta === null) {
            if (count(Auth::user()->roles) > 1) {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('uploaded_files.darulifta_name')
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->pluck('uploaded_files.darulifta_name');

                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                $this->fileCode = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('file_code')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->orderBy('file_code')
                    ->pluck('file_code');
                    $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
            } else {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', $firstRoleName)
                    ->pluck('darulifta_name');

                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                $this->fileCode = DB::table('uploaded_files')
                    ->select('file_code')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('file_code');
                    $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
            }
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('darulifta_name');

            $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('selected', 1)
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('sender');
            $this->fileCode = DB::table('uploaded_files')
                ->select('file_code')
                ->where('selected', 1)
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('file_code');
                $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
        }
    }
    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')

        ->distinct()

        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()

    ->pluck('mail_folder_date');
    }
}

public function toggleViral($fileId)
    {
        // Find the file in the list

            // Update the database
            DB::table('uploaded_files')
                ->where('id', $fileId)
                ->update([
                    'viral' => $file->viral ? 0 : Auth::id(), // Toggle the viral status
                ]);

            // Reload the files
            $this->loadFiles();

            session()->flash('message', 'Viral status updated successfully.');

    }
public function sendMessage($iftaCode)
{

    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.


    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}

public function render()
    {

        // $this->obtain = DB::table('uploaded_files')
        // ->where('selected', 0)
        // // Add any additional conditions here
        // ->select('mail_folder_date', DB::raw('SUM(total_score) as total_score_sum'))
        // ->groupBy('mail_folder_date')
        // ->havingRaw('COUNT(mail_folder_date) > 1')
        // ->get();
        $this->daruliftalist = DB::table('uploaded_files')
        ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('uploaded_files.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('uploaded_files.darulifta_name');

        // $this->mujeebs = DB::table('uploaded_files')
        // ->select('sender')
        // ->where('selected',1)
        // ->where('darulifta_name',$this->darulifta)
        // ->distinct()

        // ->pluck('sender');

        // Cache static data that doesn't change often
        $this->checkerlist = cache()->remember('checkerlist', 3600, function() {
            return DB::table('checker')->get();
        });

        $this->que_day_r = cache()->remember('questions_' . md5(serialize($this->daruliftaNames)), 1800, function() {
            return DB::table('questions')
                ->whereIn('question_branch', $this->daruliftaNames)
                ->get();
        });

        // Only load allfatawa when showDetail is enabled and apply filters
        if ($this->showDetail) {
            $this->allfatawa = DB::table('uploaded_files')
                ->whereIn('darulifta_name', $this->daruliftaNames)
                ->where('selected', 0) // Only get sent fatawa
                ->orderBy('id', 'asc')
                ->get();
        } else {
            $this->allfatawa = collect(); // Empty collection when not needed
        }

        $this->datefilter = cache()->remember('datefilter_uploaded_files', 3600, function() {
            return DB::table('uploaded_files')
                ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
                ->orderBy('year', 'asc')
                ->orderBy('month', 'asc')
                ->get();
        });

        // Only load codebylower when needed and apply filters
        if ($this->showDetail) {
            $daruliftaNamesArray = is_array($this->daruliftaNames) ? $this->daruliftaNames : $this->daruliftaNames->toArray();
            $this->codebylower = DB::table('uploaded_files as uf1')
                ->select('uf1.*')
                ->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files WHERE darulifta_name IN (\'' . implode('\',\'', $daruliftaNamesArray) . '\') GROUP BY file_code) as uf2'), function($join) {
                    $join->on('uf1.id', '=', 'uf2.id');
                })
                ->whereIn('uf1.darulifta_name', $this->daruliftaNames)
                ->orderBy('uf1.id', 'asc')
                ->get();
        } else {
            $this->codebylower = collect(); // Empty collection when not needed
        }

        $this->mahlenazar_null = DB::table('uploaded_files as u1')
            ->whereIn('u1.darulifta_name', $this->daruliftaNames)
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.*')
            ->get();

        $this->remainingFatawa = $this->remainingFatawadata();
        $this->sendingFatawa = $this->sendingFatawadata();
// dd($this->remainingFatawa,$this->sendingFatawa);

        $messages = Chat::latest()->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        // Build query string for maintaining filters in delete actions
        $queryParams = [];
        if ($this->showDetail) $queryParams['showDetail'] = '1';
        if ($this->showQue) $queryParams['showQue'] = '1';
        if ($this->showChat) $queryParams['showChat'] = '1';
        if ($this->selectedmujeeb !== 'all') $queryParams['selectedmujeeb'] = $this->selectedmujeeb;
        if ($this->selectedmufti !== 'all') $queryParams['selectedmufti'] = $this->selectedmufti;
        if ($this->selectedchecked !== 'all_checked') $queryParams['selectedchecked'] = $this->selectedchecked;
        if ($this->selectedTimeFrame !== 'this_month') $queryParams['selectedTimeFrame'] = $this->selectedTimeFrame;
        if ($this->selectedfileCode !== 'all') $queryParams['selectedfileCode'] = $this->selectedfileCode;
        if (!empty($this->selectedMonths)) $queryParams['selectedMonths'] = implode(',', $this->selectedMonths);
        if ($this->startDate) $queryParams['startDate'] = $this->startDate;
        if ($this->endDate) $queryParams['endDate'] = $this->endDate;
        
        $queryString = http_build_query($queryParams);

        return view('livewire.sent-fatawa', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'checkers' => $this->checkers,
            'remainingFatawa' => $this->remainingFatawa,
            'sendingFatawa' => $this->sendingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmujeeb' => $this->selectedmujeeb,
            'selectedmufti' => $this->selectedmufti,
            'selectedchecked' => $this->selectedchecked,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'tempSelectedTimeFrame' => $this->tempSelectedTimeFrame,
            'selectedfileCode' => $this->selectedfileCode,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'showDetail' => $this->showDetail,
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'userName' => $this->userName,
            'checkerlist' => $this->checkerlist,
            'munsab' => $this->munsab,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'fileCode' => $this->fileCode,
            'isAdmin' => $this->roleInfo['isSuperAdmin'],
            'roleInfo' => $this->roleInfo,
            'queryString' => $queryString,

            // ... (other data to be passed to the view)
        ]

        )
        ->layout('layouts.app');

    }
    public function remainingFatawadata()
    {
        // Handle case where "Other" is selected but no months are selected
        if ($this->tempSelectedTimeFrame == 'other' && empty($this->selectedMonths)) {
            return [];
        }

        // Create cache key based on filters
        $cacheKey = 'remaining_fatawa_' . md5(serialize([
            $this->daruliftaNames,
            $this->mailfolderDate,
            $this->selectedmufti,
            $this->selectedchecked,
            $this->tempSelectedTimeFrame,
            $this->selectedMonths,
            $this->selectedfileCode,
            $this->userName,
            $this->roleInfo
        ]));

        return cache()->remember($cacheKey, 300, function() {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, $this->selectedMonths);

            $checkerFolderId = DB::table('checker')
                ->where('checker_name', $this->userName)
                ->value('folder_id');

            // Build single optimized query instead of nested loops
            $query = DB::table('uploaded_files')
                ->whereIn('darulifta_name', $this->daruliftaNames)
                ->whereIn('mail_folder_date', $this->mailfolderDate)
                ->where('selected', 1);

            // Apply role-based filtering
            $this->applyRoleBasedFiltering($query);

            // Apply mufti filter
            if ($this->selectedmufti && $this->selectedmufti != 'all') {
                if ($this->selectedmufti == 'mufti_ali_asghar') {
                    $query->where(function ($query) {
                        $query->where('checker', 'mufti_ali_asghar')
                              ->orWhereNull('checker');
                    });
                } elseif ($this->selectedmufti == 'transfer_checked') {
                    $query->where('transfer_by', $checkerFolderId);
                } else {
                    $query->where('checker', $this->selectedmufti);
                }
            }

            // Apply checked folder filter
            if ($this->selectedchecked == 'mahle_nazar') {
                $query->where('checked_folder', 'Mahl-e-Nazar');
            } elseif ($this->selectedchecked == 'ok_fatawa') {
                $query->where('checked_folder', 'ok');
            }

            // Apply time frame filters
            if ($this->tempSelectedTimeFrame == 'this_month') {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    now()->startOfMonth(),
                    now()->endOfMonth()
                ]);
            } elseif ($this->tempSelectedTimeFrame == 'last_month') {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    now()->subMonth()->startOfMonth(),
                    now()->subMonth()->endOfMonth()
                ]);
            } elseif ($this->tempSelectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
                $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
            } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    Carbon::parse($this->startDate)->format('Y-m-d'),
                    Carbon::parse($this->endDate)->format('Y-m-d')
                ]);
            }

            if ($this->selectedfileCode != 'all') {
                $query->where('file_code', $this->selectedfileCode);
            }

            // Get all results in single query and group them
            $results = $query->get();

            $remainingFatawas = [];
            foreach ($results as $result) {
                $remainingFatawas[$result->darulifta_name][$result->mail_folder_date][] = $result;
            }

            return $remainingFatawas;
        });
    }

    public function sendingFatawadata()
    {
        // Create cache key based on filters
        $cacheKey = 'sending_fatawa_' . md5(serialize([
            $this->daruliftaNames,
            $this->mailfolderDate,
            $this->checkers,
            $this->selectedchecked,
            $this->tempSelectedTimeFrame,
            $this->selectedMonths,
            $this->roleInfo
        ]));

        return cache()->remember($cacheKey, 300, function() {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, $this->selectedMonths);

            // Build single optimized query instead of triple nested loops
            $query = DB::table('uploaded_files')
                ->whereIn('darulifta_name', $this->daruliftaNames)
                ->whereIn('mail_folder_date', $this->mailfolderDate)
                ->where('selected', 1);

            // Apply checker filter using OR conditions
            if (!empty($this->checkers)) {
                $query->where(function($query) {
                    foreach ($this->checkers as $checked) {
                        $query->orWhere(function($subQuery) use ($checked) {
                            $subQuery->where('checker', $checked);
                            if ($checked === 'mufti_ali_asghar') {
                                $subQuery->orWhereNull('checker');
                            }
                        });
                    }
                });
            }

            // Apply checked folder filter
            if ($this->selectedchecked == 'mahle_nazar') {
                $query->where('checked_folder', 'Mahl-e-Nazar');
            } elseif ($this->selectedchecked == 'ok_fatawa') {
                $query->where('checked_folder', 'ok');
            }

            // Apply role-based filtering
            $this->applyRoleBasedFiltering($query);

            // Apply time frame filters
            if ($this->tempSelectedTimeFrame == 'this_month') {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    now()->startOfMonth(),
                    now()->endOfMonth()
                ]);
            } elseif ($this->tempSelectedTimeFrame == 'last_month') {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    now()->subMonth()->startOfMonth(),
                    now()->subMonth()->endOfMonth()
                ]);
            } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
                $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
            } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    Carbon::parse($this->startDate)->format('Y-m-d'),
                    Carbon::parse($this->endDate)->format('Y-m-d')
                ]);
            }

            // Get all results in single query and group them
            $results = $query->get();

            $sendingFatawas = [];
            foreach ($results as $result) {
                $checker = $result->checker ?: 'mufti_ali_asghar'; // Handle null checker
                $sendingFatawas[$result->darulifta_name][$checker][$result->mail_folder_date][] = $result;
            }

            // Reorder based on checkers order
            $orderedSendingFatawas = [];
            foreach ($sendingFatawas as $daruliftaName => $checkers) {
                $orderedCheckers = [];
                foreach ($this->checkers as $checker) {
                    if (isset($checkers[$checker])) {
                        $orderedCheckers[$checker] = $checkers[$checker];
                    }
                }
                $orderedSendingFatawas[$daruliftaName] = $orderedCheckers;
            }

            return $orderedSendingFatawas;
        });
    }

    /**
     * Apply role-based filtering to the query (same as RemainingFatawa)
     */
    private function applyRoleBasedFiltering($query)
    {
        if ($this->roleInfo['isMujeeb']) {
            $query->where('sender', $this->roleInfo['userName']);
        } elseif ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }
    }

}
